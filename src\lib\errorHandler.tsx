// Global Error Boundary for SynapseAI

import React, { Component, ReactNode, ErrorInfo as ReactErrorInfo } from 'react';
import { getLogger } from './logger';
import { getConfig } from './config';

export interface ErrorInfo {
  id: string;
  timestamp: string;
  message: string;
  stack?: string;
  component?: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'ui' | 'api' | 'auth' | 'routing' | 'system' | 'unknown';
  metadata?: Record<string, any>;
}

export interface ErrorHandlerConfig {
  enabled: boolean;
  reportToRemote: boolean;
  showUserNotification: boolean;
  maxRetries: number;
  retryDelay: number;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ReactErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private errorHandler: ErrorHandler;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
    this.errorHandler = getErrorHandler();
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ReactErrorInfo): void {
    this.setState({ errorInfo });

    this.errorHandler.handleError({
      message: error.message,
      stack: error.stack,
      component: 'ErrorBoundary',
      severity: 'critical',
      category: 'ui',
      metadata: {
        componentStack: errorInfo.componentStack,
      },
    });
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="text-center max-w-md">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h2>
            <p className="text-muted-foreground mb-4">
              An unexpected error occurred. Please refresh the page or try again later.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

class ErrorHandler {
  private config: ErrorHandlerConfig;
  private logger = getLogger().createChild('ErrorHandler');
  private errorQueue: ErrorInfo[] = [];
  private retryAttempts: Map<string, number> = new Map();

  constructor() {
    const appConfig = getConfig();

    this.config = {
      enabled: true,
      reportToRemote: appConfig.isProduction(),
      showUserNotification: !appConfig.isDevelopment(),
      maxRetries: 3,
      retryDelay: 1000,
    };

    this.setupGlobalErrorHandlers();
  }

  private setupGlobalErrorHandlers(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('error', (event) => {
      this.handleError({
        message: event.message,
        stack: event.error?.stack,
        component: 'Global',
        severity: 'high',
        category: 'system',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        component: 'Promise',
        severity: 'high',
        category: 'system',
        metadata: {
          reason: event.reason,
        },
      });
    });

    (window as any).__SYNAPSEAI_ERROR_HANDLER__ = this;
  }

  handleError(errorInfo: Partial<ErrorInfo>): string {
    if (!this.config.enabled) return '';

    const error: ErrorInfo = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      message: errorInfo.message || 'Unknown error',
      stack: errorInfo.stack,
      component: errorInfo.component || 'Unknown',
      severity: errorInfo.severity || 'medium',
      category: errorInfo.category || 'unknown',
      url: window?.location?.href,
      userAgent: navigator?.userAgent,
      metadata: errorInfo.metadata,
      ...errorInfo,
    };

    this.logger.error(`Error in ${error.component}: ${error.message}`, {
      errorId: error.id,
      stack: error.stack,
      metadata: error.metadata,
    });

    this.errorQueue.push(error);
    this.maintainErrorQueue();

    if (this.config.reportToRemote) {
      this.reportToRemote(error);
    }

    if (this.config.showUserNotification && error.severity === 'critical') {
      this.showUserNotification(error);
    }

    return error.id;
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private maintainErrorQueue(): void {
    if (this.errorQueue.length > 100) {
      this.errorQueue = this.errorQueue.slice(-100);
    }
  }

  private async reportToRemote(error: ErrorInfo): Promise<void> {
    try {
      const config = getConfig();
      const endpoint = `${config.getApiBaseUrl()}/errors`;

      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      });
    } catch (reportError) {
      this.logger.warn('Failed to report error to remote service', {
        originalError: error.id,
        reportError: reportError instanceof Error ? reportError.message : 'Unknown',
      });
    }
  }

  private showUserNotification(error: ErrorInfo): void {
    if (typeof window !== 'undefined' && window.confirm) {
      const message = `An error occurred: ${error.message}\n\nError ID: ${error.id}\n\nWould you like to report this issue?`;
      if (window.confirm(message)) {
        this.reportUserFeedback(error.id);
      }
    }
  }

  private reportUserFeedback(errorId: string): void {
    const error = this.errorQueue.find(e => e.id === errorId);
    if (error) {
      this.logger.info('User reported error', { errorId, userReported: true });
    }
  }

  async withRetry<T>(
    operation: () => Promise<T>,
    context: string,
    maxRetries: number = this.config.maxRetries
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        this.logger.warn(`Attempt ${attempt}/${maxRetries} failed for ${context}`, {
          error: lastError.message,
          attempt,
        });

        if (attempt < maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }

    this.handleError({
      message: `Operation failed after ${maxRetries} attempts: ${lastError!.message}`,
      stack: lastError!.stack,
      component: context,
      severity: 'high',
      category: 'system',
      metadata: {
        maxRetries,
        finalError: lastError!.message,
      },
    });

    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: ErrorInfo[];
  } {
    const errorsByCategory: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors: this.errorQueue.slice(-10),
    };
  }

  getError(errorId: string): ErrorInfo | undefined {
    return this.errorQueue.find(error => error.id === errorId);
  }

  clearErrors(): void {
    this.errorQueue = [];
    this.retryAttempts.clear();
  }

  updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

let errorHandler: ErrorHandler | null = null;

export function getErrorHandler(): ErrorHandler {
  if (!errorHandler) {
    errorHandler = new ErrorHandler();
  }
  return errorHandler;
}

// Legacy alias for backward compatibility
export const getErrorBoundary = getErrorHandler;

export async function safeAsync<T>(
  operation: () => Promise<T>,
  context: string,
  fallback?: T
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    getErrorHandler().handleError({
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      component: context,
      severity: 'medium',
      category: 'system',
    });
    return fallback;
  }
}
