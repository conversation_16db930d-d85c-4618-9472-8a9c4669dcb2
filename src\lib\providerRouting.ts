// Provider Routing Logic for SynapseAI

import { getSynapseAI } from "./sdk";

export interface RoutingContext {
  requestType: "text" | "image" | "code" | "analysis" | "creative";
  complexity: "low" | "medium" | "high";
  priority: "low" | "normal" | "high" | "urgent";
  maxLatency?: number; // in milliseconds
  maxCost?: number; // in dollars
  preferredProviders?: string[];
  excludedProviders?: string[];
  requiresStreaming?: boolean;
  language?: string;
  domain?: string;
}

export interface ProviderCapability {
  providerId: string;
  name: string;
  type: string;
  models: string[];
  capabilities: {
    text: boolean;
    image: boolean;
    code: boolean;
    analysis: boolean;
    creative: boolean;
    streaming: boolean;
    multimodal: boolean;
  };
  performance: {
    averageLatency: number; // in milliseconds
    successRate: number; // percentage
    costPerToken: number; // in dollars
    maxTokens: number;
  };
  limits: {
    requestsPerMinute: number;
    requestsPerDay: number;
    currentUsage: number;
  };
  status: "active" | "inactive" | "error" | "maintenance";
}

export interface RoutingDecision {
  selectedProvider: string;
  selectedModel: string;
  confidence: number; // 0-1
  reasoning: string[];
  fallbackProviders: string[];
  estimatedCost: number;
  estimatedLatency: number;
}

export interface RoutingRule {
  id: string;
  name: string;
  condition: (
    context: RoutingContext,
    providers: ProviderCapability[]
  ) => boolean;
  priority: number;
  action: (
    context: RoutingContext,
    providers: ProviderCapability[]
  ) => ProviderCapability[];
}

class ProviderRouter {
  private providers: Map<string, ProviderCapability> = new Map();
  private routingRules: RoutingRule[] = [];
  private routingHistory: Array<{
    timestamp: string;
    context: RoutingContext;
    decision: RoutingDecision;
    success: boolean;
  }> = [];

  constructor() {
    this.initializeDefaultProviders();
    this.initializeDefaultRules();
  }

  private initializeDefaultProviders(): void {
    const defaultProviders: ProviderCapability[] = [
      {
        providerId: "openai-1",
        name: "OpenAI",
        type: "openai",
        models: ["gpt-4", "gpt-3.5-turbo", "gpt-4-vision"],
        capabilities: {
          text: true,
          image: true,
          code: true,
          analysis: true,
          creative: true,
          streaming: true,
          multimodal: true,
        },
        performance: {
          averageLatency: 800,
          successRate: 98.5,
          costPerToken: 0.00003,
          maxTokens: 128000,
        },
        limits: {
          requestsPerMinute: 60,
          requestsPerDay: 10000,
          currentUsage: 1250,
        },
        status: "active",
      },
      {
        providerId: "claude-1",
        name: "Claude",
        type: "anthropic",
        models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        capabilities: {
          text: true,
          image: true,
          code: true,
          analysis: true,
          creative: true,
          streaming: true,
          multimodal: true,
        },
        performance: {
          averageLatency: 1200,
          successRate: 97.2,
          costPerToken: 0.000015,
          maxTokens: 200000,
        },
        limits: {
          requestsPerMinute: 50,
          requestsPerDay: 8000,
          currentUsage: 850,
        },
        status: "active",
      },
      {
        providerId: "gemini-1",
        name: "Gemini",
        type: "google",
        models: ["gemini-pro", "gemini-ultra", "gemini-pro-vision"],
        capabilities: {
          text: true,
          image: true,
          code: true,
          analysis: true,
          creative: true,
          streaming: false,
          multimodal: true,
        },
        performance: {
          averageLatency: 1500,
          successRate: 95.8,
          costPerToken: 0.0000125,
          maxTokens: 32000,
        },
        limits: {
          requestsPerMinute: 40,
          requestsPerDay: 5000,
          currentUsage: 320,
        },
        status: "active",
      },
      {
        providerId: "groq-1",
        name: "Groq",
        type: "groq",
        models: ["llama-3-70b", "llama-3-8b", "mixtral-8x7b"],
        capabilities: {
          text: true,
          image: false,
          code: true,
          analysis: true,
          creative: true,
          streaming: true,
          multimodal: false,
        },
        performance: {
          averageLatency: 300,
          successRate: 96.5,
          costPerToken: 0.000001,
          maxTokens: 8000,
        },
        limits: {
          requestsPerMinute: 100,
          requestsPerDay: 15000,
          currentUsage: 2100,
        },
        status: "active",
      },
    ];

    defaultProviders.forEach((provider) => {
      this.providers.set(provider.providerId, provider);
    });
  }

  private initializeDefaultRules(): void {
    this.routingRules = [
      {
        id: "urgent-priority",
        name: "Urgent Priority Routing",
        priority: 1,
        condition: (context) => context.priority === "urgent",
        action: (context, providers) => {
          return providers
            .filter((p) => p.status === "active")
            .sort(
              (a, b) =>
                a.performance.averageLatency - b.performance.averageLatency
            );
        },
      },
      {
        id: "low-cost-preference",
        name: "Cost Optimization",
        priority: 2,
        condition: (context) =>
          context.maxCost !== undefined && context.maxCost < 0.01,
        action: (context, providers) => {
          return providers
            .filter((p) => p.status === "active")
            .sort(
              (a, b) => a.performance.costPerToken - b.performance.costPerToken
            );
        },
      },
      {
        id: "streaming-required",
        name: "Streaming Support",
        priority: 3,
        condition: (context) => context.requiresStreaming === true,
        action: (context, providers) => {
          return providers.filter(
            (p) => p.capabilities.streaming && p.status === "active"
          );
        },
      },
      {
        id: "image-processing",
        name: "Image Processing",
        priority: 4,
        condition: (context) => context.requestType === "image",
        action: (context, providers) => {
          return providers.filter(
            (p) => p.capabilities.image && p.status === "active"
          );
        },
      },
      {
        id: "code-generation",
        name: "Code Generation",
        priority: 5,
        condition: (context) => context.requestType === "code",
        action: (context, providers) => {
          return providers
            .filter((p) => p.capabilities.code && p.status === "active")
            .sort(
              (a, b) => b.performance.successRate - a.performance.successRate
            );
        },
      },
      {
        id: "high-complexity",
        name: "High Complexity Tasks",
        priority: 6,
        condition: (context) => context.complexity === "high",
        action: (context, providers) => {
          return providers
            .filter(
              (p) => p.status === "active" && p.performance.maxTokens > 50000
            )
            .sort(
              (a, b) => b.performance.successRate - a.performance.successRate
            );
        },
      },
      {
        id: "rate-limit-check",
        name: "Rate Limit Avoidance",
        priority: 7,
        condition: () => true,
        action: (context, providers) => {
          return providers.filter((p) => {
            const usageRatio = p.limits.currentUsage / p.limits.requestsPerDay;
            return p.status === "active" && usageRatio < 0.9;
          });
        },
      },
    ];
  }

  async routeRequest(context: RoutingContext): Promise<RoutingDecision> {
    const startTime = Date.now();
    const reasoning: string[] = [];

    try {
      // Get available providers
      let availableProviders = Array.from(this.providers.values());
      reasoning.push(
        `Starting with ${availableProviders.length} available providers`
      );

      // Apply user preferences
      if (context.preferredProviders?.length) {
        availableProviders = availableProviders.filter((p) =>
          context.preferredProviders!.includes(p.providerId)
        );
        reasoning.push(
          `Filtered to preferred providers: ${availableProviders.length} remaining`
        );
      }

      if (context.excludedProviders?.length) {
        availableProviders = availableProviders.filter(
          (p) => !context.excludedProviders!.includes(p.providerId)
        );
        reasoning.push(
          `Excluded providers: ${availableProviders.length} remaining`
        );
      }

      // Apply routing rules
      const applicableRules = this.routingRules
        .filter((rule) => rule.condition(context, availableProviders))
        .sort((a, b) => a.priority - b.priority);

      reasoning.push(`Applied ${applicableRules.length} routing rules`);

      for (const rule of applicableRules) {
        const beforeCount = availableProviders.length;
        availableProviders = rule.action(context, availableProviders);
        reasoning.push(
          `Rule '${rule.name}': ${beforeCount} → ${availableProviders.length} providers`
        );
      }

      if (availableProviders.length === 0) {
        throw new Error(
          "No suitable providers available after applying routing rules"
        );
      }

      // Select the best provider
      const selectedProvider = this.selectBestProvider(
        context,
        availableProviders
      );
      const selectedModel = this.selectBestModel(context, selectedProvider);

      // Calculate estimates
      const estimatedCost = this.estimateCost(context, selectedProvider);
      const estimatedLatency = selectedProvider.performance.averageLatency;

      // Prepare fallback providers
      const fallbackProviders = availableProviders
        .filter((p) => p.providerId !== selectedProvider.providerId)
        .slice(0, 2)
        .map((p) => p.providerId);

      const decision: RoutingDecision = {
        selectedProvider: selectedProvider.providerId,
        selectedModel,
        confidence: this.calculateConfidence(
          context,
          selectedProvider,
          availableProviders
        ),
        reasoning,
        fallbackProviders,
        estimatedCost,
        estimatedLatency,
      };

      // Record routing decision
      this.recordRoutingDecision(context, decision, true);

      // Emit routing event
      try {
        const sdk = getSynapseAI();
        // Emit the routing decision event instead of subscribing
        if (sdk && typeof sdk.emitEvent === "function") {
          sdk.emitEvent("routing_decision", {
            decision: decision,
            context,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        // SDK not initialized, skip event
        console.debug("SDK not available for routing event emission:", error);
      }

      return decision;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown routing error";
      reasoning.push(`Error: ${errorMessage}`);

      // Fallback to first available provider
      const fallbackProvider = Array.from(this.providers.values()).find(
        (p) => p.status === "active"
      );

      if (!fallbackProvider) {
        throw new Error("No providers available for fallback routing");
      }

      const fallbackDecision: RoutingDecision = {
        selectedProvider: fallbackProvider.providerId,
        selectedModel: fallbackProvider.models[0],
        confidence: 0.1,
        reasoning: [
          ...reasoning,
          "Using fallback provider due to routing error",
        ],
        fallbackProviders: [],
        estimatedCost: 0.01,
        estimatedLatency: fallbackProvider.performance.averageLatency,
      };

      this.recordRoutingDecision(context, fallbackDecision, false);
      return fallbackDecision;
    }
  }

  private selectBestProvider(
    context: RoutingContext,
    providers: ProviderCapability[]
  ): ProviderCapability {
    // Score providers based on context
    const scoredProviders = providers.map((provider) => {
      let score = 0;

      // Performance score (40%)
      score += (provider.performance.successRate / 100) * 0.4;

      // Latency score (30%)
      const maxLatency = Math.max(
        ...providers.map((p) => p.performance.averageLatency)
      );
      score += (1 - provider.performance.averageLatency / maxLatency) * 0.3;

      // Cost score (20%)
      const maxCost = Math.max(
        ...providers.map((p) => p.performance.costPerToken)
      );
      score += (1 - provider.performance.costPerToken / maxCost) * 0.2;

      // Availability score (10%)
      const usageRatio =
        provider.limits.currentUsage / provider.limits.requestsPerDay;
      score += (1 - usageRatio) * 0.1;

      return { provider, score };
    });

    // Return the highest scoring provider
    return scoredProviders.sort((a, b) => b.score - a.score)[0].provider;
  }

  private selectBestModel(
    context: RoutingContext,
    provider: ProviderCapability
  ): string {
    // Simple model selection based on complexity
    const models = provider.models;

    if (context.complexity === "high" && models.length > 1) {
      return models[0]; // Assume first model is the most capable
    }

    if (context.complexity === "low" && models.length > 2) {
      return models[models.length - 1]; // Assume last model is the most efficient
    }

    return models[Math.floor(models.length / 2)] || models[0];
  }

  private estimateCost(
    context: RoutingContext,
    provider: ProviderCapability
  ): number {
    // Rough cost estimation based on average token usage
    const estimatedTokens =
      {
        low: 500,
        medium: 1500,
        high: 5000,
      }[context.complexity] || 1000;

    return estimatedTokens * provider.performance.costPerToken;
  }

  private calculateConfidence(
    context: RoutingContext,
    selectedProvider: ProviderCapability,
    availableProviders: ProviderCapability[]
  ): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence based on provider success rate
    confidence += (selectedProvider.performance.successRate - 90) / 100;

    // Increase confidence if provider matches request type capabilities
    if (selectedProvider.capabilities[context.requestType]) {
      confidence += 0.2;
    }

    // Decrease confidence if many providers were available (more uncertainty)
    confidence -= Math.min(availableProviders.length * 0.05, 0.3);

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  private recordRoutingDecision(
    context: RoutingContext,
    decision: RoutingDecision,
    success: boolean
  ): void {
    this.routingHistory.push({
      timestamp: new Date().toISOString(),
      context,
      decision,
      success,
    });

    // Keep only last 1000 routing decisions
    if (this.routingHistory.length > 1000) {
      this.routingHistory = this.routingHistory.slice(-1000);
    }
  }

  // Public methods for provider management
  updateProvider(provider: ProviderCapability): void {
    this.providers.set(provider.providerId, provider);
  }

  removeProvider(providerId: string): boolean {
    return this.providers.delete(providerId);
  }

  getProviders(): ProviderCapability[] {
    return Array.from(this.providers.values());
  }

  getProvider(providerId: string): ProviderCapability | undefined {
    return this.providers.get(providerId);
  }

  getRoutingHistory(): typeof this.routingHistory {
    return [...this.routingHistory];
  }

  getRoutingStats(): {
    totalRequests: number;
    successRate: number;
    averageConfidence: number;
    providerUsage: Record<string, number>;
  } {
    const total = this.routingHistory.length;
    const successful = this.routingHistory.filter((h) => h.success).length;
    const avgConfidence =
      this.routingHistory.reduce((sum, h) => sum + h.decision.confidence, 0) /
      total;

    const providerUsage: Record<string, number> = {};
    this.routingHistory.forEach((h) => {
      const provider = h.decision.selectedProvider;
      providerUsage[provider] = (providerUsage[provider] || 0) + 1;
    });

    return {
      totalRequests: total,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageConfidence: total > 0 ? avgConfidence : 0,
      providerUsage,
    };
  }
}

// Global router instance
let providerRouter: ProviderRouter | null = null;

export function getProviderRouter(): ProviderRouter {
  if (!providerRouter) {
    providerRouter = new ProviderRouter();
  }
  return providerRouter;
}

export { ProviderRouter };
