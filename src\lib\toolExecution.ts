// Tool Execution Framework for SynapseAI

import { getLogger } from "./logger";
import { getError<PERSON>and<PERSON> } from "./errorHandler";
import { getAPXClient } from "./websocket";
import { getConfig } from "./config";

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: "object";
    properties: Record<
      string,
      {
        type: string;
        description: string;
        required?: boolean;
        enum?: string[];
        default?: any;
      }
    >;
    required: string[];
  };
  handler: ToolHandler;
  category: "internal" | "external" | "hybrid";
  timeout: number;
  retryPolicy: {
    maxAttempts: number;
    backoffMs: number;
    retryableErrors: string[];
  };
  fallback?: ToolFallback;
  permissions: string[];
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
}

export interface ToolHandler {
  execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolResult>;
}

export interface ToolExecutionContext {
  sessionId?: string;
  userId?: string;
  agentId?: string;
  requestId: string;
  timestamp: string;
  metadata: Record<string, any>;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
  executionTime: number;
}

export interface ToolFallback {
  toolName: string;
  parameterMapping?: Record<string, string>;
  condition?: (error: Error, context: ToolExecutionContext) => boolean;
}

export interface ToolExecution {
  id: string;
  toolName: string;
  parameters: Record<string, any>;
  context: ToolExecutionContext;
  status: "pending" | "running" | "completed" | "failed" | "timeout";
  result?: ToolResult;
  startTime: string;
  endTime?: string;
  attempts: number;
  errors: string[];
}

class ToolExecutionFramework {
  private tools: Map<string, ToolDefinition> = new Map();
  private executions: Map<string, ToolExecution> = new Map();
  private rateLimiters: Map<
    string,
    { requests: number[]; limit: number; windowMs: number }
  > = new Map();
  private logger = getLogger().createChild("ToolExecution");
  private errorHandler = getErrorHandler();
  private apxClient = getAPXClient();

  constructor() {
    this.registerBuiltInTools();
    this.setupEventHandlers();
  }

  private registerBuiltInTools(): void {
    // Web Search Tool
    this.registerTool({
      name: "web_search",
      description: "Search the web for information",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "Search query",
          },
          limit: {
            type: "number",
            description: "Maximum number of results",
            default: 10,
          },
        },
        required: ["query"],
      },
      handler: new WebSearchHandler(),
      category: "external",
      timeout: 10000,
      retryPolicy: {
        maxAttempts: 3,
        backoffMs: 1000,
        retryableErrors: ["timeout", "network_error"],
      },
      permissions: ["web_access"],
      rateLimit: {
        requests: 100,
        windowMs: 60000,
      },
    });

    // Database Query Tool
    this.registerTool({
      name: "database_query",
      description: "Execute database queries",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "SQL query to execute",
          },
          parameters: {
            type: "array",
            description: "Query parameters",
          },
        },
        required: ["query"],
      },
      handler: new DatabaseQueryHandler(),
      category: "internal",
      timeout: 30000,
      retryPolicy: {
        maxAttempts: 2,
        backoffMs: 2000,
        retryableErrors: ["connection_error"],
      },
      permissions: ["database_access"],
    });

    // API Call Tool
    this.registerTool({
      name: "api_call",
      description: "Make HTTP API calls",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "API endpoint URL",
          },
          method: {
            type: "string",
            description: "HTTP method",
            enum: ["GET", "POST", "PUT", "DELETE", "PATCH"],
            default: "GET",
          },
          headers: {
            type: "object",
            description: "HTTP headers",
          },
          body: {
            type: "object",
            description: "Request body",
          },
        },
        required: ["url"],
      },
      handler: new APICallHandler(),
      category: "external",
      timeout: 15000,
      retryPolicy: {
        maxAttempts: 3,
        backoffMs: 1500,
        retryableErrors: ["timeout", "5xx"],
      },
      permissions: ["api_access"],
      rateLimit: {
        requests: 200,
        windowMs: 60000,
      },
    });

    // File Operations Tool
    this.registerTool({
      name: "file_operations",
      description: "Perform file operations",
      parameters: {
        type: "object",
        properties: {
          operation: {
            type: "string",
            description: "File operation to perform",
            enum: ["read", "write", "delete", "list"],
          },
          path: {
            type: "string",
            description: "File or directory path",
          },
          content: {
            type: "string",
            description: "Content to write (for write operation)",
          },
        },
        required: ["operation", "path"],
      },
      handler: new FileOperationsHandler(),
      category: "internal",
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 2,
        backoffMs: 1000,
        retryableErrors: ["permission_error"],
      },
      permissions: ["file_access"],
    });
  }

  private setupEventHandlers(): void {
    // Listen for tool execution requests from APIX
    this.apxClient.subscribe("tool_call_start", (message) => {
      const { callId, toolName, parameters } = message.data;

      this.executeTool(toolName, parameters, {
        sessionId: message.sessionId,
        userId: message.userId,
        agentId: message.agentId,
        requestId: callId,
        timestamp: message.timestamp,
        metadata: {},
      })
        .then((result) => {
          this.apxClient.send({
            id: this.generateId(),
            type: "tool_call_response",
            timestamp: new Date().toISOString(),
            data: {
              callId,
              success: result.success,
              result: result.data,
              error: result.error,
              executionTime: result.executionTime,
            },
          });
        })
        .catch((error) => {
          this.apxClient.send({
            id: this.generateId(),
            type: "tool_call_response",
            timestamp: new Date().toISOString(),
            data: {
              callId,
              success: false,
              error: error.message,
              executionTime: 0,
            },
          });
        });
    });
  }

  registerTool(definition: ToolDefinition): void {
    this.tools.set(definition.name, definition);
    this.logger.info(`Registered tool: ${definition.name}`, {
      category: definition.category,
      permissions: definition.permissions,
    });
  }

  unregisterTool(name: string): boolean {
    const removed = this.tools.delete(name);
    if (removed) {
      this.logger.info(`Unregistered tool: ${name}`);
    }
    return removed;
  }

  async executeTool(
    toolName: string,
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found`);
    }

    // Check rate limits
    if (tool.rateLimit && !this.checkRateLimit(toolName, tool.rateLimit)) {
      throw new Error(`Rate limit exceeded for tool '${toolName}'`);
    }

    // Validate parameters
    this.validateParameters(parameters, tool.parameters);

    const executionId = this.generateId();
    const execution: ToolExecution = {
      id: executionId,
      toolName,
      parameters,
      context,
      status: "pending",
      startTime: new Date().toISOString(),
      attempts: 0,
      errors: [],
    };

    this.executions.set(executionId, execution);

    try {
      const result = await this.executeWithRetry(tool, execution);

      execution.status = result.success ? "completed" : "failed";
      execution.result = result;
      execution.endTime = new Date().toISOString();

      this.logger.info(
        `Tool execution ${result.success ? "completed" : "failed"}`,
        {
          toolName,
          executionId,
          executionTime: result.executionTime,
          attempts: execution.attempts,
        }
      );

      return result;
    } catch (error) {
      execution.status = "failed";
      execution.endTime = new Date().toISOString();
      execution.errors.push(
        error instanceof Error ? error.message : String(error)
      );

      this.errorHandler.handleError({
        message: `Tool execution failed: ${toolName}`,
        stack: error instanceof Error ? error.stack : undefined,
        component: "ToolExecution",
        severity: "medium",
        category: "system",
        metadata: { toolName, executionId, parameters },
      });

      throw error;
    }
  }

  private async executeWithRetry(
    tool: ToolDefinition,
    execution: ToolExecution
  ): Promise<ToolResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= tool.retryPolicy.maxAttempts; attempt++) {
      execution.attempts = attempt;
      execution.status = "running";

      try {
        const startTime = Date.now();

        // Execute with timeout
        const result = await Promise.race([
          tool.handler.execute(execution.parameters, execution.context),
          new Promise<never>((_, reject) => {
            setTimeout(
              () => reject(new Error("Tool execution timeout")),
              tool.timeout
            );
          }),
        ]);

        result.executionTime = Date.now() - startTime;
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        execution.errors.push(lastError.message);

        this.logger.warn(`Tool execution attempt ${attempt} failed`, {
          toolName: tool.name,
          executionId: execution.id,
          error: lastError.message,
          attempt,
        });

        // Check if error is retryable
        const isRetryable = tool.retryPolicy.retryableErrors.some(
          (retryableError) =>
            lastError!.message
              .toLowerCase()
              .includes(retryableError.toLowerCase())
        );

        if (!isRetryable || attempt === tool.retryPolicy.maxAttempts) {
          break;
        }

        // Wait before retry
        await new Promise((resolve) =>
          setTimeout(resolve, tool.retryPolicy.backoffMs * attempt)
        );
      }
    }

    // Try fallback if available
    if (tool.fallback && lastError) {
      if (
        !tool.fallback.condition ||
        tool.fallback.condition(lastError, execution.context)
      ) {
        this.logger.info(`Attempting fallback for tool: ${tool.name}`, {
          fallbackTool: tool.fallback.toolName,
        });

        try {
          const fallbackParameters = this.mapParameters(
            execution.parameters,
            tool.fallback.parameterMapping || {}
          );

          return await this.executeTool(
            tool.fallback.toolName,
            fallbackParameters,
            execution.context
          );
        } catch (fallbackError) {
          this.logger.error(`Fallback execution failed`, {
            originalTool: tool.name,
            fallbackTool: tool.fallback.toolName,
            error:
              fallbackError instanceof Error
                ? fallbackError.message
                : String(fallbackError),
          });
        }
      }
    }

    throw lastError || new Error("Tool execution failed");
  }

  private validateParameters(
    parameters: Record<string, any>,
    schema: ToolDefinition["parameters"]
  ): void {
    // Check required parameters
    for (const required of schema.required) {
      if (!(required in parameters)) {
        throw new Error(`Missing required parameter: ${required}`);
      }
    }

    // Validate parameter types and constraints
    for (const [key, value] of Object.entries(parameters)) {
      const property = schema.properties[key];
      if (!property) {
        continue; // Allow extra parameters
      }

      // Type validation
      if (property.type === "string" && typeof value !== "string") {
        throw new Error(`Parameter '${key}' must be a string`);
      }
      if (property.type === "number" && typeof value !== "number") {
        throw new Error(`Parameter '${key}' must be a number`);
      }
      if (property.type === "boolean" && typeof value !== "boolean") {
        throw new Error(`Parameter '${key}' must be a boolean`);
      }
      if (property.type === "array" && !Array.isArray(value)) {
        throw new Error(`Parameter '${key}' must be an array`);
      }
      if (
        property.type === "object" &&
        (typeof value !== "object" || value === null)
      ) {
        throw new Error(`Parameter '${key}' must be an object`);
      }

      // Enum validation
      if (property.enum && !property.enum.includes(value)) {
        throw new Error(
          `Parameter '${key}' must be one of: ${property.enum.join(", ")}`
        );
      }
    }
  }

  private checkRateLimit(
    toolName: string,
    rateLimit: { requests: number; windowMs: number }
  ): boolean {
    const now = Date.now();
    const limiter = this.rateLimiters.get(toolName) || {
      requests: [],
      limit: rateLimit.requests,
      windowMs: rateLimit.windowMs,
    };

    // Remove old requests outside the window
    limiter.requests = limiter.requests.filter(
      (timestamp) => now - timestamp < limiter.windowMs
    );

    // Check if we're within the limit
    if (limiter.requests.length >= limiter.limit) {
      return false;
    }

    // Add current request
    limiter.requests.push(now);
    this.rateLimiters.set(toolName, limiter);

    return true;
  }

  private mapParameters(
    original: Record<string, any>,
    mapping: Record<string, string>
  ): Record<string, any> {
    const mapped: Record<string, any> = { ...original };

    for (const [from, to] of Object.entries(mapping)) {
      if (from in original) {
        mapped[to] = original[from];
        delete mapped[from];
      }
    }

    return mapped;
  }

  private generateId(): string {
    return `tool_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  // Public API
  getTools(): ToolDefinition[] {
    return Array.from(this.tools.values());
  }

  getTool(name: string): ToolDefinition | undefined {
    return this.tools.get(name);
  }

  getExecution(id: string): ToolExecution | undefined {
    return this.executions.get(id);
  }

  getExecutions(toolName?: string): ToolExecution[] {
    const executions = Array.from(this.executions.values());
    return toolName
      ? executions.filter((e) => e.toolName === toolName)
      : executions;
  }

  getExecutionStats(): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    toolUsage: Record<string, number>;
  } {
    const executions = Array.from(this.executions.values());
    const successful = executions.filter((e) => e.status === "completed");
    const failed = executions.filter((e) => e.status === "failed");

    const avgTime =
      successful.reduce((sum, e) => {
        return sum + (e.result?.executionTime || 0);
      }, 0) / successful.length || 0;

    const toolUsage: Record<string, number> = {};
    executions.forEach((e) => {
      toolUsage[e.toolName] = (toolUsage[e.toolName] || 0) + 1;
    });

    return {
      totalExecutions: executions.length,
      successfulExecutions: successful.length,
      failedExecutions: failed.length,
      averageExecutionTime: avgTime,
      toolUsage,
    };
  }
}

// Built-in Tool Handlers
class WebSearchHandler implements ToolHandler {
  async execute(parameters: Record<string, any>): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate required parameters
      if (!parameters.query || typeof parameters.query !== "string") {
        throw new Error("Query parameter is required and must be a string");
      }

      const limit = Math.min(parameters.limit || 10, 50); // Cap at 50 results

      // In production, integrate with search APIs like Google Custom Search, Bing, etc.
      // For now, simulate realistic search results
      const searchTerms = parameters.query.toLowerCase().split(" ");
      const mockResults = [
        {
          title: `${parameters.query} - Official Documentation`,
          url: `https://docs.example.com/${searchTerms[0]}`,
          snippet: `Official documentation and guides for ${parameters.query}. Learn about features, API reference, and best practices.`,
          relevanceScore: 0.95,
        },
        {
          title: `${parameters.query} Tutorial - Getting Started`,
          url: `https://tutorial.example.com/${searchTerms[0]}-guide`,
          snippet: `Step-by-step tutorial for ${parameters.query}. Perfect for beginners and advanced users alike.`,
          relevanceScore: 0.88,
        },
        {
          title: `${parameters.query} Community Forum`,
          url: `https://community.example.com/topics/${searchTerms[0]}`,
          snippet: `Join the discussion about ${parameters.query}. Get help from experts and share your experiences.`,
          relevanceScore: 0.75,
        },
      ].slice(0, limit);

      return {
        success: true,
        data: {
          results: mockResults,
          query: parameters.query,
          totalResults: mockResults.length,
          searchTime: Date.now() - startTime,
        },
        executionTime: Date.now() - startTime,
        metadata: {
          searchEngine: "mock",
          resultsCount: mockResults.length,
          queryProcessed: parameters.query,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Web search failed",
        executionTime: Date.now() - startTime,
        metadata: {
          searchEngine: "mock",
          queryAttempted: parameters.query,
        },
      };
    }
  }
}

class DatabaseQueryHandler implements ToolHandler {
  async execute(parameters: Record<string, any>): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate SQL query
      if (!parameters.query || typeof parameters.query !== "string") {
        throw new Error("SQL query parameter is required");
      }

      const query = parameters.query.trim().toLowerCase();

      // Basic SQL injection protection (in production, use proper parameterized queries)
      const dangerousKeywords = [
        "drop",
        "delete",
        "truncate",
        "alter",
        "create",
        "insert",
        "update",
      ];
      const isDangerous = dangerousKeywords.some(
        (keyword) => query.includes(keyword) && !query.startsWith("select")
      );

      if (isDangerous) {
        throw new Error("Only SELECT queries are allowed for security reasons");
      }

      // Mock database results based on query type
      let mockResult;
      if (query.includes("users")) {
        mockResult = {
          rows: [
            {
              id: 1,
              name: "John Doe",
              email: "<EMAIL>",
              created_at: new Date().toISOString(),
            },
            {
              id: 2,
              name: "Jane Smith",
              email: "<EMAIL>",
              created_at: new Date().toISOString(),
            },
          ],
          rowCount: 2,
          fields: ["id", "name", "email", "created_at"],
        };
      } else if (query.includes("orders")) {
        mockResult = {
          rows: [
            { id: 101, user_id: 1, amount: 99.99, status: "completed" },
            { id: 102, user_id: 2, amount: 149.5, status: "pending" },
          ],
          rowCount: 2,
          fields: ["id", "user_id", "amount", "status"],
        };
      } else {
        mockResult = {
          rows: [
            {
              result: "Query executed successfully",
              timestamp: new Date().toISOString(),
            },
          ],
          rowCount: 1,
          fields: ["result", "timestamp"],
        };
      }

      return {
        success: true,
        data: mockResult,
        executionTime: Date.now() - startTime,
        metadata: {
          queryType: "SELECT",
          database: "mock_db",
          affectedRows: mockResult.rowCount,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Database query failed",
        executionTime: Date.now() - startTime,
        metadata: {
          queryAttempted: parameters.query,
          database: "mock_db",
        },
      };
    }
  }
}

class APICallHandler implements ToolHandler {
  async execute(parameters: Record<string, any>): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate URL
      if (!parameters.url || typeof parameters.url !== "string") {
        throw new Error("URL parameter is required");
      }

      let url: URL;
      try {
        url = new URL(parameters.url);
      } catch {
        throw new Error("Invalid URL format");
      }

      // Security check - only allow HTTPS in production
      const config = getConfig();
      if (config.isProduction() && url.protocol !== "https:") {
        throw new Error("Only HTTPS URLs are allowed in production");
      }

      // Rate limiting check
      const method = (parameters.method || "GET").toUpperCase();
      const headers = {
        "User-Agent": "SynapseAI-ToolExecution/1.0",
        Accept: "application/json",
        ...parameters.headers,
      };

      const requestOptions: RequestInit = {
        method,
        headers,
        signal: AbortSignal.timeout(30000), // 30 second timeout
      };

      if (parameters.body && ["POST", "PUT", "PATCH"].includes(method)) {
        requestOptions.body =
          typeof parameters.body === "string"
            ? parameters.body
            : JSON.stringify(parameters.body);

        if (!headers["Content-Type"]) {
          headers["Content-Type"] = "application/json";
        }
      }

      const response = await fetch(parameters.url, requestOptions);
      const responseTime = Date.now() - startTime;

      let responseData;
      const contentType = response.headers.get("content-type");

      if (contentType?.includes("application/json")) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      return {
        success: response.ok,
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          data: responseData,
          responseTime,
        },
        error: response.ok
          ? undefined
          : `HTTP ${response.status}: ${response.statusText}`,
        executionTime: Date.now() - startTime,
        metadata: {
          url: parameters.url,
          method,
          statusCode: response.status,
          contentType: contentType || "unknown",
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "API call failed",
        executionTime: Date.now() - startTime,
        metadata: {
          url: parameters.url,
          method: parameters.method || "GET",
        },
      };
    }
  }
}

class FileOperationsHandler implements ToolHandler {
  async execute(parameters: Record<string, any>): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate parameters
      if (!parameters.operation || !parameters.path) {
        throw new Error("Operation and path parameters are required");
      }

      const operation = parameters.operation.toLowerCase();
      const allowedOperations = [
        "read",
        "write",
        "delete",
        "list",
        "exists",
        "stat",
      ];

      if (!allowedOperations.includes(operation)) {
        throw new Error(
          `Unsupported operation: ${operation}. Allowed: ${allowedOperations.join(", ")}`
        );
      }

      // Security check - prevent path traversal
      const path = parameters.path;
      if (path.includes("..") || path.includes("~") || path.startsWith("/")) {
        throw new Error(
          "Path traversal detected. Only relative paths within working directory are allowed."
        );
      }

      // Mock file operations - in production, implement actual file system operations with proper security
      let result;

      switch (operation) {
        case "read":
          result = {
            operation: "read",
            path,
            content: `Mock file content for ${path}`,
            size: 1024,
            encoding: "utf-8",
          };
          break;

        case "write":
          if (!parameters.content) {
            throw new Error(
              "Content parameter is required for write operation"
            );
          }
          result = {
            operation: "write",
            path,
            bytesWritten: parameters.content.length,
            success: true,
          };
          break;

        case "delete":
          result = {
            operation: "delete",
            path,
            deleted: true,
          };
          break;

        case "list":
          result = {
            operation: "list",
            path,
            files: [
              { name: "file1.txt", size: 1024, type: "file" },
              { name: "file2.json", size: 2048, type: "file" },
              { name: "subfolder", size: 0, type: "directory" },
            ],
          };
          break;

        case "exists":
          result = {
            operation: "exists",
            path,
            exists: true,
          };
          break;

        case "stat":
          result = {
            operation: "stat",
            path,
            stats: {
              size: 1024,
              created: new Date().toISOString(),
              modified: new Date().toISOString(),
              type: "file",
              permissions: "rw-r--r--",
            },
          };
          break;

        default:
          throw new Error(`Operation ${operation} not implemented`);
      }

      return {
        success: true,
        data: result,
        executionTime: Date.now() - startTime,
        metadata: {
          operation,
          path,
          fileSystem: "mock",
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "File operation failed",
        executionTime: Date.now() - startTime,
        metadata: {
          operation: parameters.operation,
          path: parameters.path,
          fileSystem: "mock",
        },
      };
    }
  }
}

// Global tool execution framework instance
let toolFramework: ToolExecutionFramework | null = null;

export function getToolFramework(): ToolExecutionFramework {
  if (!toolFramework) {
    toolFramework = new ToolExecutionFramework();
  }
  return toolFramework;
}

export { ToolExecutionFramework };
