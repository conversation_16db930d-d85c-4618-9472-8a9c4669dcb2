// Browser-safe database module

export interface QueryOptions {
  timeout?: number;
}

export interface QueryResult<T = any> {
  rows: T[];
  rowCount: number;
  command: string;
  oid: number;
  fields: any[];
}

class Database {
  private static instance: Database;
  private pool: any = null;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public async connect(): Promise<void> {
    // Database connection is handled by the backend server
    // Frontend connects via API calls
    console.log("🌐 Frontend connects to database via API server");
    this.isConnected = true;
    return;

    // Server-side database connection (Node.js only)
    try {
      const { Pool } = await import("pg");
      const { getDatabaseConfig, createPoolConfig } = await import("./config");

      const config = getDatabaseConfig();
      const poolConfig = createPoolConfig(config);
      this.pool = new Pool(poolConfig);

      // Test the connection with timeout
      const client = await this.pool.connect();
      try {
        await Promise.race([
          client.query("SELECT NOW()"),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Connection timeout")), 5000)
          ),
        ]);
      } finally {
        client.release();
      }

      this.isConnected = true;
      console.log("🗄️ Database connection established");

      // Run migrations
      await this.runMigrations();

      // Setup connection monitoring
      this.setupConnectionMonitoring();
    } catch (error) {
      console.error("Failed to connect to database:", error);
      this.isConnected = false;
      throw error;
    }
  }

  private async runMigrations(): Promise<void> {
    if (typeof window !== "undefined") return;

    try {
      console.log("🔄 Running database migrations...");
      const { migrationManager } = await import("./migrations/index");
      await migrationManager.migrate();
      console.log("✅ Database migrations completed");
    } catch (error) {
      console.error("❌ Database migration failed:", error);
      throw error;
    }
  }

  private setupConnectionMonitoring(): void {
    if (!this.pool || typeof window !== "undefined") return;

    // Monitor pool events
    this.pool.on("error", (err: any) => {
      console.error("Database pool error:", err);
      this.isConnected = false;
    });

    this.pool.on("connect", () => {
      console.log("New database client connected");
    });

    this.pool.on("remove", () => {
      console.log("Database client removed");
    });

    // Periodic health check
    setInterval(async () => {
      try {
        await this.healthCheck();
      } catch (error) {
        console.error("Database health check failed:", error);
        this.isConnected = false;
      }
    }, 30000); // Every 30 seconds
  }

  public async disconnect(): Promise<void> {
    if (this.pool && typeof window === "undefined") {
      await this.pool.end();
      this.pool = null;
      console.log("Database connection closed");
    }
  }

  public async query<T = any>(
    text: string,
    params?: any[],
    options: QueryOptions = {}
  ): Promise<QueryResult<T>> {
    // Frontend queries are handled via API calls to backend
    throw new Error(
      "Direct database queries not available in frontend. Use API client instead."
    );

    if (!this.pool) {
      throw new Error("Database not connected");
    }

    const start = Date.now();
    let result: any;

    try {
      const client = await this.pool.connect();
      try {
        if (options.timeout) {
          await client.query(`SET statement_timeout = ${options.timeout}`);
        }

        result = await client.query(text, params);
        return result;
      } catch (error) {
        throw error;
      } finally {
        const duration = Date.now() - start;
        console.log(`Query executed in ${duration}ms`);
        client.release();
      }
    } catch (error) {
      throw error;
    }
  }

  public async transaction<T>(
    callback: (client: any) => Promise<T>
  ): Promise<T> {
    // Return mock result in browser environment
    if (typeof window !== "undefined") {
      throw new Error("Database transactions not available in browser");
    }

    if (!this.pool || !this.isConnected) {
      throw new Error("Database not connected");
    }

    const client = await this.pool.connect();

    try {
      await client.query("BEGIN");
      const result = await callback(client);
      await client.query("COMMIT");
      return result;
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  }

  // Provider Management
  public async createProvider(
    provider: Omit<DBProvider, "id" | "created_at" | "updated_at">
  ): Promise<DBProvider> {
    const query = `
            INSERT INTO providers (name, type, api_key, status, config, models, last_used)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
    const values = [
      provider.name,
      provider.type,
      provider.api_key,
      provider.status,
      JSON.stringify(provider.config),
      provider.models || [],
      provider.last_used,
    ];
    const result = await this.query<DBProvider>(query, values);
    return result.rows[0];
  }

  public async getProviders(): Promise<DBProvider[]> {
    const query = "SELECT * FROM providers ORDER BY created_at DESC";
    const result = await this.query<DBProvider>(query);
    return result.rows;
  }

  public async getProvider(id: string): Promise<DBProvider | null> {
    const query = "SELECT * FROM providers WHERE id = $1";
    const result = await this.query<DBProvider>(query, [id]);
    return result.rows[0] || null;
  }

  public async updateProvider(
    id: string,
    updates: Partial<DBProvider>
  ): Promise<DBProvider | null> {
    const fields = [];
    const values = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(updates)) {
      if (key !== "id" && key !== "created_at" && value !== undefined) {
        fields.push(`${key} = $${paramIndex}`);
        values.push(key === "config" ? JSON.stringify(value) : value);
        paramIndex++;
      }
    }

    if (fields.length === 0) {
      return this.getProvider(id);
    }

    const query = `
            UPDATE providers 
            SET ${fields.join(", ")}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramIndex}
            RETURNING *
        `;
    values.push(id);

    const result = await this.query<DBProvider>(query, values);
    return result.rows[0] || null;
  }

  public async deleteProvider(id: string): Promise<boolean> {
    const query = "DELETE FROM providers WHERE id = $1";
    const result = await this.query(query, [id]);
    return result.rowCount > 0;
  }

  // Agent Management
  public async createAgent(
    agent: Omit<DBAgent, "id" | "created_at" | "updated_at">
  ): Promise<DBAgent> {
    const query = `
            INSERT INTO agents (name, description, type, status, version, provider_id, model, system_prompt, config, deployed_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        `;
    const values = [
      agent.name,
      agent.description,
      agent.type,
      agent.status,
      agent.version,
      agent.provider_id,
      agent.model,
      agent.system_prompt,
      JSON.stringify(agent.config),
      agent.deployed_at,
    ];
    const result = await this.query<DBAgent>(query, values);
    return result.rows[0];
  }

  public async getAgents(): Promise<DBAgent[]> {
    const query = "SELECT * FROM agents ORDER BY created_at DESC";
    const result = await this.query<DBAgent>(query);
    return result.rows;
  }

  public async getAgent(id: string): Promise<DBAgent | null> {
    const query = "SELECT * FROM agents WHERE id = $1";
    const result = await this.query<DBAgent>(query, [id]);
    return result.rows[0] || null;
  }

  public async updateAgent(
    id: string,
    updates: Partial<DBAgent>
  ): Promise<DBAgent | null> {
    const fields = [];
    const values = [];
    let paramIndex = 1;

    for (const [key, value] of Object.entries(updates)) {
      if (key !== "id" && key !== "created_at" && value !== undefined) {
        fields.push(`${key} = $${paramIndex}`);
        values.push(key === "config" ? JSON.stringify(value) : value);
        paramIndex++;
      }
    }

    if (fields.length === 0) {
      return this.getAgent(id);
    }

    const query = `
            UPDATE agents 
            SET ${fields.join(", ")}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramIndex}
            RETURNING *
        `;
    values.push(id);

    const result = await this.query<DBAgent>(query, values);
    return result.rows[0] || null;
  }

  public async deleteAgent(id: string): Promise<boolean> {
    const query = "DELETE FROM agents WHERE id = $1";
    const result = await this.query(query, [id]);
    return result.rowCount > 0;
  }

  // User Management
  public async createUser(
    user: Omit<DBUser, "id" | "created_at" | "updated_at">
  ): Promise<DBUser> {
    const query = `
            INSERT INTO users (email, name, role, password_hash)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        `;
    const values = [user.email, user.name, user.role, user.password_hash];
    const result = await this.query<DBUser>(query, values);
    return result.rows[0];
  }

  public async getUserByEmail(email: string): Promise<DBUser | null> {
    const query = "SELECT * FROM users WHERE email = $1";
    const result = await this.query<DBUser>(query, [email]);
    return result.rows[0] || null;
  }

  public async getUser(id: string): Promise<DBUser | null> {
    const query = "SELECT * FROM users WHERE id = $1";
    const result = await this.query<DBUser>(query, [id]);
    return result.rows[0] || null;
  }

  public async updateUserLastLogin(id: string): Promise<void> {
    const query =
      "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1";
    await this.query(query, [id]);
  }

  // Metrics Management
  public async recordMetric(
    metric: Omit<DBMetrics, "id" | "timestamp">
  ): Promise<void> {
    const query = `
            INSERT INTO metrics (entity_type, entity_id, metrics)
            VALUES ($1, $2, $3)
        `;
    const values = [
      metric.entity_type,
      metric.entity_id,
      JSON.stringify(metric.metrics),
    ];
    await this.query(query, values);
  }

  public async getMetrics(
    entityType: string,
    entityId: string,
    limit: number = 100
  ): Promise<DBMetrics[]> {
    const query = `
            SELECT * FROM metrics 
            WHERE entity_type = $1 AND entity_id = $2 
            ORDER BY timestamp DESC 
            LIMIT $3
        `;
    const result = await this.query<DBMetrics>(query, [
      entityType,
      entityId,
      limit,
    ]);
    return result.rows;
  }

  public async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    latency?: number;
    error?: string;
    metrics?: any;
  }> {
    try {
      const start = Date.now();
      await this.query("SELECT 1");
      const latency = Date.now() - start;

      return {
        status: "healthy",
        latency,
        metrics: {},
      };
    } catch (error) {
      return {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  // Event Management
  public async createEvent(
    event: Omit<DBEvent, "id" | "created_at">
  ): Promise<DBEvent> {
    const query = `
            INSERT INTO events (type, data, created_at)
            VALUES ($1, $2, $3)
            RETURNING *
        `;
    const values = [
      event.type,
      JSON.stringify(event.data),
      new Date().toISOString(),
    ];
    const result = await this.query<DBEvent>(query, values);
    return result.rows[0];
  }

  public async getEvents(limit: number = 100): Promise<DBEvent[]> {
    const query = `
            SELECT * FROM events 
            ORDER BY created_at DESC
            LIMIT $1
        `;
    const result = await this.query<DBEvent>(query, [limit]);
    return result.rows;
  }

  public async deleteEvent(id: string): Promise<boolean> {
    const query = "DELETE FROM events WHERE id = $1";
    const result = await this.query(query, [id]);
    return result.rowCount > 0;
  }
}

// Export singleton instance
export const db = Database.getInstance();

// Export types for database entities
export interface DBUser {
  id: string;
  email: string;
  name: string;
  role: "admin" | "user" | "viewer";
  password_hash: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
}

export interface DBProvider {
  id: string;
  name: string;
  type: string;
  api_key: string;
  status: string;
  config: Record<string, any>;
  models?: string[];
  last_used?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface DBAgent {
  id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  version: string;
  provider_id: string;
  model: string;
  system_prompt: string;
  config: Record<string, any>;
  deployed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface DBMetrics {
  id: string;
  entity_type: "provider" | "agent";
  entity_id: string;
  metrics: Record<string, any>;
  timestamp: Date;
}

export interface DBEvent {
  id: string;
  type: string;
  data: Record<string, any>;
  created_at: Date;
}

export class DatabaseError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "DatabaseError";
  }
}

export class ConnectionError extends DatabaseError {
  constructor(message: string) {
    super(message);
    this.name = "ConnectionError";
  }
}

export class QueryError extends DatabaseError {
  constructor(message: string) {
    super(message);
    this.name = "QueryError";
  }
}
