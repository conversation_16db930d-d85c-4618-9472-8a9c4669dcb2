@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 100% 50%;

    --radius: 0.75rem;

    /* Custom gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(220, 100%, 60%) 0%,
      hsl(260, 100%, 70%) 50%,
      hsl(300, 100%, 80%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(210, 40%, 96.1%) 0%,
      hsl(220, 40%, 94%) 100%
    );
    --gradient-surface: linear-gradient(
      135deg,
      hsl(0, 0%, 100%) 0%,
      hsl(210, 40%, 98%) 100%
    );
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 100% 60%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 100% 60%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(220, 100%, 60%) 0%,
      hsl(260, 100%, 70%) 50%,
      hsl(300, 100%, 80%) 100%
    );
    --gradient-secondary: linear-gradient(
      135deg,
      hsl(217.2, 32.6%, 17.5%) 0%,
      hsl(220, 32%, 20%) 100%
    );
    --gradient-surface: linear-gradient(
      135deg,
      hsl(222.2, 84%, 4.9%) 0%,
      hsl(220, 84%, 6%) 100%
    );
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Smooth transitions for theme switching */
  * {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/80 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-surface {
    background: var(--gradient-surface);
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.12);
  }

  .shadow-medium {
    box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
  }

  .shadow-large {
    box-shadow: 0 8px 24px 0 rgb(0 0 0 / 0.15);
  }

  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 ease-smooth hover:scale-[1.02] hover:shadow-medium active:scale-[0.98];
  }

  .interactive-subtle {
    @apply transition-all duration-200 ease-smooth hover:bg-accent/50 hover:shadow-soft;
  }

  /* Loading states */
  .loading-shimmer {
    @apply relative overflow-hidden;
  }

  .loading-shimmer::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    content: "";
    animation: shimmer 2s ease-in-out infinite;
  }

  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-online {
    @apply bg-success-500;
    box-shadow:
      0 0 0 2px hsl(var(--background)),
      0 0 0 4px hsl(142, 76%, 36%);
  }

  .status-offline {
    @apply bg-muted-foreground;
  }

  .status-busy {
    @apply bg-warning-500;
    box-shadow:
      0 0 0 2px hsl(var(--background)),
      0 0 0 4px hsl(38, 92%, 50%);
  }

  .status-error {
    @apply bg-destructive;
    box-shadow:
      0 0 0 2px hsl(var(--background)),
      0 0 0 4px hsl(var(--destructive));
  }
}

@layer utilities {
  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary via-purple-500 to-pink-500 bg-clip-text text-transparent;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Layout utilities */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }

  /* Spacing utilities */
  .space-y-px > * + * {
    @apply mt-px;
  }

  /* Animation utilities */
  .animate-in {
    @apply animate-fade-in;
  }

  .animate-out {
    @apply animate-fade-out;
  }
}
