// Performance Monitoring and Optimization

import { getLogger } from "./logger";
import { getConfig } from "./config";
import React from "react";

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: "ms" | "bytes" | "count" | "percentage";
  timestamp: string;
  category: "load" | "render" | "api" | "user" | "memory";
  metadata?: Record<string, any>;
}

export interface PerformanceThreshold {
  metric: string;
  warning: number;
  critical: number;
  unit: string;
}

class PerformanceMonitor {
  private logger = getLogger().createChild("Performance");
  private metrics: PerformanceMetric[] = [];
  private observers: Map<string, PerformanceObserver> = new Map();
  private timers: Map<string, number> = new Map();
  private config = getConfig();

  private thresholds: PerformanceThreshold[] = [
    { metric: "page-load", warning: 3000, critical: 5000, unit: "ms" },
    { metric: "api-response", warning: 2000, critical: 5000, unit: "ms" },
    { metric: "component-render", warning: 100, critical: 500, unit: "ms" },
    { metric: "memory-usage", warning: 50, critical: 80, unit: "percentage" },
  ];

  constructor() {
    if (typeof window !== "undefined" && this.config.canUseMonitoring()) {
      this.initializeObservers();
      this.startMemoryMonitoring();
      this.measurePageLoad();
    }
  }

  private initializeObservers(): void {
    try {
      // Navigation timing
      if ("PerformanceObserver" in window) {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === "navigation") {
              this.recordNavigationMetrics(
                entry as PerformanceNavigationTiming
              );
            }
          }
        });
        navObserver.observe({ entryTypes: ["navigation"] });
        this.observers.set("navigation", navObserver);

        // Resource timing
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === "resource") {
              this.recordResourceMetric(entry as PerformanceResourceTiming);
            }
          }
        });
        resourceObserver.observe({ entryTypes: ["resource"] });
        this.observers.set("resource", resourceObserver);

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric({
            name: "largest-contentful-paint",
            value: lastEntry.startTime,
            unit: "ms",
            category: "load",
            metadata: {
              element: (lastEntry as any).element?.tagName,
              url: (lastEntry as any).url,
            },
          });
        });
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
        this.observers.set("lcp", lcpObserver);

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: "first-input-delay",
              value: (entry as any).processingStart - entry.startTime,
              unit: "ms",
              category: "user",
              metadata: {
                eventType: (entry as any).name,
              },
            });
          }
        });
        fidObserver.observe({ entryTypes: ["first-input"] });
        this.observers.set("fid", fidObserver);
      }
    } catch (error) {
      this.logger.warn("Failed to initialize performance observers", { error });
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      {
        name: "dns-lookup",
        value: entry.domainLookupEnd - entry.domainLookupStart,
        unit: "ms" as const,
        category: "load" as const,
      },
      {
        name: "tcp-connection",
        value: entry.connectEnd - entry.connectStart,
        unit: "ms" as const,
        category: "load" as const,
      },
      {
        name: "server-response",
        value: entry.responseEnd - entry.requestStart,
        unit: "ms" as const,
        category: "load" as const,
      },
      {
        name: "dom-content-loaded",
        value: entry.domContentLoadedEventEnd - entry.fetchStart,
        unit: "ms" as const,
        category: "load" as const,
      },
      {
        name: "page-load-complete",
        value: entry.loadEventEnd - entry.fetchStart,
        unit: "ms" as const,
        category: "load" as const,
      },
    ];

    metrics.forEach((metric) => this.recordMetric(metric));
  }

  private recordResourceMetric(entry: PerformanceResourceTiming): void {
    // Only track significant resources
    if (entry.transferSize > 10000 || entry.duration > 100) {
      this.recordMetric({
        name: "resource-load",
        value: entry.duration,
        unit: "ms",
        category: "load",
        metadata: {
          name: entry.name,
          size: entry.transferSize,
          type: this.getResourceType(entry.name),
        },
      });
    }
  }

  private getResourceType(url: string): string {
    if (url.includes(".js")) return "javascript";
    if (url.includes(".css")) return "stylesheet";
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return "image";
    if (url.includes("/api/")) return "api";
    return "other";
  }

  private measurePageLoad(): void {
    if (typeof window !== "undefined") {
      window.addEventListener("load", () => {
        setTimeout(() => {
          const loadTime = performance.now();
          this.recordMetric({
            name: "page-load",
            value: loadTime,
            unit: "ms",
            category: "load",
          });
        }, 0);
      });
    }
  }

  private startMemoryMonitoring(): void {
    if ("memory" in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        if (memory) {
          const usedPercent =
            (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;

          this.recordMetric({
            name: "memory-usage",
            value: usedPercent,
            unit: "percentage",
            category: "memory",
            metadata: {
              used: memory.usedJSHeapSize,
              total: memory.totalJSHeapSize,
              limit: memory.jsHeapSizeLimit,
            },
          });
        }
      }, 30000); // Check every 30 seconds
    }
  }

  // Public API
  recordMetric(metric: Omit<PerformanceMetric, "timestamp">): void {
    const fullMetric: PerformanceMetric = {
      ...metric,
      timestamp: new Date().toISOString(),
    };

    this.metrics.push(fullMetric);
    this.maintainMetricsSize();
    this.checkThresholds(fullMetric);

    this.logger.debug(`Performance metric recorded: ${metric.name}`, {
      value: metric.value,
      unit: metric.unit,
      category: metric.category,
    });
  }

  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  endTimer(
    name: string,
    category: PerformanceMetric["category"] = "user"
  ): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      this.logger.warn(`Timer '${name}' was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);

    this.recordMetric({
      name,
      value: duration,
      unit: "ms",
      category,
    });

    return duration;
  }

  measureAsync<T>(name: string, operation: () => Promise<T>): Promise<T> {
    return new Promise(async (resolve, reject) => {
      this.startTimer(name);
      try {
        const result = await operation();
        this.endTimer(name, "api");
        resolve(result);
      } catch (error) {
        this.endTimer(name, "api");
        reject(error);
      }
    });
  }

  measureSync<T>(name: string, operation: () => T): T {
    this.startTimer(name);
    try {
      const result = operation();
      this.endTimer(name, "render");
      return result;
    } catch (error) {
      this.endTimer(name, "render");
      throw error;
    }
  }

  private maintainMetricsSize(): void {
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  private checkThresholds(metric: PerformanceMetric): void {
    const threshold = this.thresholds.find((t) => t.metric === metric.name);
    if (!threshold) return;

    if (metric.value >= threshold.critical) {
      this.logger.error(
        `Critical performance threshold exceeded for ${metric.name}`,
        {
          value: metric.value,
          threshold: threshold.critical,
          unit: metric.unit,
        }
      );
    } else if (metric.value >= threshold.warning) {
      this.logger.warn(
        `Performance warning threshold exceeded for ${metric.name}`,
        {
          value: metric.value,
          threshold: threshold.warning,
          unit: metric.unit,
        }
      );
    }
  }

  getMetrics(
    category?: PerformanceMetric["category"],
    limit?: number
  ): PerformanceMetric[] {
    let filtered = this.metrics;

    if (category) {
      filtered = filtered.filter((m) => m.category === category);
    }

    if (limit) {
      filtered = filtered.slice(-limit);
    }

    return [...filtered];
  }

  getAverageMetric(name: string, timeWindow?: number): number {
    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : 0;

    const relevantMetrics = this.metrics.filter(
      (m) => m.name === name && new Date(m.timestamp).getTime() >= windowStart
    );

    if (relevantMetrics.length === 0) return 0;

    const sum = relevantMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / relevantMetrics.length;
  }

  getPerformanceReport(): {
    summary: Record<string, { avg: number; max: number; count: number }>;
    recentMetrics: PerformanceMetric[];
    thresholdViolations: Array<{
      metric: string;
      value: number;
      threshold: number;
      severity: "warning" | "critical";
    }>;
  } {
    const summary: Record<string, { avg: number; max: number; count: number }> =
      {};
    const thresholdViolations: Array<{
      metric: string;
      value: number;
      threshold: number;
      severity: "warning" | "critical";
    }> = [];

    // Calculate summary statistics
    this.metrics.forEach((metric) => {
      if (!summary[metric.name]) {
        summary[metric.name] = { avg: 0, max: 0, count: 0 };
      }

      const stats = summary[metric.name];
      stats.count++;
      stats.max = Math.max(stats.max, metric.value);
      stats.avg = (stats.avg * (stats.count - 1) + metric.value) / stats.count;

      // Check for threshold violations
      const threshold = this.thresholds.find((t) => t.metric === metric.name);
      if (threshold) {
        if (metric.value >= threshold.critical) {
          thresholdViolations.push({
            metric: metric.name,
            value: metric.value,
            threshold: threshold.critical,
            severity: "critical",
          });
        } else if (metric.value >= threshold.warning) {
          thresholdViolations.push({
            metric: metric.name,
            value: metric.value,
            threshold: threshold.warning,
            severity: "warning",
          });
        }
      }
    });

    return {
      summary,
      recentMetrics: this.metrics.slice(-20),
      thresholdViolations,
    };
  }

  clearMetrics(): void {
    this.metrics = [];
    this.timers.clear();
  }

  destroy(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers.clear();
    this.clearMetrics();
  }
}

// Global performance monitor instance
let performanceMonitor: PerformanceMonitor | null = null;

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor();
  }
  return performanceMonitor;
}

// Utility functions
export function measureComponent<P extends object>(
  Component: React.ComponentType<P>,
  name?: string
): React.ComponentType<P> {
  const componentName =
    name || Component.displayName || Component.name || "Component";

  return function MeasuredComponent(props: P) {
    const monitor = getPerformanceMonitor();

    React.useEffect(() => {
      monitor.startTimer(`${componentName}-mount`);
      return () => {
        monitor.endTimer(`${componentName}-mount`, "render");
      };
    }, []);

    return React.createElement(Component, props);
  };
}

export function withPerformanceTracking<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: any[]) => {
    const monitor = getPerformanceMonitor();
    return monitor.measureSync(name, () => fn(...args));
  }) as T;
}

export { PerformanceMonitor };
