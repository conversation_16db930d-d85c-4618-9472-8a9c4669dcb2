
export interface AppConfig {
  environment: "development" | "staging" | "production";
  version: string;
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    mockEnabled: boolean;
  };
  auth: {
    enabled: boolean;
    sessionTimeout: number;
    jwt: {
      secret: string;
      issuer: string;
      audience: string;
      expiresIn: string;
      refreshExpiresIn: string;
      algorithm: string;
    };
    rbac: {
      enabled: boolean;
      defaultRole: string;
      roleHierarchy: Record<string, string[]>;
    };
    multiTenant: {
      enabled: boolean;
      tenantHeader: string;
      isolationLevel: "strict" | "shared";
    };
  };
  features: {
    routing: boolean;
    eventSystem: boolean;
    analytics: boolean;
    monitoring: boolean;
    realTime: boolean;
    streaming: boolean;
    multiModal: boolean;
  };
  logging: {
    enabled: boolean;
    level: "debug" | "info" | "warn" | "error";
  };
  performance: {
    codeSplitting: boolean;
    lazyLoading: boolean;
    bundleAnalyzer: boolean;
  };
  rateLimit: {
    enabled: boolean;
    requestsPerMinute: number;
    requestsPerDay: number;
  };
  security: {
    corsEnabled: boolean;
    corsOrigins: string[];
    csrfProtection: boolean;
  };
  monitoring: {
    sentryDsn?: string;
    analyticsTrackingId?: string;
  };
  database: {
    enabled: boolean;
    url?: string;
    poolSize: number;
  };
  websocket: {
    enabled: boolean;
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    heartbeatInterval: number;
    enableCompression: boolean;
    protocols: string[];
    bufferSize: number;
    messageQueueSize: number;
    sessionTTL: number;
    enableStatePersistence: boolean;
  };
}

class ConfigManager {
  constructor() {
    this.validateConfig();
  }

  private loadConfig(): AppConfig {
    const env = import.meta.env;

    return {
      environment: (env.VITE_APP_ENV ||
        "development") as AppConfig["environment"],
      version: env.VITE_APP_VERSION || "1.0.0",
      api: {
        baseUrl: env.VITE_API_BASE_URL || "http://localhost:3001",
        timeout: parseInt(env.VITE_API_TIMEOUT || "30000"),
        retryAttempts: parseInt(env.VITE_API_RETRY_ATTEMPTS || "3"),
        mockEnabled: false, // NEVER use mock API in production
      },
      auth: {
        enabled: env.VITE_AUTH_ENABLED !== "false",
        sessionTimeout: parseInt(env.VITE_SESSION_TIMEOUT || "3600000"),
        jwt: {
          secret: env.VITE_JWT_SECRET || this.generateSecureSecret(),
          issuer: env.VITE_JWT_ISSUER || "synapseai.com",
          audience: env.VITE_JWT_AUDIENCE || "synapseai-platform",
          expiresIn: env.VITE_JWT_EXPIRES_IN || "15m",
          refreshExpiresIn: env.VITE_JWT_REFRESH_EXPIRES_IN || "7d",
          algorithm: env.VITE_JWT_ALGORITHM || "HS256",
        },
        rbac: {
          enabled: env.VITE_RBAC_ENABLED !== "false",
          defaultRole: env.VITE_RBAC_DEFAULT_ROLE || "user",
          roleHierarchy: {
            superadmin: ["admin", "user", "viewer"],
            admin: ["user", "viewer"],
            user: ["viewer"],
            viewer: [],
          },
        },
        multiTenant: {
          enabled: env.VITE_MULTI_TENANT_ENABLED !== "false",
          tenantHeader: env.VITE_TENANT_HEADER || "X-Tenant-ID",
          isolationLevel: (env.VITE_TENANT_ISOLATION_LEVEL || "strict") as
            | "strict"
            | "shared",
        },
      },
      features: {
        routing: env.VITE_FEATURE_ROUTING !== "false",
        eventSystem: env.VITE_FEATURE_EVENT_SYSTEM !== "false",
        analytics: env.VITE_FEATURE_ANALYTICS !== "false",
        monitoring: env.VITE_FEATURE_MONITORING !== "false",
        realTime: env.VITE_FEATURE_REAL_TIME !== "false",
        streaming: env.VITE_FEATURE_STREAMING !== "false",
        multiModal: env.VITE_FEATURE_MULTI_MODAL !== "false",
      },
      logging: {
        enabled: env.VITE_LOGGING_ENABLED !== "false",
        level: (env.VITE_LOGGING_LEVEL ||
          "info") as AppConfig["logging"]["level"],
      },
      performance: {
        codeSplitting: env.VITE_ENABLE_CODE_SPLITTING !== "false",
        lazyLoading: env.VITE_ENABLE_LAZY_LOADING !== "false",
        bundleAnalyzer: env.VITE_BUNDLE_ANALYZER === "true",
      },
      rateLimit: {
        enabled: env.VITE_RATE_LIMIT_ENABLED !== "false",
        requestsPerMinute: parseInt(
          env.VITE_RATE_LIMIT_REQUESTS_PER_MINUTE || "60"
        ),
        requestsPerDay: parseInt(
          env.VITE_RATE_LIMIT_REQUESTS_PER_DAY || "10000"
        ),
      },
      security: {
        corsEnabled: env.VITE_CORS_ENABLED !== "false",
        corsOrigins: (
          env.VITE_CORS_ORIGINS ||
          "http://localhost:3000,https://localhost:3000"
        ).split(","),
        csrfProtection: env.VITE_CSRF_PROTECTION !== "false",
      },
      monitoring: {
        sentryDsn: env.VITE_SENTRY_DSN,
        analyticsTrackingId: env.VITE_ANALYTICS_TRACKING_ID,
      },
      database: {
        enabled: env.VITE_DATABASE_ENABLED !== "false",
        url: env.DATABASE_URL || "postgresql://localhost:5432/synapseai",
        poolSize: parseInt(env.DATABASE_POOL_SIZE || "20"),
      },
      websocket: {
        enabled: env.VITE_WEBSOCKET_ENABLED !== "false",
        url: env.VITE_WEBSOCKET_URL || "wss://api.synapseai.com/apix",
        reconnectInterval: parseInt(env.VITE_WS_RECONNECT_INTERVAL || "5000"),
        maxReconnectAttempts: parseInt(
          env.VITE_WS_MAX_RECONNECT_ATTEMPTS || "10"
        ),
        heartbeatInterval: parseInt(env.VITE_WS_HEARTBEAT_INTERVAL || "30000"),
        enableCompression: env.VITE_WS_ENABLE_COMPRESSION !== "false",
        protocols: (env.VITE_WS_PROTOCOLS || "apix-v1").split(","),
        bufferSize: parseInt(env.VITE_WS_BUFFER_SIZE || "1048576"), // 1MB
        messageQueueSize: parseInt(env.VITE_WS_MESSAGE_QUEUE_SIZE || "1000"),
        sessionTTL: parseInt(env.VITE_WS_SESSION_TTL || "3600000"), // 1 hour
        enableStatePersistence:
          env.VITE_WS_ENABLE_STATE_PERSISTENCE !== "false",
      },
    };
  }

  private generateSecureSecret(): string {
    // Generate a secure random secret for JWT signing
    const array = new Uint8Array(64);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  private validateConfig(): void {
    const errors: string[] = [];

    // Validate API configuration
    if (!this.getConfig().api.baseUrl) {
      errors.push("API base URL is required");
    }

    if (this.getConfig().api.timeout < 1000) {
      errors.push("API timeout must be at least 1000ms");
    }

    if (
      this.getConfig().api.retryAttempts < 0 ||
      this.getConfig().api.retryAttempts > 10
    ) {
      errors.push("API retry attempts must be between 0 and 10");
    }

    // Validate JWT secret strength
    if (this.getConfig().auth.jwt.secret.length < 32) {
      errors.push("JWT secret must be at least 32 characters long");
    }

    // Validate CORS origins
    if (
      this.getConfig().security.corsEnabled &&
      this.getConfig().security.corsOrigins.length === 0
    ) {
      errors.push("CORS origins must be specified when CORS is enabled");
    }

    // Validate rate limiting
    if (this.getConfig().rateLimit.requestsPerMinute < 1) {
      errors.push("Rate limit requests per minute must be at least 1");
    }

    if (this.getConfig().rateLimit.requestsPerDay < 1) {
      errors.push("Rate limit requests per day must be at least 1");
    }

    // Validate database configuration
    if (this.getConfig().database.enabled && !this.getConfig().database.url) {
      errors.push("Database URL is required when database is enabled");
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.join(", ")}`);
    }
  }

  getConfig(): AppConfig {
    return { ...this.loadConfig() };
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.getConfig()[key];
  }

  isFeatureEnabled(feature: keyof AppConfig["features"]): boolean {
    return this.getConfig().features[feature];
  }

  isDevelopment(): boolean {
    return this.getConfig().environment === "development";
  }

  isProduction(): boolean {
    return this.getConfig().environment === "production";
  }

  isStaging(): boolean {
    return this.getConfig().environment === "staging";
  }



  // Production readiness checks
  isProductionReady(): {
    ready: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check critical configurations
    if (
      !this.getConfig().api.baseUrl ||
      this.getConfig().api.baseUrl.includes("localhost")
    ) {
      issues.push("Production API URL not configured");
    }

    if (this.getConfig().auth.jwt.secret.length < 32) {
      issues.push("JWT secret is too weak for production");
    }

    if (!this.getConfig().monitoring.sentryDsn && this.isProduction()) {
      issues.push("Error monitoring not configured");
    }

    if (this.getConfig().api.mockEnabled) {
      issues.push("Mock API is still enabled");
    }

    if (!this.getConfig().security.corsEnabled) {
      issues.push("CORS protection is disabled");
    }

    if (!this.getConfig().security.csrfProtection) {
      issues.push("CSRF protection is disabled");
    }

    return {
      ready: issues.length === 0,
      issues,
    };
  }

  getApiBaseUrl(): string {
    return this.getConfig().api.baseUrl;
  }

  getLogLevel(): AppConfig["logging"]["level"] {
    return this.getConfig().logging.level;
  }

  isLoggingEnabled(): boolean {
    return this.getConfig().logging.enabled;
  }

  // Feature flag methods
  canUseRouting(): boolean {
    return this.getConfig().features.routing;
  }

  canUseEventSystem(): boolean {
    return this.getConfig().features.eventSystem;
  }

  canUseAnalytics(): boolean {
    return this.getConfig().features.analytics;
  }

  canUseMonitoring(): boolean {
      return this.getConfig().features.monitoring;
  }

  canUseRealTime(): boolean {
    return this.getConfig().features.realTime;
  }

  canUseStreaming(): boolean {
    return this.getConfig().features.streaming;
  }

  canUseMultiModal(): boolean {
    return this.getConfig().features.multiModal;
  }

  // Environment-specific configurations
  getEnvironmentSpecificConfig() {
    const baseConfig = {
      apiUrl: this.getConfig().api.baseUrl,
      timeout: this.getConfig().api.timeout,
      retryAttempts: this.getConfig().api.retryAttempts,
    };

    switch (this.getConfig().environment) {
      case "development":
        return {
          ...baseConfig,
          debug: true,
          logLevel: "debug",
        };
      case "staging":
        return {
          ...baseConfig,
          debug: false,
          logLevel: "info",
        };
      case "production":
        return {
          ...baseConfig,
          debug: false,
          logLevel: "error",
        };
      default:
        return baseConfig;
    }
  }
}

// Global configuration instance
let configManager: ConfigManager | null = null;

export function getConfig(): ConfigManager {
  if (!configManager) {
    configManager = new ConfigManager();
  }
  return configManager;
}

export { ConfigManager };
