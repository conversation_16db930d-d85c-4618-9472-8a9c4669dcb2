# SynapseAI Backend Integration

This document describes the backend integration for the SynapseAI platform.

## Features Implemented

### Database Layer
- PostgreSQL database with comprehensive schema
- Migration system with up/down migrations
- Connection pooling and monitoring
- CRUD operations for all entities
- Analytics and metrics tracking

### API Endpoints
- RESTful API following the endpoints specification
- Provider management (CRUD + testing)
- Agent management (CRUD + deployment)
- Analytics and usage statistics
- Health checks and monitoring

### Frontend Integration
- Updated components to use real API calls
- Mock API server for development
- Error handling and loading states
- Real-time data synchronization

## Database Setup

### Prerequisites
1. PostgreSQL 12+ installed and running
2. Create a database named `synapseai`
3. Set the `DATABASE_URL` environment variable

### Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
DATABASE_URL=postgresql://username:password@localhost:5432/synapseai
VITE_DATABASE_ENABLED=true
VITE_MOCK_API=true
```

### Running Migrations

```bash
# Check migration status
npm run db:status

# Run migrations
npm run db:migrate

# Seed with sample data
npm run db:seed

# Rollback migrations (if needed)
npm run db:rollback
```

## API Endpoints

The following endpoints are implemented:

### Providers
- `GET /api/v1/providers` - List all providers
- `POST /api/v1/providers` - Create new provider
- `GET /api/v1/providers/:id` - Get provider by ID
- `PUT /api/v1/providers/:id` - Update provider
- `DELETE /api/v1/providers/:id` - Delete provider
- `POST /api/v1/providers/:id/test` - Test provider connection

### Agents
- `GET /api/v1/agents` - List all agents
- `POST /api/v1/agents` - Create new agent
- `GET /api/v1/agents/:id` - Get agent by ID
- `PUT /api/v1/agents/:id` - Update agent
- `DELETE /api/v1/agents/:id` - Delete agent
- `POST /api/v1/agents/:id/deploy` - Deploy agent

### Analytics
- `GET /api/v1/analytics/overview` - Get analytics overview
- `GET /api/v1/analytics/usage` - Get usage statistics

### Health
- `GET /api/v1/health` - Health check endpoint

## Development Mode

In development mode, the application uses a mock API server that:
- Intercepts API calls and routes them to the database
- Provides realistic response times and error simulation
- Allows full testing without a separate backend server

## Database Schema

The database includes the following main tables:

### Core Tables
- `users` - User accounts with RBAC
- `providers` - AI provider configurations
- `agents` - AI agent definitions
- `metrics` - Performance and usage metrics

### Security Tables
- `security_events` - Security event logging
- `security_alerts` - Security alerts and notifications

### Features
- Automatic timestamps (`created_at`, `updated_at`)
- UUID primary keys
- JSONB columns for flexible configuration
- Proper foreign key relationships
- Indexes for performance

## Data Validation

All data is validated at multiple levels:
1. Frontend form validation
2. API request validation
3. Database constraints
4. Business logic validation

## Security Features

- Input sanitization and validation
- SQL injection prevention
- Rate limiting
- Authentication and authorization
- Security event logging
- Encrypted sensitive data storage

## Monitoring and Analytics

- Real-time performance metrics
- Usage statistics and analytics
- Error tracking and logging
- Database query monitoring
- Health check endpoints

## Next Steps

To complete the backend integration:

1. **Production Backend**: Deploy a proper Node.js/Express backend server
2. **Authentication**: Implement JWT-based authentication
3. **WebSocket Server**: Add real-time WebSocket support
4. **Caching**: Implement Redis caching layer
5. **File Storage**: Add file upload and storage capabilities
6. **Email Service**: Integrate email notifications
7. **Monitoring**: Add comprehensive monitoring and alerting

## Testing

The implementation includes:
- Database connection testing
- API endpoint testing
- Mock data for development
- Error handling validation
- Performance monitoring

## Troubleshooting

### Database Connection Issues
1. Verify PostgreSQL is running
2. Check DATABASE_URL format
3. Ensure database exists
4. Check user permissions

### Migration Issues
1. Check database connectivity
2. Verify migration files exist
3. Check for syntax errors in SQL
4. Review migration logs

### API Issues
1. Check network connectivity
2. Verify API endpoints
3. Check request/response format
4. Review browser console for errors
