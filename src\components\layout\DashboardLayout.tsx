import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ThemeToggle from "@/components/ui/theme-toggle";
import { StatusIndicator } from "@/components/ui/status-indicator";
import {
  Bell,
  Settings,
  Menu,
  Search,
  Home,
  Brain,
  BarChart3,
  Activity,
  Users,
  Zap,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/lib/auth";

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title = "Dashboard",
  subtitle,
  actions,
}) => {
  const [showSidebar, setShowSidebar] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setShowSidebar(false);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Helper function to check if a path is active
  const isActivePath = (href: string) => {
    if (href === "/dashboard") {
      return location.pathname === "/dashboard" || location.pathname === "/";
    }
    return location.pathname.startsWith(href);
  };

  // Helper function to handle navigation
  const handleNavigation = (href: string) => {
    if (href === "/") {
      navigate("/dashboard");
    } else {
      navigate(`/dashboard${href}`);
    }

    // Close sidebar on mobile after navigation
    if (isMobile) {
      setShowSidebar(false);
    }
  };

  const navigationItems = [
    {
      icon: <Home className="h-4 w-4" />,
      label: "Overview",
      href: "/dashboard",
      active: isActivePath("/dashboard"),
    },
    {
      icon: <Brain className="h-4 w-4" />,
      label: "AI Providers",
      href: "/providers",
      active: isActivePath("/dashboard/providers"),
    },
    {
      icon: <Zap className="h-4 w-4" />,
      label: "Agents",
      href: "/agents",
      active: isActivePath("/dashboard/agents"),
    },
    {
      icon: <BarChart3 className="h-4 w-4" />,
      label: "Analytics",
      href: "/analytics",
      active: isActivePath("/dashboard/analytics"),
    },
    {
      icon: <Activity className="h-4 w-4" />,
      label: "Events",
      href: "/events",
      active: isActivePath("/dashboard/events"),
    },
    {
      icon: <Users className="h-4 w-4" />,
      label: "Users",
      href: "/users",
      active: isActivePath("/dashboard/users"),
    },
  ];

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-surface flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">
            Please log in to access the dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-surface text-foreground flex transition-all duration-300">
      {/* Sidebar */}
      {showSidebar && (
        <>
          <div className="w-64 border-r bg-card/95 backdrop-blur-xl shadow-large flex flex-col">
            {/* Logo */}
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="bg-gradient-primary h-8 w-8 rounded-xl flex items-center justify-center shadow-medium">
                  <span className="text-white font-bold text-sm">S</span>
                </div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                  SynapseAI
                </h1>
              </div>
              {!isMobile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSidebar(false)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-2">
              <ul className="space-y-1">
                {navigationItems.map((item, index) => (
                  <li key={index}>
                    <Button
                      variant={item.active ? "default" : "ghost"}
                      className={cn(
                        "w-full justify-start h-12 rounded-xl transition-all duration-200",
                        item.active
                          ? "bg-primary text-primary-foreground shadow-medium"
                          : "hover:bg-accent/50 hover:shadow-soft hover:scale-[1.02]"
                      )}
                      onClick={() => handleNavigation(item.href)}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.label}
                    </Button>
                  </li>
                ))}
              </ul>
            </nav>

            {/* User Info */}
            <div className="p-4 border-t">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar} />
                  <AvatarFallback>
                    {user?.name?.charAt(0) || "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user?.name || "User"}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user?.email}
                  </p>
                </div>
                <StatusIndicator
                  status="online"
                  size="sm"
                  variant="dot"
                  showLabel={false}
                />
              </div>
            </div>
          </div>

          {/* Mobile overlay */}
          {isMobile && (
            <div
              className="fixed inset-0 bg-black/50 z-40 md:hidden"
              onClick={() => setShowSidebar(false)}
            />
          )}
        </>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <header className="border-b border-border/50 p-4 flex items-center justify-between bg-card/80 backdrop-blur-xl shadow-soft">
          <div className="flex items-center space-x-4">
            {!showSidebar && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSidebar(true)}
              >
                {isMobile ? (
                  <Menu className="h-5 w-5" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}
            <div>
              <h1 className="text-xl font-semibold">{title}</h1>
              {subtitle && (
                <p className="text-sm text-muted-foreground">{subtitle}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative w-full max-w-md hidden md:block">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search..."
                className="pl-8 h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              />
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {actions}
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
              <ThemeToggle variant="dropdown" size="default" />
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar} />
                <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-6 overflow-auto">{children}</main>
      </div>
    </div>
  );
};

export default DashboardLayout;
