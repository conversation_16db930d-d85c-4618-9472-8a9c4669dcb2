// Data Encryption Utilities for SynapseAI

export class EncryptionManager {
  private static instance: EncryptionManager;
  private encryptionKey: CryptoKey | null = null;

  private constructor() {
    this.initializeEncryption();
  }

  static getInstance(): EncryptionManager {
    if (!EncryptionManager.instance) {
      EncryptionManager.instance = new EncryptionManager();
    }
    return EncryptionManager.instance;
  }

  private async initializeEncryption(): Promise<void> {
    try {
      // Generate or import encryption key
      this.encryptionKey = await crypto.subtle.generateKey(
        {
          name: "AES-GCM",
          length: 256,
        },
        true,
        ["encrypt", "decrypt"]
      );
    } catch (error) {
      console.error("Failed to initialize encryption:", error);
    }
  }

  async encryptData(
    data: string
  ): Promise<{ encrypted: string; iv: string } | null> {
    if (!this.encryptionKey) {
      console.error("Encryption key not initialized");
      return null;
    }

    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const iv = crypto.getRandomValues(new Uint8Array(12));

      const encrypted = await crypto.subtle.encrypt(
        {
          name: "AES-GCM",
          iv: iv,
        },
        this.encryptionKey,
        dataBuffer
      );

      return {
        encrypted: Array.from(new Uint8Array(encrypted), (byte) =>
          byte.toString(16).padStart(2, "0")
        ).join(""),
        iv: Array.from(iv, (byte) => byte.toString(16).padStart(2, "0")).join(
          ""
        ),
      };
    } catch (error) {
      console.error("Encryption failed:", error);
      return null;
    }
  }

  async decryptData(
    encryptedData: string,
    ivHex: string
  ): Promise<string | null> {
    if (!this.encryptionKey) {
      console.error("Encryption key not initialized");
      return null;
    }

    try {
      const encrypted = new Uint8Array(
        encryptedData.match(/.{1,2}/g)!.map((byte) => parseInt(byte, 16))
      );
      const iv = new Uint8Array(
        ivHex.match(/.{1,2}/g)!.map((byte) => parseInt(byte, 16))
      );

      const decrypted = await crypto.subtle.decrypt(
        {
          name: "AES-GCM",
          iv: iv,
        },
        this.encryptionKey,
        encrypted
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error("Decryption failed:", error);
      return null;
    }
  }

  // Secure local storage with encryption
  async setSecureItem(key: string, value: string): Promise<void> {
    const encrypted = await this.encryptData(value);
    if (encrypted) {
      localStorage.setItem(key, JSON.stringify(encrypted));
    }
  }

  async getSecureItem(key: string): Promise<string | null> {
    const stored = localStorage.getItem(key);
    if (!stored) return null;

    try {
      const { encrypted, iv } = JSON.parse(stored);
      return await this.decryptData(encrypted, iv);
    } catch (error) {
      console.error("Failed to decrypt stored item:", error);
      return null;
    }
  }

  removeSecureItem(key: string): void {
    localStorage.removeItem(key);
  }

  // Generate secure random tokens
  generateSecureToken(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  // Hash sensitive data
  async hashData(data: string, salt?: string): Promise<string> {
    const encoder = new TextEncoder();
    const saltBytes = salt
      ? encoder.encode(salt)
      : crypto.getRandomValues(new Uint8Array(16));
    const dataBytes = encoder.encode(data);

    const combined = new Uint8Array(dataBytes.length + saltBytes.length);
    combined.set(dataBytes);
    combined.set(saltBytes, dataBytes.length);

    const hashBuffer = await crypto.subtle.digest("SHA-256", combined);
    const hashArray = Array.from(new Uint8Array(hashBuffer));

    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  }
}

export const encryptionManager = EncryptionManager.getInstance();
