import * as React from "react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  AlertCircle,
  XCircle,
  Clock,
  Wifi,
  WifiOff,
} from "lucide-react";

interface StatusIndicatorProps {
  status: "online" | "offline" | "error" | "warning" | "pending" | "success";
  label?: string;
  size?: "sm" | "md" | "lg";
  variant?: "dot" | "badge" | "icon";
  className?: string;
  showLabel?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  size = "md",
  variant = "dot",
  className,
  showLabel = true,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case "online":
        return {
          color: "bg-green-500",
          textColor: "text-green-600",
          icon: <Wifi className="h-4 w-4" />,
          badgeVariant: "default" as const,
          label: label || "Online",
        };
      case "offline":
        return {
          color: "bg-gray-400",
          textColor: "text-gray-600",
          icon: <WifiOff className="h-4 w-4" />,
          badgeVariant: "secondary" as const,
          label: label || "Offline",
        };
      case "error":
        return {
          color: "bg-red-500",
          textColor: "text-red-600",
          icon: <XCircle className="h-4 w-4" />,
          badgeVariant: "destructive" as const,
          label: label || "Error",
        };
      case "warning":
        return {
          color: "bg-yellow-500",
          textColor: "text-yellow-600",
          icon: <AlertCircle className="h-4 w-4" />,
          badgeVariant: "outline" as const,
          label: label || "Warning",
        };
      case "pending":
        return {
          color: "bg-blue-500",
          textColor: "text-blue-600",
          icon: <Clock className="h-4 w-4" />,
          badgeVariant: "outline" as const,
          label: label || "Pending",
        };
      case "success":
        return {
          color: "bg-green-500",
          textColor: "text-green-600",
          icon: <CheckCircle className="h-4 w-4" />,
          badgeVariant: "default" as const,
          label: label || "Success",
        };
      default:
        return {
          color: "bg-gray-400",
          textColor: "text-gray-600",
          icon: <Clock className="h-4 w-4" />,
          badgeVariant: "secondary" as const,
          label: label || "Unknown",
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "h-2 w-2";
      case "lg":
        return "h-4 w-4";
      default:
        return "h-3 w-3";
    }
  };

  const config = getStatusConfig();

  if (variant === "badge") {
    return (
      <Badge
        variant={config.badgeVariant}
        className={cn("flex items-center gap-1", className)}
      >
        <div className={cn("rounded-full", getSizeClasses(), config.color)} />
        {showLabel && config.label}
      </Badge>
    );
  }

  if (variant === "icon") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className={cn(config.textColor)}>{config.icon}</div>
        {showLabel && (
          <span className={cn("text-sm font-medium", config.textColor)}>
            {config.label}
          </span>
        )}
      </div>
    );
  }

  // Default dot variant
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div
        className={cn(
          "rounded-full animate-pulse",
          getSizeClasses(),
          config.color
        )}
      />
      {showLabel && (
        <span className={cn("text-sm font-medium", config.textColor)}>
          {config.label}
        </span>
      )}
    </div>
  );
};

export { StatusIndicator };
