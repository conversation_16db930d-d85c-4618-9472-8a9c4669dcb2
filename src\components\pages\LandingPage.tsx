import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Activity,
  Code,
  Users,
  CheckCircle,
  Star,
  Linkedin,
  Play,
  Sparkles,
  TrendingUp,
  Lock,
  Layers,
  Workflow,
  ChevronRight,
  Cpu,
  Network,
  Timer,
  DollarSign,
  Eye,
  Gauge,
} from "lucide-react";
import ThemeToggle from "@/components/ui/theme-toggle";

interface LandingPageProps {
  onGetStarted?: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({
  onGetStarted = () => { },
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 6);
    }, 3000);
    return () => clearInterval(interval);
  }, []);
  const coreFeatures = [
    {
      icon: <Network className="h-6 w-6" />,
      title: "Intelligent Provider Routing",
      description: "AI-powered routing across OpenAI, Anthropic, Google, and more based on real-time performance metrics",
      gradient: "from-blue-500 to-cyan-500",
      stats: "40% cost reduction",
    },
    {
      icon: <Workflow className="h-6 w-6" />,
      title: "Session-Aware Memory Engine",
      description: "Redis-backed state management with TTL, continuity, and replay support for seamless agent interactions",
      gradient: "from-purple-500 to-pink-500",
      stats: "99.9% uptime",
    },
    {
      icon: <Lock className="h-6 w-6" />,
      title: "Multi-Tenant Security",
      description: "Enterprise-grade RBAC with JWT authentication scoped by tenant, project, and application",
      gradient: "from-green-500 to-emerald-500",
      stats: "SOC 2 compliant",
    },
    {
      icon: <Layers className="h-6 w-6" />,
      title: "Widget Integration Layer",
      description: "Embed-ready scripts with domain restrictions, style configuration, and live preview functionality",
      gradient: "from-orange-500 to-red-500",
      stats: "5min setup",
    },
    {
      icon: <Eye className="h-6 w-6" />,
      title: "Real-Time Analytics",
      description: "Comprehensive monitoring with cost tracking, performance metrics, and intelligent insights",
      gradient: "from-indigo-500 to-purple-500",
      stats: "Live monitoring",
    },
    {
      icon: <Cpu className="h-6 w-6" />,
      title: "Tool Execution Framework",
      description: "Declarative tool definitions with internal/external API orchestration and fallback logic",
      gradient: "from-teal-500 to-blue-500",
      stats: "100+ integrations",
    },
  ];

  const providers = [
    { name: "OpenAI", logo: "🤖", status: "active" },
    { name: "Anthropic", logo: "🧠", status: "active" },
    { name: "Google", logo: "🔍", status: "active" },
    { name: "Mistral", logo: "⚡", status: "active" },
    { name: "Groq", logo: "🚀", status: "active" },
  ];

  const metrics = [
    { label: "API Calls", value: "2.4M+", icon: <Activity className="h-5 w-5" /> },
    { label: "Cost Saved", value: "$180K+", icon: <DollarSign className="h-5 w-5" /> },
    { label: "Response Time", value: "120ms", icon: <Timer className="h-5 w-5" /> },
    { label: "Uptime", value: "99.99%", icon: <Gauge className="h-5 w-5" /> },
  ];

  const testimonials = [
    {
      name: "Alex Chen",
      role: "VP Engineering at DataFlow",
      company: "DataFlow",
      content: "SynapseAI's intelligent routing reduced our AI costs by 45% while improving response quality. The multi-tenant security gives us enterprise-grade confidence.",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=alex",
      rating: 5,
    },
    {
      name: "Maria Rodriguez",
      role: "CTO at InnovateLabs",
      company: "InnovateLabs",
      content: "The session-aware memory engine is a game-changer. Our agents now maintain context across conversations seamlessly. Implementation took just 2 hours.",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=maria",
      rating: 5,
    },
    {
      name: "David Kim",
      role: "Lead AI Engineer at TechScale",
      company: "TechScale",
      content: "Widget integration layer made embedding AI capabilities into our platform effortless. The domain restrictions and security features are exactly what we needed.",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=david",
      rating: 5,
    },
  ];

  const pricingPlans = [
    {
      name: "Developer",
      price: "Free",
      period: "Forever",
      description: "Perfect for developers and small projects",
      features: [
        "5,000 requests/month",
        "3 AI providers",
        "Basic routing",
        "Community support",
        "Standard analytics",
      ],
      popular: false,
      cta: "Start Building",
    },
    {
      name: "Professional",
      price: "$99",
      period: "/month",
      description: "Ideal for growing teams and production apps",
      features: [
        "100,000 requests/month",
        "All AI providers",
        "Intelligent routing",
        "Priority support",
        "Advanced analytics",
        "Multi-tenant security",
        "Session management",
        "Widget integration",
      ],
      popular: true,
      cta: "Start Free Trial",
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "For large-scale applications and organizations",
      features: [
        "Unlimited requests",
        "All features included",
        "Dedicated support",
        "Custom integrations",
        "SLA guarantees",
        "On-premise deployment",
        "Custom security",
        "White-label options",
      ],
      popular: false,
      cta: "Contact Sales",
    },
  ];

  return (
    <div className="min-h-screen bg-background text-foreground overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white to-purple-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Header */}
      <header className="relative border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
        <div className="container mx-auto px-6 h-20 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 h-10 w-10 rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            </div>
            <div>
              <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                SynapseAI
              </span>
              <div className="text-xs text-muted-foreground font-medium">Universal AI Orchestration</div>
            </div>
          </div>

          <nav className="hidden lg:flex items-center space-x-8">
            <a href="#features" className="text-sm font-medium hover:text-primary transition-colors relative group">
              Features
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all group-hover:w-full" />
            </a>
            <a href="#solutions" className="text-sm font-medium hover:text-primary transition-colors relative group">
              Solutions
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all group-hover:w-full" />
            </a>
            <a href="#pricing" className="text-sm font-medium hover:text-primary transition-colors relative group">
              Pricing
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all group-hover:w-full" />
            </a>
            <a href="#docs" className="text-sm font-medium hover:text-primary transition-colors relative group">
              Documentation
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all group-hover:w-full" />
            </a>
          </nav>

          <div className="flex items-center space-x-3">
            <ThemeToggle variant="button" size="sm" />
            <Button variant="ghost" size="sm" className="hidden sm:flex">
              Sign In
            </Button>
            <Button size="sm" onClick={onGetStarted} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 px-6 overflow-hidden">
        <div className="container mx-auto">
          <div className="text-center max-w-5xl mx-auto">
            {/* Announcement Badge */}
            <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 border border-blue-200/50 dark:border-blue-800/50 mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                🚀 Production Ready • Multi-Tenant Security • Enterprise Grade
              </span>
              <ChevronRight className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>

            {/* Main Headline */}
            <h1 className={`text-5xl md:text-7xl lg:text-8xl font-bold mb-8 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <span className="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight">
                Universal AI
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Orchestration
              </span>
            </h1>

            {/* Subtitle */}
            <p className={`text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              Intelligently route requests across <span className="font-semibold text-foreground">OpenAI, Anthropic, Google & more</span> with
              <span className="font-semibold text-blue-600"> session-aware memory</span>,
              <span className="font-semibold text-purple-600"> multi-tenant security</span>, and
              <span className="font-semibold text-green-600"> widget integration</span> — all through a single TypeScript SDK.
            </p>

            {/* CTA Buttons */}
            <div className={`flex flex-col sm:flex-row gap-4 justify-center mb-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <Button
                size="lg"
                onClick={onGetStarted}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 text-lg px-8 py-6 h-auto group"
              >
                Start Building Free
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 hover:bg-muted/50 text-lg px-8 py-6 h-auto group"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>

            {/* Live Metrics */}
            <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              {metrics.map((metric, index) => (
                <div key={index} className="text-center group">
                  <div className="flex items-center justify-center mb-2 text-muted-foreground group-hover:text-primary transition-colors">
                    {metric.icon}
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-foreground mb-1">{metric.value}</div>
                  <div className="text-sm text-muted-foreground">{metric.label}</div>
                </div>
              ))}
            </div>

            {/* Provider Logos */}
            <div className={`transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <p className="text-sm text-muted-foreground mb-6 font-medium">Trusted by teams using</p>
              <div className="flex items-center justify-center gap-8 md:gap-12 flex-wrap">
                {providers.map((provider, index) => (
                  <div key={index} className="flex items-center gap-3 px-4 py-2 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-300">
                    <span className="text-2xl">{provider.logo}</span>
                    <span className="font-medium text-sm">{provider.name}</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 px-6 bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-900/50 dark:to-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-20">
            <Badge className="mb-6 px-4 py-2 text-sm font-medium bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 border border-blue-200/50 dark:border-blue-800/50 text-blue-700 dark:text-blue-300">
              ⚡ Production-Ready Features
            </Badge>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Enterprise-Grade AI
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Orchestration Platform
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Built for scale, security, and performance. Everything you need to deploy AI at enterprise level with confidence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
            {coreFeatures.map((feature, index) => (
              <Card
                key={index}
                className={`group relative overflow-hidden border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 ${index === activeFeature ? 'ring-2 ring-blue-500/50 shadow-xl' : ''
                  }`}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                <CardHeader className="relative">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br ${feature.gradient} mb-4 shadow-lg`}>
                    <div className="text-white">
                      {feature.icon}
                    </div>
                  </div>
                  <CardTitle className="text-xl font-bold mb-2">{feature.title}</CardTitle>
                  <Badge variant="secondary" className="w-fit text-xs font-medium">
                    {feature.stats}
                  </Badge>
                </CardHeader>
                <CardContent className="relative">
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                  <div className="mt-4 flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Learn more
                    <ArrowRight className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 px-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 to-purple-50/30 dark:from-blue-950/20 dark:to-purple-950/20" />
        <div className="container mx-auto relative">
          <div className="text-center mb-20">
            <Badge className="mb-6 px-4 py-2 text-sm font-medium bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/50 dark:to-blue-950/50 border border-green-200/50 dark:border-green-800/50 text-green-700 dark:text-green-300">
              🚀 Simple Integration
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Get Started in
              </span>
              <br />
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                3 Simple Steps
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              From setup to production in minutes. Our intelligent platform handles the complexity while you focus on building.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                step: "01",
                title: "Connect & Configure",
                description: "Add your AI provider credentials with enterprise-grade security. Configure routing rules, set budgets, and define performance thresholds.",
                icon: <Network className="h-8 w-8" />,
                gradient: "from-blue-500 to-cyan-500",
                features: ["Multi-provider setup", "Security encryption", "Custom routing rules"]
              },
              {
                step: "02",
                title: "Deploy & Monitor",
                description: "Deploy your AI agents with session-aware memory and real-time monitoring. Track performance, costs, and user interactions across all providers.",
                icon: <Gauge className="h-8 w-8" />,
                gradient: "from-purple-500 to-pink-500",
                features: ["Real-time monitoring", "Session management", "Cost tracking"]
              },
              {
                step: "03",
                title: "Scale & Optimize",
                description: "Use our TypeScript SDK or embed widgets anywhere. Automatic scaling, intelligent routing, and continuous optimization keep your AI running smoothly.",
                icon: <TrendingUp className="h-8 w-8" />,
                gradient: "from-green-500 to-emerald-500",
                features: ["Auto-scaling", "Widget embedding", "Performance optimization"]
              }
            ].map((step, index) => (
              <Card key={index} className="group relative overflow-hidden border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className={`absolute inset-0 bg-gradient-to-br ${step.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                <CardHeader className="relative text-center">
                  <div className="relative mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${step.gradient} shadow-lg mb-4`}>
                      <div className="text-white">
                        {step.icon}
                      </div>
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-gray-800 to-gray-600 dark:from-white dark:to-gray-200 rounded-full flex items-center justify-center text-white dark:text-gray-800 text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold mb-4">{step.title}</CardTitle>
                </CardHeader>
                <CardContent className="relative text-center">
                  <p className="text-muted-foreground leading-relaxed mb-6">{step.description}</p>
                  <div className="space-y-2">
                    {step.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center justify-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-16">
            <Button
              size="lg"
              onClick={onGetStarted}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 shadow-xl text-lg px-8 py-6 h-auto group"
            >
              Start Your Integration
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 px-6 bg-gradient-to-b from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-20">
            <Badge className="mb-6 px-4 py-2 text-sm font-medium bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/50 dark:to-orange-950/50 border border-yellow-200/50 dark:border-yellow-800/50 text-yellow-700 dark:text-yellow-300">
              ⭐ Customer Success Stories
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Trusted by Teams
              </span>
              <br />
              <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                Building the Future
              </span>
            </h2>
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <span className="text-lg font-semibold text-foreground ml-3">4.9/5</span>
              <span className="text-muted-foreground">from 1,200+ developers</span>
            </div>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              See how leading teams are transforming their AI operations with SynapseAI's enterprise-grade platform.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="group relative overflow-hidden border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-orange-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <CardContent className="relative p-8">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <blockquote className="text-lg text-muted-foreground leading-relaxed mb-6 italic">
                    "{testimonial.content}"
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full ring-2 ring-gray-200 dark:ring-gray-700"
                    />
                    <div>
                      <div className="font-bold text-foreground">{testimonial.name}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      <div className="text-xs font-medium text-blue-600 dark:text-blue-400">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <div className="inline-flex items-center gap-8 px-8 py-4 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">1,200+</div>
                <div className="text-sm text-muted-foreground">Developers</div>
              </div>
              <div className="w-px h-8 bg-gray-300 dark:bg-gray-600" />
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">50+</div>
                <div className="text-sm text-muted-foreground">Companies</div>
              </div>
              <div className="w-px h-8 bg-gray-300 dark:bg-gray-600" />
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">99.9%</div>
                <div className="text-sm text-muted-foreground">Uptime</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 px-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-50/30 to-blue-50/30 dark:from-purple-950/20 dark:to-blue-950/20" />
        <div className="container mx-auto relative">
          <div className="text-center mb-20">
            <Badge className="mb-6 px-4 py-2 text-sm font-medium bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/50 dark:to-blue-950/50 border border-purple-200/50 dark:border-purple-800/50 text-purple-700 dark:text-purple-300">
              💎 Transparent Pricing
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                Scale with Confidence
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Pay as You Grow
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Start free, scale seamlessly. No hidden fees, no vendor lock-in. Enterprise-grade features available at every tier.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`group relative overflow-hidden border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm transition-all duration-500 hover:-translate-y-2 ${plan.popular
                  ? "ring-2 ring-purple-500/50 shadow-2xl scale-105"
                  : "hover:shadow-2xl"
                  }`}
              >
                {plan.popular && (
                  <div className="absolute -top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500" />
                )}
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 shadow-lg">
                    ⭐ Most Popular
                  </Badge>
                )}

                <CardHeader className="text-center relative pt-8">
                  <CardTitle className="text-2xl font-bold mb-2">{plan.name}</CardTitle>
                  <div className="mb-4">
                    <span className="text-5xl font-bold text-foreground">{plan.price}</span>
                    <span className="text-lg text-muted-foreground ml-1">{plan.period}</span>
                  </div>
                  <p className="text-muted-foreground leading-relaxed">{plan.description}</p>
                </CardHeader>

                <CardContent className="relative">
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground leading-relaxed">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className={`w-full h-12 text-base font-medium transition-all duration-300 ${plan.popular
                      ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 shadow-lg"
                      : "border-2 hover:bg-muted/50"
                      }`}
                    variant={plan.popular ? "default" : "outline"}
                    onClick={onGetStarted}
                  >
                    {plan.cta}
                    {plan.popular && <ArrowRight className="ml-2 h-4 w-4" />}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <div className="inline-flex items-center gap-6 px-8 py-4 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium">14-day free trial</span>
              </div>
              <div className="w-px h-4 bg-gray-300 dark:bg-gray-600" />
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium">No credit card required</span>
              </div>
              <div className="w-px h-4 bg-gray-300 dark:bg-gray-600" />
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium">Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-6 relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10" />
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-400/5 rounded-full blur-3xl" />
        </div>
        <div className="container mx-auto text-center relative">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-8 px-6 py-3 text-base font-medium bg-white/10 backdrop-blur-sm border border-white/20 text-white">
              🚀 Ready to Scale Your AI Operations?
            </Badge>

            <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                Transform Your AI
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">
                Operations Today
              </span>
            </h2>

            <p className="text-xl md:text-2xl mb-12 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Join <span className="font-bold text-blue-300">1,200+ developers</span> and <span className="font-bold text-purple-300">50+ companies</span> building the future of AI with enterprise-grade orchestration, security, and performance.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button
                size="lg"
                onClick={onGetStarted}
                className="bg-white text-gray-900 hover:bg-gray-100 border-0 shadow-2xl text-lg px-10 py-6 h-auto font-semibold group"
              >
                Start Building Free
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm text-lg px-10 py-6 h-auto font-semibold group"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-300 mb-2">5 min</div>
                <div className="text-white/80">Setup Time</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-300 mb-2">40%</div>
                <div className="text-white/80">Cost Reduction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-300 mb-2">99.9%</div>
                <div className="text-white/80">Uptime SLA</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-6 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 border-t border-gray-200 dark:border-gray-800">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="relative">
                  <div className="bg-gradient-to-br from-blue-600 to-purple-600 h-12 w-12 rounded-2xl flex items-center justify-center shadow-lg">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
                </div>
                <div>
                  <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                    SynapseAI
                  </span>
                  <div className="text-sm text-muted-foreground font-medium">Universal AI Orchestration</div>
                </div>
              </div>
              <p className="text-muted-foreground leading-relaxed mb-6 max-w-md">
                Enterprise-grade AI orchestration platform with intelligent routing, session-aware memory, and multi-tenant security. Built for scale, designed for developers.
              </p>
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="icon" className="hover:bg-blue-50 dark:hover:bg-blue-950/50">
                  <Code className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-blue-50 dark:hover:bg-blue-950/50">
                  <Users className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-blue-50 dark:hover:bg-blue-950/50">
                  <Linkedin className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="font-bold text-foreground mb-6">Product</h3>
              <ul className="space-y-4 text-sm">
                {[
                  { name: "Features", href: "#features" },
                  { name: "Pricing", href: "#pricing" },
                  { name: "API Reference", href: "#" },
                  { name: "Integrations", href: "#" },
                  { name: "Changelog", href: "#" },
                ].map((link) => (
                  <li key={link.name}>
                    <a href={link.href} className="text-muted-foreground hover:text-primary transition-colors duration-200 flex items-center group">
                      {link.name}
                      <ChevronRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources Links */}
            <div>
              <h3 className="font-bold text-foreground mb-6">Resources</h3>
              <ul className="space-y-4 text-sm">
                {[
                  { name: "Documentation", href: "#" },
                  { name: "Tutorials", href: "#" },
                  { name: "Examples", href: "#" },
                  { name: "Community", href: "#" },
                  { name: "Support", href: "#" },
                ].map((link) => (
                  <li key={link.name}>
                    <a href={link.href} className="text-muted-foreground hover:text-primary transition-colors duration-200 flex items-center group">
                      {link.name}
                      <ChevronRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="font-bold text-foreground mb-6">Company</h3>
              <ul className="space-y-4 text-sm">
                {[
                  { name: "About", href: "#" },
                  { name: "Blog", href: "#" },
                  { name: "Careers", href: "#" },
                  { name: "Contact", href: "#" },
                  { name: "Privacy", href: "#" },
                ].map((link) => (
                  <li key={link.name}>
                    <a href={link.href} className="text-muted-foreground hover:text-primary transition-colors duration-200 flex items-center group">
                      {link.name}
                      <ChevronRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-200 dark:border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>© 2024 SynapseAI. All rights reserved.</span>
                <div className="hidden md:flex items-center gap-4">
                  <a href="#" className="hover:text-primary transition-colors">Terms</a>
                  <a href="#" className="hover:text-primary transition-colors">Privacy</a>
                  <a href="#" className="hover:text-primary transition-colors">Security</a>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Built with</span>
                <span className="text-red-500">❤️</span>
                <span>for developers</span>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse ml-2" />
                <span className="text-green-600 dark:text-green-400 font-medium">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
