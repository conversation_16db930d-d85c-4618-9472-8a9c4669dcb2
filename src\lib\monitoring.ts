
import { getLogger } from "./logger";
import { getConfig } from "./config";
import { getErrorHand<PERSON> } from "./errorHandler";

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags?: Record<string, string>;
}

export interface HealthCheck {
  service: string;
  status: "healthy" | "degraded" | "unhealthy";
  latency?: number;
  error?: string;
  timestamp: string;
}

export interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  timestamp: string;
}

class MonitoringService {
  private logger = getLogger().createChild("Monitoring");
  private config = getConfig();
  private errorHandler = getErrorHandler();
  private metrics: PerformanceMetric[] = [];
  private healthChecks: HealthCheck[] = [];
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = this.config.canUseMonitoring();

    if (this.isEnabled) {
      this.startPerformanceMonitoring();
      this.startHealthChecks();
    }
  }

  private startPerformanceMonitoring(): void {
    // Monitor page load performance
    if (typeof window !== "undefined" && window.performance) {
      window.addEventListener("load", () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType(
            "navigation"
          )[0] as PerformanceNavigationTiming;

          this.recordMetric({
            name: "page_load_time",
            value: navigation.loadEventEnd - navigation.fetchStart,
            unit: "ms",
            timestamp: new Date().toISOString(),
          });

          this.recordMetric({
            name: "dom_content_loaded",
            value: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            unit: "ms",
            timestamp: new Date().toISOString(),
          });
        }, 0);
      });

      // Monitor resource loading
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "resource") {
            const resourceEntry = entry as PerformanceResourceTiming;

            this.recordMetric({
              name: "resource_load_time",
              value: resourceEntry.responseEnd - resourceEntry.fetchStart,
              unit: "ms",
              timestamp: new Date().toISOString(),
              tags: {
                resource: resourceEntry.name,
                type: resourceEntry.initiatorType,
              },
            });
          }
        }
      });

      observer.observe({ entryTypes: ["resource"] });
    }
  }

  private startHealthChecks(): void {
    // Periodic health checks
    setInterval(async () => {
      await this.performHealthChecks();
    }, 30000); // Every 30 seconds
  }

  private async performHealthChecks(): Promise<void> {
    const checks = [
      this.checkAPIHealth(),
      this.checkDatabaseHealth(),
      this.checkMemoryUsage(),
    ];

    const results = await Promise.allSettled(checks);

    results.forEach((result, index) => {
      if (result.status === "fulfilled") {
        this.healthChecks.push(result.value);
      } else {
        this.logger.error(`Health check ${index} failed`, {
          error: result.reason,
        });
      }
    });

    // Keep only last 100 health checks
    if (this.healthChecks.length > 100) {
      this.healthChecks = this.healthChecks.slice(-100);
    }
  }

  private async checkAPIHealth(): Promise<HealthCheck> {
    const startTime = Date.now();

    try {
      const response = await fetch(`${this.config.getApiBaseUrl()}/health`, {
        method: "GET",
        timeout: 5000,
      } as any);

      const latency = Date.now() - startTime;

      return {
        service: "api",
        status: response.ok ? "healthy" : "degraded",
        latency,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        service: "api",
        status: "unhealthy",
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async checkDatabaseHealth(): Promise<HealthCheck> {
    try {
      // This would check database connectivity
      // For now, we'll simulate a check
      const startTime = Date.now();

      // Simulate database ping
      await new Promise((resolve) => setTimeout(resolve, Math.random() * 100));

      const latency = Date.now() - startTime;

      return {
        service: "database",
        status: "healthy",
        latency,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        service: "database",
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async checkMemoryUsage(): Promise<HealthCheck> {
    try {
      if (typeof window !== "undefined" && "memory" in performance) {
        const memory = (performance as any).memory;
        const usedPercent =
          (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100;

        return {
          service: "memory",
          status:
            usedPercent > 90
              ? "degraded"
              : usedPercent > 95
                ? "unhealthy"
                : "healthy",
          timestamp: new Date().toISOString(),
        };
      }

      return {
        service: "memory",
        status: "healthy",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        service: "memory",
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
    }
  }

  recordMetric(metric: PerformanceMetric): void {
    if (!this.isEnabled) return;

    this.metrics.push(metric);

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    this.logger.debug("Metric recorded", { metric });

    // Send to remote monitoring if configured
    if (this.config.isProduction()) {
      this.sendMetricToRemote(metric);
    }
  }

  private async sendMetricToRemote(metric: PerformanceMetric): Promise<void> {
    try {
      await fetch(`${this.config.getApiBaseUrl()}/metrics`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(metric),
      });
    } catch (error) {
      // Silently fail remote metric sending
      this.logger.warn("Failed to send metric to remote", { error });
    }
  }

  getMetrics(name?: string, limit?: number): PerformanceMetric[] {
    let filteredMetrics = this.metrics;

    if (name) {
      filteredMetrics = this.metrics.filter((m) => m.name === name);
    }

    if (limit) {
      filteredMetrics = filteredMetrics.slice(-limit);
    }

    return [...filteredMetrics];
  }

  getHealthStatus(): {
    overall: "healthy" | "degraded" | "unhealthy";
    services: Record<string, HealthCheck>;
  } {
    const latestChecks: Record<string, HealthCheck> = {};

    // Get latest check for each service
    this.healthChecks.forEach((check) => {
      if (
        !latestChecks[check.service] ||
        new Date(check.timestamp) >
          new Date(latestChecks[check.service].timestamp)
      ) {
        latestChecks[check.service] = check;
      }
    });

    // Determine overall status
    const statuses = Object.values(latestChecks).map((check) => check.status);
    let overall: "healthy" | "degraded" | "unhealthy" = "healthy";

    if (statuses.includes("unhealthy")) {
      overall = "unhealthy";
    } else if (statuses.includes("degraded")) {
      overall = "degraded";
    }

    return {
      overall,
      services: latestChecks,
    };
  }

  getPerformanceSummary(): {
    averagePageLoadTime: number;
    averageAPIResponseTime: number;
    errorRate: number;
    totalRequests: number;
  } {
    const pageLoadMetrics = this.metrics.filter(
      (m) => m.name === "page_load_time"
    );
    const apiMetrics = this.metrics.filter(
      (m) => m.name === "api_response_time"
    );
    const errorMetrics = this.metrics.filter((m) => m.name === "error_count");
    const requestMetrics = this.metrics.filter(
      (m) => m.name === "request_count"
    );

    return {
      averagePageLoadTime:
        pageLoadMetrics.length > 0
          ? pageLoadMetrics.reduce((sum, m) => sum + m.value, 0) /
            pageLoadMetrics.length
          : 0,
      averageAPIResponseTime:
        apiMetrics.length > 0
          ? apiMetrics.reduce((sum, m) => sum + m.value, 0) / apiMetrics.length
          : 0,
      errorRate:
        requestMetrics.length > 0
          ? (errorMetrics.reduce((sum, m) => sum + m.value, 0) /
              requestMetrics.reduce((sum, m) => sum + m.value, 0)) *
            100
          : 0,
      totalRequests: requestMetrics.reduce((sum, m) => sum + m.value, 0),
    };
  }

  // Measure function execution time
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await fn();

      this.recordMetric({
        name,
        value: Date.now() - startTime,
        unit: "ms",
        timestamp: new Date().toISOString(),
        tags: { status: "success" },
      });

      return result;
    } catch (error) {
      this.recordMetric({
        name,
        value: Date.now() - startTime,
        unit: "ms",
        timestamp: new Date().toISOString(),
        tags: { status: "error" },
      });

      throw error;
    }
  }

  measureSync<T>(name: string, fn: () => T): T {
    const startTime = Date.now();

    try {
      const result = fn();

      this.recordMetric({
        name,
        value: Date.now() - startTime,
        unit: "ms",
        timestamp: new Date().toISOString(),
        tags: { status: "success" },
      });

      return result;
    } catch (error) {
      this.recordMetric({
        name,
        value: Date.now() - startTime,
        unit: "ms",
        timestamp: new Date().toISOString(),
        tags: { status: "error" },
      });

      throw error;
    }
  }
}

// Global monitoring service instance
let monitoringService: MonitoringService | null = null;

export function getMonitoringService(): MonitoringService {
  if (!monitoringService) {
    monitoringService = new MonitoringService();
  }
  return monitoringService;
}

export { MonitoringService };
