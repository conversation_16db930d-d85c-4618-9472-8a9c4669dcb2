import React, { ReactElement } from 'react';
import { render as rtlRender } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { ErrorBoundary } from '@/lib/errorHandler';

// Mock API responses
export const mockApiResponse = {
    providers: [
        {
            id: 'test-provider-1',
            name: 'Test Provider',
            type: 'openai',
            status: 'active',
            apiKey: 'test-key',
            models: ['test-model'],
            config: {
                maxTokens: 1000,
                temperature: 0.7,
                timeout: 30000,
            },
            metrics: {
                requests: 100,
                successRate: 98,
                avgLatency: 500,
                cost: 0.5,
            },
        },
    ],
    agents: [
        {
            id: 'test-agent-1',
            name: 'Test Agent',
            description: 'Test Description',
            type: 'chatbot',
            status: 'active',
            version: '1.0.0',
            provider: 'test-provider-1',
            model: 'test-model',
            systemPrompt: 'You are a test agent',
            config: {
                temperature: 0.7,
                maxTokens: 1000,
                topP: 1,
                frequencyPenalty: 0,
            },
            metrics: {
                totalInteractions: 100,
                successRate: 98,
                avgResponseTime: 500,
            },
        },
    ],
};

// Test wrapper with common providers
interface WrapperProps {
    children: React.ReactNode;
}

export function TestWrapper({ children }: WrapperProps) {
    return (
        <BrowserRouter>
            <ErrorBoundary>
                {children}
            </ErrorBoundary>
        </BrowserRouter>
    );
}

// Custom render function with wrapper
export function render(ui: ReactElement, options = {}) {
    return rtlRender(ui, { wrapper: TestWrapper, ...options });
}

// Mock fetch responses
export function mockFetch(data: any) {
    return vi.spyOn(global, 'fetch').mockImplementation(() =>
        Promise.resolve({
            ok: true,
            json: () => Promise.resolve(data),
            status: 200,
            statusText: 'OK',
            headers: new Headers(),
        } as Response)
    );
}

// Mock error responses
export function mockFetchError(status = 500, message = 'Internal Server Error') {
    return vi.spyOn(global, 'fetch').mockImplementation(() =>
        Promise.reject(new Error(message))
    );
}

// Database test utilities
export const dbTestUtils = {
    async cleanDatabase() {
        // Implementation would go here in a real app
        console.log('Cleaning test database...');
    },

    async seedDatabase() {
        // Implementation would go here in a real app
        console.log('Seeding test database...');
    },
};

// Authentication test utilities
export const authTestUtils = {
    mockAuthenticatedUser: {
        id: 'test-user-1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'admin' as const,
        createdAt: new Date().toISOString(),
    },

    mockAuthToken: 'test-auth-token',

    mockLogin() {
        localStorage.setItem('authToken', this.mockAuthToken);
        return Promise.resolve(this.mockAuthenticatedUser);
    },

    mockLogout() {
        localStorage.removeItem('authToken');
        return Promise.resolve();
    },
};

// Event test utilities
export const eventTestUtils = {
    mockEvent(type: string, data: any) {
        return new CustomEvent(type, { detail: data });
    },

    dispatchMockEvent(type: string, data: any) {
        const event = this.mockEvent(type, data);
        window.dispatchEvent(event);
        return event;
    },
}; 