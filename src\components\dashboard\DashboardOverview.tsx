import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { MetricCard } from "@/components/ui/metric-card";
import { StatusIndicator } from "@/components/ui/status-indicator";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
} from "recharts";
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Users,
  Zap,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  Brain,
  Bot,
  Server,
  Eye,
  RefreshCw,
} from "lucide-react";
import { getSynapseAI } from "@/lib/sdk";

interface DashboardOverviewProps {
  refreshInterval?: number;
}

interface SystemMetrics {
  totalRequests: number;
  successRate: number;
  averageLatency: number;
  totalCost: number;
  activeProviders: number;
  activeAgents: number;
  eventsToday: number;
  errorRate: number;
}

interface RealtimeData {
  timestamp: string;
  requests: number;
  latency: number;
  errors: number;
}

const DashboardOverview: React.FC<DashboardOverviewProps> = ({
  refreshInterval = 30000,
}) => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalRequests: 12450,
    successRate: 97.2,
    averageLatency: 1250,
    totalCost: 45.67,
    activeProviders: 4,
    activeAgents: 3,
    eventsToday: 156,
    errorRate: 2.8,
  });

  const [realtimeData, setRealtimeData] = useState<RealtimeData[]>([
    {
      timestamp: new Date(Date.now() - 300000).toISOString(),
      requests: 45,
      latency: 1200,
      errors: 2,
    },
    {
      timestamp: new Date(Date.now() - 240000).toISOString(),
      requests: 52,
      latency: 1100,
      errors: 1,
    },
    {
      timestamp: new Date(Date.now() - 180000).toISOString(),
      requests: 38,
      latency: 1350,
      errors: 3,
    },
    {
      timestamp: new Date(Date.now() - 120000).toISOString(),
      requests: 61,
      latency: 980,
      errors: 0,
    },
    {
      timestamp: new Date(Date.now() - 60000).toISOString(),
      requests: 47,
      latency: 1180,
      errors: 1,
    },
    {
      timestamp: new Date().toISOString(),
      requests: 55,
      latency: 1050,
      errors: 2,
    },
  ]);

  const [systemHealth, setSystemHealth] = useState({
    overall: "healthy",
    providers: { healthy: 3, warning: 1, error: 0 },
    agents: { active: 3, paused: 0, error: 0 },
    events: { processed: 156, failed: 4, pending: 2 },
  });

  const [isRefreshing, setIsRefreshing] = useState(false);

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      // Simulate API call to get fresh data
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update metrics with slight variations
      setMetrics((prev) => ({
        ...prev,
        totalRequests: prev.totalRequests + Math.floor(Math.random() * 10),
        successRate: Math.max(
          95,
          Math.min(99, prev.successRate + (Math.random() - 0.5) * 0.5)
        ),
        averageLatency: Math.max(
          800,
          Math.min(2000, prev.averageLatency + (Math.random() - 0.5) * 100)
        ),
        totalCost: prev.totalCost + Math.random() * 0.5,
        eventsToday: prev.eventsToday + Math.floor(Math.random() * 3),
      }));

      // Add new realtime data point
      const newDataPoint: RealtimeData = {
        timestamp: new Date().toISOString(),
        requests: Math.floor(Math.random() * 30) + 40,
        latency: Math.floor(Math.random() * 500) + 800,
        errors: Math.floor(Math.random() * 3),
      };

      setRealtimeData((prev) => [...prev.slice(-9), newDataPoint]);
    } catch (error) {
      console.error("Failed to refresh dashboard data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "error":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const COLORS = ["#10B981", "#3B82F6", "#8B5CF6", "#F59E0B", "#EF4444"];

  return (
    <div className="space-y-6 bg-gradient-surface">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gradient">
            Dashboard Overview
          </h1>
          <p className="text-muted-foreground font-medium">
            Real-time system monitoring and analytics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <StatusIndicator
            status={systemHealth.overall === "healthy" ? "online" : "error"}
            label={`System ${systemHealth.overall}`}
            variant="badge"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={isRefreshing}
          >
            <RefreshCw
              className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
            />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Requests"
          value={metrics.totalRequests.toLocaleString()}
          icon={<Zap className="h-4 w-4" />}
          status="success"
          trend="up"
          change={{
            value: 12.5,
            period: "yesterday",
          }}
        />
        <MetricCard
          title="Success Rate"
          value={`${metrics.successRate.toFixed(1)}%`}
          icon={<CheckCircle className="h-4 w-4" />}
          status="success"
          trend="up"
          change={{
            value: 0.3,
            period: "yesterday",
          }}
        />
        <MetricCard
          title="Avg Latency"
          value={`${metrics.averageLatency}ms`}
          icon={<Clock className="h-4 w-4" />}
          status="warning"
          trend="down"
          change={{
            value: -5.2,
            period: "yesterday",
          }}
        />
        <MetricCard
          title="Total Cost"
          value={`${metrics.totalCost.toFixed(2)}`}
          icon={<DollarSign className="h-4 w-4" />}
          status="info"
          trend="up"
          change={{
            value: 8.1,
            period: "yesterday",
          }}
        />
      </div>

      {/* System Health */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <EnhancedCard
          title="Providers Status"
          status="success"
          gradient
          interactive
        >
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Server className="h-4 w-4" />
              Providers Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Healthy</span>
                <Badge className="bg-green-100 text-green-800">
                  {systemHealth.providers.healthy}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Warning</span>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {systemHealth.providers.warning}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Error</span>
                <Badge className="bg-red-100 text-red-800">
                  {systemHealth.providers.error}
                </Badge>
              </div>
            </div>
          </CardContent>
        </EnhancedCard>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Agents Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Active</span>
                <Badge className="bg-green-100 text-green-800">
                  {systemHealth.agents.active}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Paused</span>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {systemHealth.agents.paused}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Error</span>
                <Badge className="bg-red-100 text-red-800">
                  {systemHealth.agents.error}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Events Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Processed</span>
                <Badge className="bg-green-100 text-green-800">
                  {systemHealth.events.processed}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Failed</span>
                <Badge className="bg-red-100 text-red-800">
                  {systemHealth.events.failed}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Pending</span>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {systemHealth.events.pending}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Real-time Request Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={realtimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={(value) =>
                    new Date(value).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  }
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) =>
                    new Date(value).toLocaleTimeString()
                  }
                  formatter={(value) => [value, "Requests"]}
                />
                <Area
                  type="monotone"
                  dataKey="requests"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Response Time Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={realtimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={(value) =>
                    new Date(value).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  }
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) =>
                    new Date(value).toLocaleTimeString()
                  }
                  formatter={(value) => [`${value}ms`, "Latency"]}
                />
                <Line
                  type="monotone"
                  dataKey="latency"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: "#10B981", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Bot className="h-6 w-6" />
              <span className="text-sm">Deploy Agent</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Server className="h-6 w-6" />
              <span className="text-sm">Add Provider</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Eye className="h-6 w-6" />
              <span className="text-sm">View Events</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
            >
              <Activity className="h-6 w-6" />
              <span className="text-sm">System Health</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardOverview;
