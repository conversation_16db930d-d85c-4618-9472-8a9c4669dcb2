import { getLogger } from "./logger";
import { getErrorHandler } from "./errorHandler";
import { getConfig } from "./config";

export interface APIClientConfig {
  baseURL: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableLogging: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId: string;
}

export interface RequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

export interface ProviderRequest {
  providerId: string;
  model: string;
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface ProviderResponse {
  id: string;
  model: string;
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
}

class APIClient {
  private config: APIClientConfig;
  private logger = getLogger().createChild("APIClient");
  private errorHandler = getErrorHandler();
  private requestCache = new Map<string, { data: any; timestamp: number }>();
  private rateLimiter = new Map<string, number[]>();

  constructor(config?: Partial<APIClientConfig>) {
    const appConfig = getConfig();

    this.config = {
      baseURL: config?.baseURL || appConfig.getApiBaseUrl(),
      apiKey: config?.apiKey || process.env.VITE_API_KEY || "",
      timeout: config?.timeout || 30000,
      retryAttempts: config?.retryAttempts || 3,
      retryDelay: config?.retryDelay || 1000,
      enableLogging: config?.enableLogging ?? true,
    };
  }

  async request<T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<APIResponse<T>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Check rate limiting
      if (!this.checkRateLimit(endpoint)) {
        throw new Error("Rate limit exceeded");
      }

      // Check cache for GET requests
      if (options.method === "GET" || !options.method) {
        const cached = this.getCachedResponse(endpoint);
        if (cached && options.cache !== false) {
          this.logger.debug("Returning cached response", {
            endpoint,
            requestId,
          });
          return cached as APIResponse<T>;
        }
      }

      const response = await this.executeRequest<T>(
        endpoint,
        options,
        requestId
      );

      // Cache successful GET responses
      if ((options.method === "GET" || !options.method) && response.success) {
        this.setCachedResponse(endpoint, response);
      }

      const duration = Date.now() - startTime;
      if (this.config.enableLogging) {
        this.logger.info("API request completed", {
          endpoint,
          method: options.method || "GET",
          duration,
          success: response.success,
          requestId,
        });
      }

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      this.errorHandler.handleError({
        message: `API request failed: ${endpoint}`,
        stack: error instanceof Error ? error.stack : undefined,
        component: "APIClient",
        severity: "medium",
        category: "api",
        metadata: {
          endpoint,
          method: options.method || "GET",
          duration,
          requestId,
          error: errorMessage,
        },
      });

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        requestId,
      };
    }
  }

  private async executeRequest<T>(
    endpoint: string,
    options: RequestOptions,
    requestId: string
  ): Promise<APIResponse<T>> {
    const url = this.buildURL(endpoint);
    const headers = this.buildHeaders(options.headers);
    const timeout = options.timeout || this.config.timeout;
    const retries = options.retries ?? this.config.retryAttempts;

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const fetchOptions: RequestInit = {
          method: options.method || "GET",
          headers,
          signal: controller.signal,
        };

        if (options.body && options.method !== "GET") {
          fetchOptions.body = JSON.stringify(options.body);
        }

        const response = await fetch(url, fetchOptions);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        return {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          requestId,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < retries && this.isRetryableError(lastError)) {
          const delay = this.config.retryDelay * Math.pow(2, attempt);
          this.logger.warn(
            `API request attempt ${attempt + 1} failed, retrying in ${delay}ms`,
            {
              endpoint,
              error: lastError.message,
              requestId,
            }
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          break;
        }
      }
    }

    throw lastError || new Error("Request failed");
  }

  private buildURL(endpoint: string): string {
    const baseURL = this.config.baseURL.replace(/\/$/, "");
    const cleanEndpoint = endpoint.replace(/^\//, "");
    return `${baseURL}/${cleanEndpoint}`;
  }

  private buildHeaders(
    customHeaders?: Record<string, string>
  ): Record<string, string> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "application/json",
      "User-Agent": "SynapseAI-Client/1.0",
    };

    if (this.config.apiKey) {
      headers["Authorization"] = `Bearer ${this.config.apiKey}`;
    }

    return { ...headers, ...customHeaders };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      "timeout",
      "network",
      "connection",
      "ECONNRESET",
      "ENOTFOUND",
      "ECONNREFUSED",
    ];

    return retryableErrors.some((retryableError) =>
      error.message.toLowerCase().includes(retryableError.toLowerCase())
    );
  }

  private checkRateLimit(endpoint: string): boolean {
    const now = Date.now();
    const windowMs = 60000; // 1 minute
    const maxRequests = 100; // 100 requests per minute

    if (!this.rateLimiter.has(endpoint)) {
      this.rateLimiter.set(endpoint, []);
    }

    const requests = this.rateLimiter.get(endpoint)!;

    // Remove old requests outside the window
    const validRequests = requests.filter(
      (timestamp) => now - timestamp < windowMs
    );

    if (validRequests.length >= maxRequests) {
      return false;
    }

    // Add current request
    validRequests.push(now);
    this.rateLimiter.set(endpoint, validRequests);

    return true;
  }

  private getCachedResponse<T>(endpoint: string): APIResponse<T> | null {
    const cached = this.requestCache.get(endpoint);
    if (!cached) {
      return null;
    }

    const maxAge = 5 * 60 * 1000; // 5 minutes
    if (Date.now() - cached.timestamp > maxAge) {
      this.requestCache.delete(endpoint);
      return null;
    }

    return cached.data;
  }

  private setCachedResponse<T>(
    endpoint: string,
    response: APIResponse<T>
  ): void {
    this.requestCache.set(endpoint, {
      data: response,
      timestamp: Date.now(),
    });

    // Cleanup old cache entries
    if (this.requestCache.size > 100) {
      const entries = Array.from(this.requestCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      // Remove oldest 20 entries
      for (let i = 0; i < 20; i++) {
        this.requestCache.delete(entries[i][0]);
      }
    }
  }

  // Convenience methods
  async get<T = any>(
    endpoint: string,
    options?: Omit<RequestOptions, "method">
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "GET" });
  }

  async post<T = any>(
    endpoint: string,
    body?: any,
    options?: Omit<RequestOptions, "method" | "body">
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "POST", body });
  }

  async put<T = any>(
    endpoint: string,
    body?: any,
    options?: Omit<RequestOptions, "method" | "body">
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "PUT", body });
  }

  async delete<T = any>(
    endpoint: string,
    options?: Omit<RequestOptions, "method">
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "DELETE" });
  }

  async patch<T = any>(
    endpoint: string,
    body?: any,
    options?: Omit<RequestOptions, "method" | "body">
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "PATCH", body });
  }

  // Configuration methods
  updateConfig(config: Partial<APIClientConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.info("API client configuration updated", { config });
  }

  getConfig(): APIClientConfig {
    return { ...this.config };
  }

  // Production-ready authentication
  async authenticate(credentials: {
    email: string;
    password: string;
  }): Promise<any> {
    try {
      const response = await this.post<any>("/api/v1/auth/login", credentials);
      if (!response.success) {
        throw new Error(response.error || "Authentication failed");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Authentication failed", { error });
      throw error;
    }
  }

  // Real provider integration
  async sendProviderRequest(
    request: ProviderRequest
  ): Promise<ProviderResponse> {
    try {
      // Validate request
      if (!request.providerId || !request.model || !request.messages) {
        throw new Error("Invalid provider request: missing required fields");
      }

      // Security: Sanitize messages
      const sanitizedMessages = request.messages.map((msg) => ({
        ...msg,
        content: this.sanitizeContent(msg.content),
      }));

      const response = await this.post<ProviderResponse>(
        `/api/v1/providers/${request.providerId}/chat`,
        {
          model: request.model,
          messages: sanitizedMessages,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 1000,
          stream: request.stream || false,
        }
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || "Provider request failed");
      }

      return response.data;
    } catch (error) {
      this.logger.error("Provider request failed", {
        providerId: request.providerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private sanitizeContent(content: string): string {
    // Remove potentially dangerous content
    return content
      .replace(/<script[^>]*>.*?<\/script>/gi, "")
      .replace(/javascript:/gi, "")
      .replace(/on\w+\s*=/gi, "")
      .trim();
  }

  // Cache management
  clearCache(): void {
    this.requestCache.clear();
    this.logger.info("API client cache cleared");
  }

  getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.requestCache.size,
      entries: Array.from(this.requestCache.keys()),
    };
  }

  // Rate limiting
  getRateLimitStats(): Record<
    string,
    { requests: number; windowStart: number }
  > {
    const stats: Record<string, { requests: number; windowStart: number }> = {};
    const now = Date.now();
    const windowMs = 60000;

    for (const [endpoint, requests] of this.rateLimiter.entries()) {
      const validRequests = requests.filter(
        (timestamp) => now - timestamp < windowMs
      );
      stats[endpoint] = {
        requests: validRequests.length,
        windowStart: Math.min(...validRequests),
      };
    }

    return stats;
  }

  // Health check
  async healthCheck(): Promise<
    APIResponse<{ status: string; timestamp: string }>
  > {
    try {
      const response = await this.get<{ status: string; timestamp: string }>(
        "/api/v1/health"
      );
      return response;
    } catch (error) {
      return {
        success: false,
        error: "Health check failed",
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
      };
    }
  }

  // Provider management
  async getProviders(): Promise<any[]> {
    try {
      const response = await this.get<any[]>("/api/v1/providers");
      return response.success ? response.data || [] : [];
    } catch (error) {
      this.logger.error("Failed to get providers", { error });
      return [];
    }
  }

  async createProvider(provider: any): Promise<any> {
    try {
      const response = await this.post<any>("/api/v1/providers", provider);
      if (!response.success) {
        throw new Error(response.error || "Failed to create provider");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Failed to create provider", { error });
      throw error;
    }
  }

  async updateProvider(id: string, updates: any): Promise<any> {
    try {
      const response = await this.put<any>(`/api/v1/providers/${id}`, updates);
      if (!response.success) {
        throw new Error(response.error || "Failed to update provider");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Failed to update provider", { error });
      throw error;
    }
  }

  async deleteProvider(id: string): Promise<boolean> {
    try {
      const response = await this.delete(`/api/v1/providers/${id}`);
      return response.success;
    } catch (error) {
      this.logger.error("Failed to delete provider", { error });
      return false;
    }
  }

  async testProvider(id: string): Promise<any> {
    try {
      const response = await this.post<any>(`/api/v1/providers/${id}/test`);
      if (!response.success) {
        throw new Error(response.error || "Provider test failed");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Provider test failed", { error });
      throw error;
    }
  }

  // Agent management
  async getAgents(): Promise<any[]> {
    try {
      const response = await this.get<any[]>("/api/v1/agents");
      return response.success ? response.data || [] : [];
    } catch (error) {
      this.logger.error("Failed to get agents", { error });
      return [];
    }
  }

  async createAgent(agent: any): Promise<any> {
    try {
      const response = await this.post<any>("/api/v1/agents", agent);
      if (!response.success) {
        throw new Error(response.error || "Failed to create agent");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Failed to create agent", { error });
      throw error;
    }
  }

  async updateAgent(id: string, updates: any): Promise<any> {
    try {
      const response = await this.put<any>(`/api/v1/agents/${id}`, updates);
      if (!response.success) {
        throw new Error(response.error || "Failed to update agent");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Failed to update agent", { error });
      throw error;
    }
  }

  async deleteAgent(id: string): Promise<boolean> {
    try {
      const response = await this.delete(`/api/v1/agents/${id}`);
      return response.success;
    } catch (error) {
      this.logger.error("Failed to delete agent", { error });
      return false;
    }
  }

  async deployAgent(id: string): Promise<any> {
    try {
      const response = await this.post<any>(`/api/v1/agents/${id}/deploy`);
      if (!response.success) {
        throw new Error(response.error || "Failed to deploy agent");
      }
      return response.data;
    } catch (error) {
      this.logger.error("Failed to deploy agent", { error });
      throw error;
    }
  }

  // Analytics
  async getAnalytics(): Promise<any> {
    try {
      const response = await this.get<any>("/api/v1/analytics/overview");
      return response.success ? response.data : null;
    } catch (error) {
      this.logger.error("Failed to get analytics", { error });
      return null;
    }
  }

  async getUsageStats(): Promise<any> {
    try {
      const response = await this.get<any>("/api/v1/analytics/usage");
      return response.success ? response.data : null;
    } catch (error) {
      this.logger.error("Failed to get usage stats", { error });
      return null;
    }
  }
}

// Use Real API Client instead of mock
import { getRealAPIClient } from "./realApiClient";

// Global API client instance
let apiClient: APIClient | null = null;

export function getAPIClient(config?: Partial<APIClientConfig>): any {
  // Return real API client for production
  return getRealAPIClient();
}

export { APIClient };
