{"name": "starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc ; vite build", "build-no-errors": "tsc ; vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "db:migrate": "node -r ts-node/register src/lib/database/migrations/cli.ts migrate", "db:rollback": "node -r ts-node/register src/lib/database/migrations/cli.ts rollback", "db:status": "node -r ts-node/register src/lib/database/migrations/cli.ts status"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/react": "^7.120.3", "@sentry/vite-plugin": "^2.23.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.18.0", "lucide-react": "^0.394.0", "pg": "^8.11.3", "pg-format": "^1.0.4", "pg-pool": "^3.6.1", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-resizable-panels": "^2.0.19", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "recharts": "^3.0.2", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "^0.9.1", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.2", "@types/pg": "^8.11.0", "@types/pg-format": "^1.0.5", "@types/pg-pool": "^2.0.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^9.0.8", "@vitejs/plugin-react-swc": "^3.8.1", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "autoprefixer": "^10.4.19", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jsdom": "^24.1.3", "lint-staged": "^15.5.2", "postcss": "^8.4.38", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "3.4.1", "tempo-devtools": "^2.0.108", "ts-node": "^10.9.2", "typescript": "^5.8.2", "vite": "^6.2.3", "vitest": "^1.6.1"}}