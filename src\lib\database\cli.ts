#!/usr/bin/env node
// Database CLI for SynapseAI

import { migrationManager } from "./migrations/index";
import { db } from "./index";

const command = process.argv[2];

async function main() {
  try {
    await db.connect();

    switch (command) {
      case "migrate":
        console.log("🔄 Running migrations...");
        await migrationManager.migrate();
        console.log("✅ Migrations completed");
        break;

      case "rollback":
        const steps = parseInt(process.argv[3]) || 1;
        console.log(`🔄 Rolling back ${steps} migration(s)...`);
        await migrationManager.rollback(steps);
        console.log("✅ Rollback completed");
        break;

      case "status":
        console.log("📊 Migration status:");
        const status = await migrationManager.status();
        status.forEach((migration) => {
          const statusIcon = migration.applied ? "✅" : "⏳";
          console.log(`${statusIcon} ${migration.id} - ${migration.name}`);
        });
        break;

      case "seed":
        console.log("🌱 Seeding database...");
        await seedDatabase();
        console.log("✅ Database seeded");
        break;

      default:
        console.log(
          "Usage: npm run db:migrate | db:rollback [steps] | db:status | db:seed"
        );
    }
  } catch (error) {
    console.error("❌ Database operation failed:", error);
    process.exit(1);
  } finally {
    await db.disconnect();
    process.exit(0);
  }
}

async function seedDatabase() {
  // Create default admin user
  const bcrypt = await import("bcryptjs");
  const hashedPassword = await bcrypt.hash("admin123", 10);

  await db.query(
    `
    INSERT INTO users (email, name, role, password_hash)
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (email) DO NOTHING
  `,
    ["<EMAIL>", "Admin User", "admin", hashedPassword]
  );

  // Create sample providers
  const providers = [
    {
      name: "OpenAI",
      type: "openai",
      api_key: "sk-demo-key",
      status: "active",
      models: ["gpt-4", "gpt-3.5-turbo"],
      config: { maxTokens: 4096, temperature: 0.7 },
    },
    {
      name: "Claude",
      type: "anthropic",
      api_key: "sk-ant-demo-key",
      status: "active",
      models: ["claude-3-opus", "claude-3-sonnet"],
      config: { maxTokens: 8192, temperature: 0.5 },
    },
  ];

  for (const provider of providers) {
    await db.query(
      `
      INSERT INTO providers (name, type, api_key, status, models, config)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (name) DO NOTHING
    `,
      [
        provider.name,
        provider.type,
        provider.api_key,
        provider.status,
        provider.models,
        JSON.stringify(provider.config),
      ]
    );
  }

  console.log("✅ Sample data created");
}

if (require.main === module) {
  main();
}
