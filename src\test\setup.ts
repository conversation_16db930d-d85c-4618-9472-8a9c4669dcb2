import '@testing-library/jest-dom';
import { vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import { db } from '../lib/database';
import { migrationManager } from '../lib/database/migrations';

// Mock environment variables for tests
Object.defineProperty(import.meta, "env", {
  value: {
    VITE_APP_ENV: "test",
    VITE_API_BASE_URL: "http://localhost:3001",
    VITE_MOCK_API: "true",
    VITE_FEATURE_ROUTING: "true",
    VITE_FEATURE_EVENT_SYSTEM: "true",
    VITE_LOGGING_ENABLED: "false",
    VITE_DATABASE_ENABLED: "true",
    DATABASE_URL: "postgresql://test:test@localhost:5432/synapseai_test",
    VITE_AUTH_ENABLED: "true",
    VITE_SESSION_TIMEOUT: "3600000",
    DB_POOL_SIZE: "5",
    DB_IDLE_TIMEOUT: "10000",
    DB_CONNECTION_TIMEOUT: "2000",
    DB_STATEMENT_TIMEOUT: "5000",
    DB_QUERY_TIMEOUT: "5000",
    NODE_ENV: "test",
  },
  writable: true,
});

// Mock fetch globally
global.fetch = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock IntersectionObserver
class IntersectionObserverMock {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
}
Object.defineProperty(window, 'IntersectionObserver', {
  value: IntersectionObserverMock,
});

// Database setup and teardown
beforeAll(async () => {
  try {
    // Connect to test database
    await db.connect();

    // Run migrations
    await migrationManager.migrate();

    console.log('Test database setup completed');
  } catch (error) {
    console.error('Failed to setup test database:', error);
    throw error;
  }
});

afterAll(async () => {
  try {
    // Clean up database
    await db.transaction(async (client) => {
      // Drop all tables
      await client.query(`
        DO $$ DECLARE
          r RECORD;
        BEGIN
          FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
            EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
          END LOOP;
        END $$;
      `);
    });

    // Disconnect
    await db.disconnect();

    console.log('Test database cleanup completed');
  } catch (error) {
    console.error('Failed to cleanup test database:', error);
    throw error;
  }
});

// Reset database state before each test
beforeEach(async () => {
  try {
    // Clean existing data
    await db.transaction(async (client) => {
      // Truncate all tables except migrations
      await client.query(`
        DO $$ DECLARE
          r RECORD;
        BEGIN
          FOR r IN (
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename != 'migrations'
          ) LOOP
            EXECUTE 'TRUNCATE TABLE ' || quote_ident(r.tablename) || ' CASCADE';
          END LOOP;
        END $$;
      `);
    });
  } catch (error) {
    console.error('Failed to reset test database:', error);
    throw error;
  }
});

// Clean up after each test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});
