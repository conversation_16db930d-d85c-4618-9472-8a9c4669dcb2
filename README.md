# SynapseAI Platform

A Universal AI Orchestration Platform that integrates with multiple AI providers and offers advanced agent management capabilities.

## Features

- Multi-provider support (OpenAI, Anthropic, Google, Mistral, Groq)
- Agent management and orchestration
- Real-time metrics and monitoring
- Secure API key management
- Request signing and validation
- Comprehensive logging and auditing

## Prerequisites

- Node.js 18+
- PostgreSQL 14+
- npm or yarn

## Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd synapseai
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
# Create a .env file with the following variables
DATABASE_URL=postgresql://user:password@localhost:5432/synapseai
SIGNING_SECRET=your-signing-secret
```

4. Set up the database:
```bash
# Create the database
createdb synapseai
createdb synapseai_test

# Run migrations
npm run db:migrate
```

## Development

Start the development server:
```bash
npm run dev
```

## Testing

The project includes comprehensive testing at multiple levels:

### Unit Tests
```bash
# Run all unit tests
npm test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### Integration Tests
Integration tests are included in the unit test suite but focus on testing multiple components together.

### End-to-End Tests
E2E tests simulate complete user flows and require a test database:
```bash
# Ensure test database exists
createdb synapseai_test

# Run migrations on test database
DATABASE_URL=postgresql://user:password@localhost:5432/synapseai_test npm run db:migrate

# Run E2E tests
npm test src/test/e2e
```

## Database Management

The project uses a custom migration system for database schema management:

```bash
# Run pending migrations
npm run db:migrate

# Roll back the last migration
npm run db:rollback

# Roll back multiple migrations
npm run db:rollback 3  # Rolls back 3 migrations

# Check migration status
npm run db:status
```

## Security Features

### API Key Management
- Automatic key rotation
- Secure key storage (hashed)
- Key validation and verification

### Request Signing
- Request payload signing
- Timestamp-based expiration
- Tamper detection

### Security Event Logging
- Comprehensive audit trail
- Security event monitoring
- Violation detection and alerting

## Project Structure

```
src/
├── components/          # React components
├── lib/                # Core libraries
│   ├── database/      # Database management
│   │   └── migrations/  # Database migrations
│   ├── security/      # Security features
│   └── api/           # API client
├── test/              # Test files
│   ├── e2e/          # End-to-end tests
│   └── utils/        # Test utilities
└── types/            # TypeScript types
```

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests
4. Submit a pull request

## License

[License Type] - See LICENSE file for details
