# SynapseAI Production Setup

## ⚠️ IMPORTANT: This is NOT a prototype - Real Production System

This application connects to a **REAL BACKEND API** and **REAL DATABASE**. No mock data or simulations.

## Quick Start

### 1. Configure Environment Variables

Create `.env.local` with your real API configuration:

```bash
# Real Production API Configuration
VITE_API_BASE_URL=http://your-backend-server:3001/api/v1
VITE_API_KEY=your-real-api-key-here
VITE_MOCK_API=false
VITE_APP_ENV=production

# WebSocket Configuration
VITE_WEBSOCKET_URL=ws://your-backend-server:3001/ws
VITE_APIX_URL=ws://your-backend-server:3001/apix

# Database Configuration (for backend)
DATABASE_URL=************************************************/synapseai

# Authentication
VITE_JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters
```

### 2. Start Backend Server

```bash
# Start your backend server first
cd synapseai-backend
npm install
npm start
```

### 3. Start Frontend

```bash
# In the main directory
npm install
npm run dev
```

### 4. Or Start Both Together

```bash
npm run start:full
```

## API Endpoints

The frontend connects to these **REAL** API endpoints:

- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/providers` - Get AI providers
- `POST /api/v1/providers` - Create AI provider
- `PUT /api/v1/providers/:id` - Update AI provider
- `DELETE /api/v1/providers/:id` - Delete AI provider
- `POST /api/v1/providers/:id/test` - Test AI provider
- `GET /api/v1/agents` - Get AI agents
- `POST /api/v1/agents` - Create AI agent
- `PUT /api/v1/agents/:id` - Update AI agent
- `DELETE /api/v1/agents/:id` - Delete AI agent
- `POST /api/v1/agents/:id/deploy` - Deploy AI agent
- `GET /api/v1/analytics/overview` - Get analytics
- `GET /api/v1/analytics/usage` - Get usage stats
- `GET /api/v1/health` - Health check

## Database

The system uses **PostgreSQL** with real data:

- Users table
- Providers table
- Agents table
- Metrics table
- Events table

## Security

- JWT authentication
- CORS protection
- CSRF protection
- Input validation
- Rate limiting
- SQL injection prevention

## No Mock Data

❌ **REMOVED**: All mock APIs, simulated data, and placeholder responses
✅ **USING**: Real backend server, real database, real API calls

## Troubleshooting

If you see "API call failed" errors:

1. Ensure your backend server is running
2. Check the `VITE_API_BASE_URL` in your `.env.local`
3. Verify your API key is correct
4. Check network connectivity to your backend

## Production Deployment

1. Set up your production database
2. Deploy your backend server
3. Update environment variables with production URLs
4. Build and deploy frontend: `npm run build`
