import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Settings,
  Trash2,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  TestTube,
  Play,
  Loader2,
  Zap,
} from "lucide-react";
import { getSynapseAI } from "@/lib/sdk";
import { getRealAPIClient } from "@/lib/realApiClient";

interface AIProvider {
  id: string;
  name: string;
  type: "openai" | "anthropic" | "google" | "mistral" | "groq";
  status: "active" | "inactive" | "error" | "maintenance";
  apiKey: string;
  models: string[];
  config: {
    maxTokens: number;
    temperature: number;
    timeout: number;
  };
  metrics: {
    requests: number;
    successRate: number;
    avgLatency: number;
    cost: number;
  };
  createdAt: string;
  lastUsed?: string;
}

interface AIProviderPanelProps {
  onProviderChange?: (providers: AIProvider[]) => void;
}

const AIProviderPanel: React.FC<AIProviderPanelProps> = ({
  onProviderChange = () => {},
}) => {
  const [providers, setProviders] = useState<AIProvider[]>([]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(
    null
  );
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<
    Record<
      string,
      {
        success: boolean;
        responseTime: number;
        error?: string;
        timestamp: string;
      }
    >
  >({});
  const [newProvider, setNewProvider] = useState({
    name: "",
    type: "openai" as const,
    apiKey: "",
    maxTokens: 4096,
    temperature: 0.7,
    timeout: 30000,
  });

  useEffect(() => {
    loadProviders();
  }, []);

  useEffect(() => {
    onProviderChange(providers);
  }, [providers, onProviderChange]);

  const loadProviders = async () => {
    try {
      const apiClient = getRealAPIClient();
      const response = await apiClient.getProviders();

      if (response && Array.isArray(response)) {
        const transformedProviders = response.map((provider: any) => ({
          id: provider.id,
          name: provider.name,
          type: provider.type,
          status: provider.status,
          apiKey: "***", // Never show actual API keys
          models: provider.models || [],
          config: provider.config || {
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000,
          },
          metrics: provider.metrics || {
            requests: 0,
            successRate: 0,
            avgLatency: 0,
            cost: 0,
          },
          createdAt: provider.created_at || new Date().toISOString(),
          lastUsed: provider.last_used,
        }));
        setProviders(transformedProviders);
      } else {
        // Handle empty or invalid response
        console.warn("No providers found or invalid response format");
        setProviders([]);
      }
    } catch (error) {
      console.error("Failed to load providers:", error);
      // Show user-friendly error message
      if (error instanceof Error) {
        // You could show a toast notification here
        console.error("Error loading providers:", error.message);
      }
      setProviders([]);
    }
  };

  const handleAddProvider = async () => {
    try {
      const apiClient = getRealAPIClient();
      const providerData = {
        name: newProvider.name,
        type: newProvider.type,
        api_key: newProvider.apiKey,
        status: "inactive",
        models: getDefaultModels(newProvider.type),
        config: {
          maxTokens: newProvider.maxTokens,
          temperature: newProvider.temperature,
          timeout: newProvider.timeout,
        },
      };

      const createdProvider = await apiClient.createProvider(providerData);

      const provider: AIProvider = {
        id: createdProvider.id,
        name: createdProvider.name,
        type: createdProvider.type,
        status: createdProvider.status,
        apiKey: "***", // Don't show actual API key
        models:
          createdProvider.models || getDefaultModels(createdProvider.type),
        config: createdProvider.config,
        metrics: {
          requests: 0,
          successRate: 0,
          avgLatency: 0,
          cost: 0,
        },
        createdAt: createdProvider.created_at,
      };

      setProviders((prev) => [...prev, provider]);
      setNewProvider({
        name: "",
        type: "openai",
        apiKey: "",
        maxTokens: 4096,
        temperature: 0.7,
        timeout: 30000,
      });
      setIsDialogOpen(false);

      // Add to SDK
      try {
        const sdk = getSynapseAI();
        sdk.addProvider({
          id: provider.id,
          name: provider.name,
          type: provider.type,
          status: provider.status,
          apiKey: provider.apiKey,
          models: provider.models,
        });
      } catch (error) {
        console.error("Failed to add provider to SDK:", error);
      }
    } catch (error) {
      console.error("Failed to create provider:", error);
      // Show error to user
    }
  };

  const handleToggleProvider = async (id: string) => {
    try {
      const provider = providers.find((p) => p.id === id);
      if (!provider) return;

      const newStatus = provider.status === "active" ? "inactive" : "active";
      const apiClient = getRealAPIClient();

      await apiClient.updateProvider(id, { status: newStatus });

      setProviders((prev) =>
        prev.map((p) => (p.id === id ? { ...p, status: newStatus } : p))
      );
    } catch (error) {
      console.error("Failed to toggle provider:", error);
    }
  };

  const handleDeleteProvider = async (id: string) => {
    try {
      const apiClient = getRealAPIClient();
      const success = await apiClient.deleteProvider(id);

      if (success) {
        setProviders((prev) => prev.filter((provider) => provider.id !== id));

        // Remove from SDK
        try {
          const sdk = getSynapseAI();
          sdk.removeProvider(id);
        } catch (error) {
          console.error("Failed to remove provider from SDK:", error);
        }
      }
    } catch (error) {
      console.error("Failed to delete provider:", error);
    }
  };

  const handleTestProvider = async (providerId: string) => {
    setTestingProvider(providerId);
    const startTime = Date.now();

    try {
      const apiClient = getRealAPIClient();
      const testResult = await apiClient.testProvider(providerId);
      const responseTime = Date.now() - startTime;

      setTestResults((prev) => ({
        ...prev,
        [providerId]: {
          success: testResult.success,
          responseTime,
          timestamp: new Date().toISOString(),
          error: testResult.error,
        },
      }));

      // Update provider status based on test result
      const newStatus = testResult.success ? "active" : "error";
      await apiClient.updateProvider(providerId, { status: newStatus });

      setProviders((prev) =>
        prev.map((p) =>
          p.id === providerId
            ? { ...p, status: newStatus as "active" | "error" }
            : p
        )
      );
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      setTestResults((prev) => ({
        ...prev,
        [providerId]: {
          success: false,
          responseTime,
          error: errorMessage,
          timestamp: new Date().toISOString(),
        },
      }));

      // Update provider status to error if test failed
      try {
        const apiClient = getRealAPIClient();
        await apiClient.updateProvider(providerId, { status: "error" });
        setProviders((prev) =>
          prev.map((p) =>
            p.id === providerId ? { ...p, status: "error" as const } : p
          )
        );
      } catch (updateError) {
        console.error("Failed to update provider status:", updateError);
      }
    } finally {
      setTestingProvider(null);
    }
  };

  const getDefaultModels = (type: string): string[] => {
    switch (type) {
      case "openai":
        return ["gpt-4", "gpt-3.5-turbo"];
      case "anthropic":
        return ["claude-3-opus", "claude-3-sonnet"];
      case "google":
        return ["gemini-pro", "gemini-ultra"];
      case "mistral":
        return ["mistral-large", "mistral-medium"];
      case "groq":
        return ["llama-3-70b", "mixtral-8x7b"];
      default:
        return [];
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "maintenance":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "error":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  return (
    <Card className="w-full bg-card">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium">AI Providers</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="flex items-center gap-1">
              <Plus className="h-4 w-4" />
              Add Provider
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add AI Provider</DialogTitle>
              <DialogDescription>
                Configure a new AI provider for your SynapseAI platform.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="provider-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="provider-name"
                  value={newProvider.name}
                  onChange={(e) =>
                    setNewProvider((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  className="col-span-3"
                  placeholder="My OpenAI Provider"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="provider-type" className="text-right">
                  Type
                </Label>
                <Select
                  value={newProvider.type}
                  onValueChange={(value: any) =>
                    setNewProvider((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">
                      Anthropic (Claude)
                    </SelectItem>
                    <SelectItem value="google">Google (Gemini)</SelectItem>
                    <SelectItem value="mistral">Mistral</SelectItem>
                    <SelectItem value="groq">Groq</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="api-key" className="text-right">
                  API Key
                </Label>
                <Input
                  id="api-key"
                  type="password"
                  value={newProvider.apiKey}
                  onChange={(e) =>
                    setNewProvider((prev) => ({
                      ...prev,
                      apiKey: e.target.value,
                    }))
                  }
                  className="col-span-3"
                  placeholder="sk-..."
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="max-tokens" className="text-right">
                  Max Tokens
                </Label>
                <Input
                  id="max-tokens"
                  type="number"
                  value={newProvider.maxTokens}
                  onChange={(e) =>
                    setNewProvider((prev) => ({
                      ...prev,
                      maxTokens: parseInt(e.target.value) || 4096,
                    }))
                  }
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddProvider}>Add Provider</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="testing">Testing</TabsTrigger>
            <TabsTrigger value="configuration">Configuration</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {providers.map((provider) => (
              <div
                key={provider.id}
                className="p-4 rounded-md border bg-card hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(provider.status)}
                    <div>
                      <h4 className="font-medium">{provider.name}</h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline">{provider.type}</Badge>
                        <Badge className={getStatusBadgeColor(provider.status)}>
                          {provider.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestProvider(provider.id)}
                      disabled={testingProvider === provider.id}
                    >
                      {testingProvider === provider.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4" />
                      )}
                    </Button>
                    <Switch
                      checked={provider.status === "active"}
                      onCheckedChange={() => handleToggleProvider(provider.id)}
                    />
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500"
                      onClick={() => handleDeleteProvider(provider.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Models</p>
                    <p className="font-medium">{provider.models.length}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Requests</p>
                    <p className="font-medium">{provider.metrics.requests}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Success Rate</p>
                    <p className="font-medium">
                      {provider.metrics.successRate}%
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Avg Latency</p>
                    <p className="font-medium">
                      {provider.metrics.avgLatency}ms
                    </p>
                  </div>
                </div>

                {provider.lastUsed && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    Last used: {new Date(provider.lastUsed).toLocaleString()}
                  </div>
                )}

                {testResults[provider.id] && (
                  <div className="mt-2 p-2 rounded bg-muted/50">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">Last Test:</span>
                      <span
                        className={
                          testResults[provider.id].success
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {testResults[provider.id].success
                          ? "✓ Passed"
                          : "✗ Failed"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-xs mt-1">
                      <span className="text-muted-foreground">
                        Response Time:
                      </span>
                      <span>{testResults[provider.id].responseTime}ms</span>
                    </div>
                    {testResults[provider.id].error && (
                      <div className="text-xs text-red-600 mt-1">
                        Error: {testResults[provider.id].error}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </TabsContent>

          <TabsContent value="testing" className="space-y-4">
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Provider Testing</h3>
                <Button
                  onClick={() => {
                    providers.forEach((provider) => {
                      if (provider.status === "active") {
                        handleTestProvider(provider.id);
                      }
                    });
                  }}
                  disabled={testingProvider !== null}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Test All Active
                </Button>
              </div>

              {providers.map((provider) => (
                <Card key={provider.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(provider.status)}
                        <h4 className="font-medium">{provider.name}</h4>
                        <Badge variant="outline">{provider.type}</Badge>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => handleTestProvider(provider.id)}
                        disabled={testingProvider === provider.id}
                      >
                        {testingProvider === provider.id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Play className="h-4 w-4 mr-2" />
                        )}
                        {testingProvider === provider.id
                          ? "Testing..."
                          : "Test Provider"}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {testResults[provider.id] ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            Status:
                          </span>
                          <Badge
                            className={
                              testResults[provider.id].success
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }
                          >
                            {testResults[provider.id].success
                              ? "Success"
                              : "Failed"}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            Response Time:
                          </span>
                          <span className="text-sm font-medium">
                            {testResults[provider.id].responseTime}ms
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            Tested:
                          </span>
                          <span className="text-sm">
                            {new Date(
                              testResults[provider.id].timestamp
                            ).toLocaleString()}
                          </span>
                        </div>
                        {testResults[provider.id].error && (
                          <div className="mt-2 p-2 bg-red-50 rounded">
                            <p className="text-sm text-red-600">
                              Error: {testResults[provider.id].error}
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        No test results available. Click "Test Provider" to run
                        a test.
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="configuration" className="space-y-4">
            <div className="text-center py-8">
              <Settings className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                Provider configuration settings will be available here.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Total Requests
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {providers.reduce((sum, p) => sum + p.metrics.requests, 0)}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Avg Success Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(
                      providers.reduce(
                        (sum, p) => sum + p.metrics.successRate,
                        0
                      ) / providers.length
                    ).toFixed(1)}
                    %
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Total Cost
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    $
                    {providers
                      .reduce((sum, p) => sum + p.metrics.cost, 0)
                      .toFixed(2)}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AIProviderPanel;
