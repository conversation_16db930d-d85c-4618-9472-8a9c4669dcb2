import { v4 as uuidv4 } from "uuid";
import {
  SecurityEvent,
  SecurityEventQuery,
  SecurityAlert,
  SecurityAnalysis,
  AlertResolution,
  SecurityEventSeverity,
  SecurityEventType,
} from "./types";

export class SecurityManager {
  private rateLimitMap = new Map<
    string,
    { count: number; resetTime: number }
  >();
  private blockedIPs = new Set<string>();
  private suspiciousPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /\beval\s*\(/gi,
    /\bexec\s*\(/gi,
  ];

  constructor(private readonly db: any) {}

  // Enhanced input validation and sanitization
  sanitizeInput(input: string): string {
    if (typeof input !== "string") {
      return "";
    }

    // Remove potentially dangerous characters and patterns
    let sanitized = input
      .replace(/[<>"'&]/g, "")
      .replace(/\0/g, "")
      .replace(/\x08/g, "")
      .replace(/\x09/g, "")
      .replace(/\x1a/g, "")
      .replace(/[\x00-\x1F\x7F]/g, "") // Remove control characters
      .trim();

    // Check for suspicious patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(sanitized)) {
        this.logSecurityEvent(
          SecurityEventType.MALICIOUS_INPUT,
          SecurityEventSeverity.WARNING,
          "system",
          { input: input.substring(0, 100), pattern: pattern.source }
        );
        throw new Error("Potentially malicious input detected");
      }
    }

    // Additional length validation
    if (sanitized.length > 10000) {
      throw new Error("Input too long");
    }

    return sanitized;
  }

  // SQL injection prevention
  validateSQLInput(input: string): boolean {
    const sqlPatterns = [
      /('|(\-\-)|(;)|(\|)|(\*)|(%))/i,
      /(union|select|insert|delete|update|drop|create|alter|exec|execute)/i,
      /(script|javascript|vbscript|onload|onerror|onclick)/i,
    ];

    return !sqlPatterns.some((pattern) => pattern.test(input));
  }

  // XSS prevention
  escapeHtml(unsafe: string): string {
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    return { valid: errors.length === 0, errors };
  }

  // Enhanced rate limiting and DDoS protection
  checkRateLimit(
    identifier: string,
    limit: number = 100,
    windowMs: number = 60000
  ): boolean {
    const now = Date.now();
    const record = this.rateLimitMap.get(identifier);

    if (!record || now > record.resetTime) {
      this.rateLimitMap.set(identifier, {
        count: 1,
        resetTime: now + windowMs,
      });
      return true;
    }

    if (record.count >= limit) {
      // Log rate limit violation
      this.logSecurityEvent(
        SecurityEventType.RATE_LIMIT_EXCEEDED,
        SecurityEventSeverity.WARNING,
        "system",
        { identifier, count: record.count, limit, windowMs }
      );

      // Block IP if excessive requests
      if (record.count > limit * 2) {
        this.blockedIPs.add(identifier);
        this.logSecurityEvent(
          SecurityEventType.IP_BLOCKED,
          SecurityEventSeverity.ERROR,
          "system",
          { identifier, reason: "Excessive rate limit violations" }
        );
      }
      return false;
    }

    record.count++;
    return true;
  }

  // Advanced threat detection
  detectAnomalousActivity(userId: string, activity: any): boolean {
    const patterns = [
      // Rapid successive requests
      { type: "rapid_requests", threshold: 10, window: 1000 },
      // Unusual access patterns
      { type: "unusual_hours", threshold: 5, window: 3600000 },
      // Multiple failed attempts
      { type: "failed_attempts", threshold: 5, window: 300000 },
    ];

    // Implementation would check against these patterns
    // For now, return false (no anomaly detected)
    return false;
  }

  isBlocked(identifier: string): boolean {
    return this.blockedIPs.has(identifier);
  }

  unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
  }

  // CSRF protection
  generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  validateCSRFToken(token: string, sessionToken: string): boolean {
    return token === sessionToken && token.length === 64;
  }

  // Request signature verification
  async signRequest(payload: any, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(JSON.stringify(payload));
    const key = await crypto.subtle.importKey(
      "raw",
      encoder.encode(secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signature = await crypto.subtle.sign("HMAC", key, data);
    return Array.from(new Uint8Array(signature), (byte) =>
      byte.toString(16).padStart(2, "0")
    ).join("");
  }

  async verifyRequestSignature(
    signature: string,
    payload: any,
    secret: string
  ): Promise<boolean> {
    const expectedSignature = await this.signRequest(payload, secret);
    return signature === expectedSignature;
  }

  // API key management
  generateApiKey(): string {
    const prefix = "sk_";
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    const key = Array.from(array, (byte) =>
      byte.toString(16).padStart(2, "0")
    ).join("");
    return prefix + key;
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    // Basic format validation
    if (!apiKey || typeof apiKey !== "string") {
      return false;
    }

    // Check for common API key patterns
    const validPrefixes = ["sk_", "pk_", "gsk_", "AIza"];
    const hasValidPrefix = validPrefixes.some((prefix) =>
      apiKey.startsWith(prefix)
    );

    if (!hasValidPrefix) {
      return false;
    }

    // In production, validate against database
    return apiKey.length >= 20;
  }

  async rotateApiKey(providerId: string): Promise<string> {
    const newKey = this.generateApiKey();
    // In production, update database and invalidate old key
    return newKey;
  }

  // Security event logging
  logSecurityEvent(
    type: string,
    severity: string,
    userId: string,
    details: any
  ): void {
    const event = {
      type,
      severity,
      userId,
      details: this.sanitizeEventDetails(details),
      timestamp: new Date(),
      ip: this.getClientIP(),
      userAgent: this.getUserAgent(),
    };

    console.log("Security Event:", event);

    // In production, store in database and alert if critical
    if (severity === "CRITICAL" || severity === "ERROR") {
      this.handleCriticalEvent(event);
    }
  }

  private sanitizeEventDetails(details: any): any {
    if (typeof details !== "object" || details === null) {
      return details;
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(details)) {
      if (typeof value === "string") {
        sanitized[key] = this.sanitizeInput(value);
      } else if (typeof value === "object") {
        sanitized[key] = this.sanitizeEventDetails(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private handleCriticalEvent(event: any): void {
    // In production, send alerts, block IPs, etc.
    console.error("Critical security event:", event);
  }

  private getClientIP(): string {
    // In production, extract from request headers
    return "unknown";
  }

  private getUserAgent(): string {
    return typeof navigator !== "undefined" ? navigator.userAgent : "unknown";
  }

  async logEvent(event: SecurityEvent): Promise<void> {
    const timestamp = event.timestamp || new Date();

    await this.db.query(
      `INSERT INTO security_events (
        id,
        type,
        severity,
        user_id,
        ip_address,
        user_agent,
        details,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
      [
        uuidv4(),
        event.type,
        event.severity,
        event.userId,
        event.ipAddress,
        event.userAgent,
        event.details,
        timestamp,
      ]
    );

    // Generate alert for high-severity events
    if (
      event.severity === SecurityEventSeverity.ERROR ||
      event.severity === SecurityEventSeverity.CRITICAL
    ) {
      await this.createAlert(event);
    }
  }

  async getEvents(query: SecurityEventQuery): Promise<SecurityEvent[]> {
    let sql = "SELECT * FROM security_events WHERE 1=1";
    const params: any[] = [];
    let paramIndex = 1;

    if (query.type) {
      sql += ` AND type = $${paramIndex}`;
      params.push(query.type);
      paramIndex++;
    }

    if (query.severity) {
      sql += ` AND severity = $${paramIndex}`;
      params.push(query.severity);
      paramIndex++;
    }

    if (query.userId) {
      sql += ` AND user_id = $${paramIndex}`;
      params.push(query.userId);
      paramIndex++;
    }

    if (query.ipAddress) {
      sql += ` AND ip_address = $${paramIndex}`;
      params.push(query.ipAddress);
      paramIndex++;
    }

    if (query.startTime) {
      sql += ` AND created_at >= $${paramIndex}`;
      params.push(query.startTime);
      paramIndex++;
    }

    if (query.endTime) {
      sql += ` AND created_at <= $${paramIndex}`;
      params.push(query.endTime);
      paramIndex++;
    }

    sql += " ORDER BY created_at DESC";

    const result = await this.db.query(sql, params);
    return result.rows.map((row) => ({
      type: row.type,
      severity: row.severity,
      userId: row.user_id,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      details: row.details,
      timestamp: row.created_at,
    }));
  }

  async analyzeSuspiciousActivity(userId: string): Promise<SecurityAnalysis> {
    const hourAgo = new Date(Date.now() - 3600000);

    // Check failed login attempts
    const failedLogins = await this.getEvents({
      type: SecurityEventType.LOGIN_ATTEMPT,
      userId,
      startTime: hourAgo,
    });

    const failedLoginCount = failedLogins.filter(
      (event) => event.details.success === false
    ).length;

    // Check API access frequency
    const apiCalls = await this.getEvents({
      type: SecurityEventType.API_ACCESS,
      userId,
      startTime: hourAgo,
    });

    const analysis: SecurityAnalysis = {
      userId,
      suspiciousLoginAttempts: failedLoginCount >= 5,
      suspiciousApiAccess: apiCalls.length >= 100,
      failedLoginCount,
      rapidApiCallCount: apiCalls.length,
      lastAnalyzed: new Date(),
    };

    return analysis;
  }

  private async createAlert(event: SecurityEvent): Promise<void> {
    await this.db.query(
      `INSERT INTO security_alerts (
        id,
        event_id,
        severity,
        user_id,
        details,
        created_at,
        resolved
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [
        uuidv4(),
        event.type,
        event.severity,
        event.userId,
        event.details,
        new Date(),
        false,
      ]
    );
  }

  async getActiveAlerts(): Promise<SecurityAlert[]> {
    const result = await this.db.query(
      "SELECT * FROM security_alerts WHERE resolved = false ORDER BY created_at DESC"
    );

    return result.rows.map((row) => ({
      id: row.id,
      eventId: row.event_id,
      severity: row.severity,
      userId: row.user_id,
      details: row.details,
      created: row.created_at,
      resolved: row.resolved,
      resolutionDetails: row.resolution_details,
    }));
  }

  async getAlertById(alertId: string): Promise<SecurityAlert> {
    const result = await this.db.query(
      "SELECT * FROM security_alerts WHERE id = $1",
      [alertId]
    );

    if (result.rows.length === 0) {
      throw new Error(`Alert not found: ${alertId}`);
    }

    const row = result.rows[0];
    return {
      id: row.id,
      eventId: row.event_id,
      severity: row.severity,
      userId: row.user_id,
      details: row.details,
      created: row.created_at,
      resolved: row.resolved,
      resolutionDetails: row.resolution_details,
    };
  }

  async resolveAlert(
    alertId: string,
    resolution: AlertResolution
  ): Promise<void> {
    const resolutionDetails = {
      ...resolution,
      resolvedAt: new Date(),
    };

    await this.db.query(
      `UPDATE security_alerts 
       SET resolved = true, resolution_details = $1 
       WHERE id = $2`,
      [resolutionDetails, alertId]
    );
  }
}

// Export types
export * from "./types";

// Create and export singleton instance with enhanced security
class EnhancedSecurityManager extends SecurityManager {
  private encryptionKey: CryptoKey | null = null;

  constructor() {
    super({} as Pool); // Mock pool for now
    this.initializeEncryption();
  }

  private async initializeEncryption(): Promise<void> {
    try {
      this.encryptionKey = await crypto.subtle.generateKey(
        {
          name: "AES-GCM",
          length: 256,
        },
        true,
        ["encrypt", "decrypt"]
      );
    } catch (error) {
      console.error("Failed to initialize encryption:", error);
    }
  }

  // Data encryption at rest
  async encryptData(data: string): Promise<{ encrypted: string; iv: string }> {
    if (!this.encryptionKey) {
      throw new Error("Encryption not initialized");
    }

    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const iv = crypto.getRandomValues(new Uint8Array(12));

    const encrypted = await crypto.subtle.encrypt(
      {
        name: "AES-GCM",
        iv: iv,
      },
      this.encryptionKey,
      dataBuffer
    );

    return {
      encrypted: Array.from(new Uint8Array(encrypted), (byte) =>
        byte.toString(16).padStart(2, "0")
      ).join(""),
      iv: Array.from(iv, (byte) => byte.toString(16).padStart(2, "0")).join(""),
    };
  }

  async decryptData(encryptedData: string, ivHex: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error("Encryption not initialized");
    }

    const encrypted = new Uint8Array(
      encryptedData.match(/.{1,2}/g)!.map((byte) => parseInt(byte, 16))
    );
    const iv = new Uint8Array(
      ivHex.match(/.{1,2}/g)!.map((byte) => parseInt(byte, 16))
    );

    const decrypted = await crypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv: iv,
      },
      this.encryptionKey,
      encrypted
    );

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  }

  // Secure session management
  generateSecureSessionId(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  // Enhanced logging with security context
  async logEvent(event: SecurityEvent): Promise<void> {
    const enhancedEvent = {
      ...event,
      sessionId: this.generateSecureSessionId(),
      encrypted: true,
      timestamp: new Date(),
    };

    // Encrypt sensitive details
    if (event.details && typeof event.details === "object") {
      try {
        const encryptedDetails = await this.encryptData(
          JSON.stringify(event.details)
        );
        enhancedEvent.details = encryptedDetails;
      } catch (error) {
        console.error("Failed to encrypt event details:", error);
      }
    }

    console.log("Enhanced Security Event:", enhancedEvent);
  }

  async getEvents(query: SecurityEventQuery): Promise<SecurityEvent[]> {
    // Mock implementation with security filtering
    return [];
  }

  async analyzeSuspiciousActivity(userId: string): Promise<SecurityAnalysis> {
    // Enhanced analysis with ML-like patterns
    return {
      userId,
      suspiciousLoginAttempts: false,
      suspiciousApiAccess: false,
      failedLoginCount: 0,
      rapidApiCallCount: 0,
      lastAnalyzed: new Date(),
    };
  }

  async getActiveAlerts(): Promise<SecurityAlert[]> {
    return [];
  }

  async getAlertById(alertId: string): Promise<SecurityAlert> {
    throw new Error(`Alert not found: ${alertId}`);
  }

  async resolveAlert(
    alertId: string,
    resolution: AlertResolution
  ): Promise<void> {
    console.log("Resolving alert:", alertId, resolution);
  }
}

export const securityManager = new EnhancedSecurityManager();
