// APIX WebSocket Protocol Implementation for SynapseAI

import { getLogger } from "./logger";
import { getConfig } from "./config";
import { getError<PERSON><PERSON><PERSON> } from "./errorHandler";

export interface APXMessage {
  id: string;
  type:
  | "tool_call_start"
  | "thinking_status"
  | "text_chunk"
  | "agent_update"
  | "session_sync"
  | "session_resume"
  | "error"
  | "heartbeat"
  | "ack"
  | "tool_call_response"
  | "hitl_request"
  | "hitl_response";
  timestamp: string;
  data: any;
  sessionId?: string;
  agentId?: string;
  userId?: string;
}

export interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  enableCompression: boolean;
  protocols: string[];
  bufferSize: number;
  messageQueueSize: number;
  sessionTTL: number;
  enableStatePersistence: boolean;
  enableAutoResume: boolean;
  stateBackupInterval: number;
}

export interface SessionState {
  id: string;
  agentId: string;
  userId: string;
  tenantId?: string;
  projectId?: string;
  appId?: string;
  state: Record<string, any>;
  ttl: number;
  lastActivity: string;
  metadata: Record<string, any>;
  version: number;
  checksum: string;
}

export interface ConnectionState {
  id: string;
  status:
  | "connecting"
  | "connected"
  | "reconnecting"
  | "disconnected"
  | "error";
  lastConnected?: Date;
  lastDisconnected?: Date;
  reconnectAttempts: number;
  latency: number;
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  errors: string[];
}

export interface MessageBuffer {
  id: string;
  message: APXMessage;
  timestamp: Date;
  attempts: number;
  priority: "low" | "normal" | "high" | "critical";
  persistent: boolean;
}

class APXWebSocketClient {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private logger = getLogger().createChild("APXWebSocket");
  private errorHandler = getErrorHandler();
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private stateBackupTimer: NodeJS.Timeout | null = null;
  private messageBuffer: Map<string, MessageBuffer> = new Map();
  private subscribers: Map<string, Set<(message: APXMessage) => void>> =
    new Map();
  private sessionStates: Map<string, SessionState> = new Map();
  private connectionState: ConnectionState;
  private connectionId: string | null = null;
  private lastHeartbeat: Date | null = null;
  private messageSequence = 0;
  private pendingAcks: Map<
    string,
    { resolve: Function; reject: Function; timeout: NodeJS.Timeout }
  > = new Map();
  private stateChecksum: string = "";
  private persistentStorage: Storage;

  constructor(config?: Partial<WebSocketConfig>) {
    const appConfig = getConfig();
    const wsConfig = appConfig.get("websocket");

    this.config = {
      url: config?.url || wsConfig.url,
      reconnectInterval:
        config?.reconnectInterval || wsConfig.reconnectInterval,
      maxReconnectAttempts:
        config?.maxReconnectAttempts || wsConfig.maxReconnectAttempts,
      heartbeatInterval:
        config?.heartbeatInterval || wsConfig.heartbeatInterval,
      enableCompression:
        config?.enableCompression ?? wsConfig.enableCompression,
      protocols: config?.protocols || wsConfig.protocols,
      bufferSize: config?.bufferSize || wsConfig.bufferSize,
      messageQueueSize: config?.messageQueueSize || wsConfig.messageQueueSize,
      sessionTTL: config?.sessionTTL || wsConfig.sessionTTL,
      enableStatePersistence:
        config?.enableStatePersistence ?? wsConfig.enableStatePersistence,
      enableAutoResume: config?.enableAutoResume ?? true,
      stateBackupInterval: config?.stateBackupInterval || 30000, // 30 seconds
    };

    this.connectionState = {
      id: this.generateConnectionId(),
      status: "disconnected",
      reconnectAttempts: 0,
      latency: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0,
      errors: [],
    };

    this.persistentStorage = this.config.enableStatePersistence
      ? localStorage
      : sessionStorage;

    if (appConfig.canUseRealTime() && wsConfig.enabled) {
      this.initializeFromStorage();
      this.connect();
    }
  }

  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.connectionState.status = "connecting";
      this.connectionState.reconnectAttempts = this.reconnectAttempts;

      this.logger.info("Connecting to APIX WebSocket", {
        url: this.config.url,
        attempt: this.reconnectAttempts + 1,
        connectionId: this.connectionState.id,
      });

      // Add authentication headers if available
      const headers: Record<string, string> = {};
      const authManager = (await import("./auth")).getAuthManager();
      const token = authManager.getAccessToken();
      const user = authManager.getState().user;

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      if (user?.tenantId) {
        headers["X-Tenant-ID"] = user.tenantId;
      }

      // Create WebSocket with enhanced configuration
      this.ws = new WebSocket(this.config.url, this.config.protocols);

      // Set binary type for better performance
      this.ws.binaryType = "arraybuffer";

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Connection timeout
      setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close();
          this.handleConnectionTimeout();
        }
      }, 10000); // 10 second timeout
    } catch (error) {
      this.connectionState.status = "error";
      this.connectionState.errors.push(
        error instanceof Error ? error.message : String(error)
      );

      this.errorHandler.handleError({
        message: "Failed to create WebSocket connection",
        stack: error instanceof Error ? error.stack : undefined,
        component: "APXWebSocket",
        severity: "high",
        category: "system",
        metadata: {
          url: this.config.url,
          connectionId: this.connectionState.id,
          attempt: this.reconnectAttempts + 1,
        },
      });

      this.scheduleReconnect();
    }
  }

  private handleOpen(event: Event): void {
    this.connectionState.status = "connected";
    this.connectionState.lastConnected = new Date();
    this.reconnectAttempts = 0;
    this.connectionId = this.generateConnectionId();

    this.logger.info("APIX WebSocket connected", {
      connectionId: this.connectionId,
      attempt: this.connectionState.reconnectAttempts + 1,
      latency: this.connectionState.latency,
    });

    // Start heartbeat and state backup
    this.startHeartbeat();
    this.startStateBackup();

    // Resume session state if enabled
    if (this.config.enableAutoResume) {
      this.resumeSessionStates();
    }

    // Send buffered messages with priority ordering
    this.flushMessageBuffer();

    // Notify subscribers
    this.notifySubscribers("connection", {
      id: this.generateMessageId(),
      type: "connection" as any,
      timestamp: new Date().toISOString(),
      data: {
        status: "connected",
        connectionId: this.connectionId,
        reconnectAttempts: this.connectionState.reconnectAttempts,
        sessionCount: this.sessionStates.size,
        bufferedMessages: this.messageBuffer.size,
      },
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      this.connectionState.messagesReceived++;
      this.connectionState.bytesTransferred += event.data.length;

      let message: APXMessage;

      // Handle both text and binary messages
      if (typeof event.data === "string") {
        message = JSON.parse(event.data);
      } else {
        // Handle binary messages (ArrayBuffer)
        const decoder = new TextDecoder();
        const jsonString = decoder.decode(event.data);
        message = JSON.parse(jsonString);
      }

      this.logger.debug("Received APIX message", {
        type: message.type,
        id: message.id,
        sessionId: message.sessionId,
        size: event.data.length,
      });

      // Handle acknowledgments
      if (message.type === "ack" && message.data?.messageId) {
        this.handleAcknowledgment(message.data.messageId);
        return;
      }

      // Handle system messages
      if (message.type === "heartbeat") {
        this.handleHeartbeat(message);
        return;
      }

      if (message.type === "session_sync") {
        this.handleSessionSync(message);
        return;
      }

      if (message.type === "error") {
        this.handleServerError(message);
        return;
      }

      // Send acknowledgment for reliable messages
      if (message.data?.requiresAck) {
        this.sendAcknowledgment(message.id);
      }

      // Update session state if applicable
      if (message.sessionId) {
        this.updateSessionActivity(message.sessionId);
      }

      // Notify subscribers
      this.notifySubscribers(message.type, message);
      this.notifySubscribers("*", message); // Wildcard subscribers
    } catch (error) {
      this.connectionState.errors.push(
        error instanceof Error ? error.message : String(error)
      );

      this.errorHandler.handleError({
        message: "Failed to parse APIX message",
        stack: error instanceof Error ? error.stack : undefined,
        component: "APXWebSocket",
        severity: "medium",
        category: "system",
        metadata: {
          rawMessage:
            typeof event.data === "string" ? event.data : "[Binary Data]",
          messageSize: event.data.length,
        },
      });
    }
  }

  private handleClose(event: CloseEvent): void {
    this.connectionState.status = "disconnected";
    this.connectionState.lastDisconnected = new Date();
    this.connectionId = null;

    this.logger.warn("APIX WebSocket disconnected", {
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean,
      reconnectAttempts: this.reconnectAttempts,
      connectionId: this.connectionState.id,
    });

    // Stop timers
    this.stopHeartbeat();
    this.stopStateBackup();

    // Backup current state before reconnection
    if (this.config.enableStatePersistence) {
      this.backupState();
    }

    // Notify subscribers
    this.notifySubscribers("connection", {
      id: this.generateMessageId(),
      type: "connection" as any,
      timestamp: new Date().toISOString(),
      data: {
        status: "disconnected",
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        reconnectAttempts: this.reconnectAttempts,
        bufferedMessages: this.messageBuffer.size,
      },
    });

    // Attempt reconnection based on close code
    if (this.shouldReconnect(event.code)) {
      if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
        this.scheduleReconnect();
      } else {
        this.connectionState.status = "error";
        this.logger.error("Max reconnection attempts reached", {
          maxAttempts: this.config.maxReconnectAttempts,
          connectionId: this.connectionState.id,
        });

        this.notifySubscribers("connection", {
          id: this.generateMessageId(),
          type: "connection" as any,
          timestamp: new Date().toISOString(),
          data: {
            status: "error",
            error: "Max reconnection attempts reached",
            maxAttempts: this.config.maxReconnectAttempts,
          },
        });
      }
    }
  }

  private handleError(event: Event): void {
    this.errorHandler.handleError({
      message: "APIX WebSocket error",
      component: "APXWebSocket",
      severity: "high",
      category: "system",
      metadata: { event },
    });
  }

  private handleHeartbeat(message: APXMessage): void {
    // Respond to heartbeat
    this.send({
      id: this.generateMessageId(),
      type: "heartbeat",
      timestamp: new Date().toISOString(),
      data: { response: true, connectionId: this.connectionId },
    });
  }

  private handleSessionSync(message: APXMessage): void {
    const sessionData = message.data as SessionState;
    if (sessionData && sessionData.id) {
      this.sessionStates.set(sessionData.id, sessionData);
      this.logger.debug("Session state synchronized", {
        sessionId: sessionData.id,
      });
    }
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    this.connectionState.status = "reconnecting";
    this.connectionState.reconnectAttempts = this.reconnectAttempts;

    // Exponential backoff with jitter
    const baseDelay = this.config.reconnectInterval;
    const exponentialDelay =
      baseDelay * Math.pow(2, this.reconnectAttempts - 1);
    const maxDelay = 30000; // 30 seconds max
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    const delay = Math.min(exponentialDelay, maxDelay) + jitter;

    this.logger.info(
      `Scheduling reconnection attempt ${this.reconnectAttempts}`,
      {
        delay: Math.round(delay),
        maxAttempts: this.config.maxReconnectAttempts,
        connectionId: this.connectionState.id,
      }
    );

    // Notify subscribers about reconnection attempt
    this.notifySubscribers("connection", {
      id: this.generateMessageId(),
      type: "connection" as any,
      timestamp: new Date().toISOString(),
      data: {
        status: "reconnecting",
        attempt: this.reconnectAttempts,
        maxAttempts: this.config.maxReconnectAttempts,
        delay: Math.round(delay),
      },
    });

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.connectionState.status === "connected") {
        this.send({
          id: this.generateMessageId(),
          type: "heartbeat",
          timestamp: new Date().toISOString(),
          data: { ping: true, connectionId: this.connectionId },
        });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private flushMessageBuffer(): void {
    if (this.connectionState.status !== "connected" || !this.ws) {
      return;
    }

    // Sort messages by priority and timestamp
    const sortedMessages = Array.from(this.messageBuffer.values()).sort(
      (a, b) => {
        const priorityOrder = { critical: 0, high: 1, normal: 2, low: 3 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];

        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }

        return a.timestamp.getTime() - b.timestamp.getTime();
      }
    );

    let flushedCount = 0;
    for (const bufferedMessage of sortedMessages) {
      try {
        this.sendImmediate(bufferedMessage.message);
        this.messageBuffer.delete(bufferedMessage.id);
        flushedCount++;
      } catch (error) {
        this.logger.warn("Failed to flush buffered message", {
          messageId: bufferedMessage.id,
          error: error instanceof Error ? error.message : String(error),
        });

        // Increment attempt count
        bufferedMessage.attempts++;
        if (bufferedMessage.attempts >= 3) {
          this.messageBuffer.delete(bufferedMessage.id);
          this.logger.error("Dropping message after max attempts", {
            messageId: bufferedMessage.id,
            attempts: bufferedMessage.attempts,
          });
        }
      }
    }

    if (flushedCount > 0) {
      this.logger.info("Flushed buffered messages", {
        count: flushedCount,
        remaining: this.messageBuffer.size,
      });
    }
  }

  private notifySubscribers(type: string, message: any): void {
    const subscribers = this.subscribers.get(type);
    if (subscribers) {
      subscribers.forEach((callback) => {
        try {
          callback(message);
        } catch (error) {
          this.errorHandler.handleError({
            message: "Error in APIX message subscriber",
            stack: error instanceof Error ? error.stack : undefined,
            component: "APXWebSocket",
            severity: "medium",
            category: "system",
            metadata: { messageType: type, subscriberError: error },
          });
        }
      });
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  // Public API
  send(
    message: APXMessage,
    options?: {
      priority?: "low" | "normal" | "high" | "critical";
      persistent?: boolean;
      requiresAck?: boolean;
      timeout?: number;
    }
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const messageId = message.id || this.generateMessageId();
      const priority = options?.priority || "normal";
      const persistent = options?.persistent ?? false;
      const requiresAck = options?.requiresAck ?? false;
      const timeout = options?.timeout || 30000;

      // Add sequence number and metadata
      const enhancedMessage: APXMessage = {
        ...message,
        id: messageId,
        data: {
          ...message.data,
          sequence: ++this.messageSequence,
          requiresAck,
          timestamp: new Date().toISOString(),
        },
      };

      if (
        this.connectionState.status === "connected" &&
        this.ws?.readyState === WebSocket.OPEN
      ) {
        try {
          this.sendImmediate(enhancedMessage);

          if (requiresAck) {
            // Set up acknowledgment handling
            const ackTimeout = setTimeout(() => {
              this.pendingAcks.delete(messageId);
              reject(new Error(`Message acknowledgment timeout: ${messageId}`));
            }, timeout);

            this.pendingAcks.set(messageId, {
              resolve,
              reject,
              timeout: ackTimeout,
            });
          } else {
            resolve();
          }
        } catch (error) {
          reject(error);
        }
      } else {
        // Buffer message for later
        const bufferedMessage: MessageBuffer = {
          id: messageId,
          message: enhancedMessage,
          timestamp: new Date(),
          attempts: 0,
          priority,
          persistent,
        };

        this.messageBuffer.set(messageId, bufferedMessage);

        // Manage buffer size
        if (this.messageBuffer.size > this.config.messageQueueSize) {
          this.pruneMessageBuffer();
        }

        if (requiresAck) {
          // For buffered messages, resolve immediately but track for later ack
          const ackTimeout = setTimeout(() => {
            this.pendingAcks.delete(messageId);
            reject(
              new Error(`Buffered message acknowledgment timeout: ${messageId}`)
            );
          }, timeout * 2); // Double timeout for buffered messages

          this.pendingAcks.set(messageId, {
            resolve,
            reject,
            timeout: ackTimeout,
          });
        } else {
          resolve();
        }
      }
    });
  }

  private sendImmediate(message: APXMessage): void {
    try {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        throw new Error("WebSocket not connected");
      }

      const messageStr = JSON.stringify(message);
      const messageSize = new Blob([messageStr]).size;

      // Check if message exceeds buffer size
      if (messageSize > this.config.bufferSize) {
        throw new Error(
          `Message size (${messageSize}) exceeds buffer size (${this.config.bufferSize})`
        );
      }

      this.ws.send(messageStr);

      this.connectionState.messagesSent++;
      this.connectionState.bytesTransferred += messageSize;

      this.logger.debug("Sent APIX message", {
        type: message.type,
        id: message.id,
        sessionId: message.sessionId,
        size: messageSize,
        sequence: message.data?.sequence,
      });
    } catch (error) {
      this.connectionState.errors.push(
        error instanceof Error ? error.message : String(error)
      );

      this.errorHandler.handleError({
        message: "Failed to send APIX message",
        stack: error instanceof Error ? error.stack : undefined,
        component: "APXWebSocket",
        severity: "medium",
        category: "system",
        metadata: {
          messageType: message.type,
          messageId: message.id,
          sessionId: message.sessionId,
        },
      });
      throw error;
    }
  }

  subscribe(
    messageType: string,
    callback: (message: APXMessage) => void
  ): string {
    const subscriptionId = this.generateMessageId();

    if (!this.subscribers.has(messageType)) {
      this.subscribers.set(messageType, new Set());
    }

    this.subscribers.get(messageType)!.add(callback);

    this.logger.debug("Added APIX subscription", {
      messageType,
      subscriptionId,
    });

    return subscriptionId;
  }

  unsubscribe(
    messageType: string,
    callback: (message: APXMessage) => void
  ): void {
    const subscribers = this.subscribers.get(messageType);
    if (subscribers) {
      subscribers.delete(callback);
      if (subscribers.size === 0) {
        this.subscribers.delete(messageType);
      }
    }
  }

  // Session Management
  createSession(
    agentId: string,
    userId: string,
    initialState: Record<string, any> = {},
    ttl: number = 3600000
  ): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const session: SessionState = {
      id: sessionId,
      agentId,
      userId,
      state: initialState,
      ttl,
      lastActivity: new Date().toISOString(),
      metadata: {},
      version: 1,
      checksum: "",
    };

    this.sessionStates.set(sessionId, session);

    // Sync with server
    this.send({
      id: this.generateMessageId(),
      type: "session_sync",
      timestamp: new Date().toISOString(),
      sessionId,
      data: session,
    });

    return sessionId;
  }

  updateSession(sessionId: string, updates: Partial<SessionState>): void {
    const session = this.sessionStates.get(sessionId);
    if (session) {
      const updatedSession = {
        ...session,
        ...updates,
        lastActivity: new Date().toISOString(),
      };

      this.sessionStates.set(sessionId, updatedSession);

      // Sync with server
      this.send({
        id: this.generateMessageId(),
        type: "session_sync",
        timestamp: new Date().toISOString(),
        sessionId,
        data: updatedSession,
      });
    }
  }

  getSession(sessionId: string): SessionState | undefined {
    return this.sessionStates.get(sessionId);
  }

  destroySession(sessionId: string): void {
    this.sessionStates.delete(sessionId);

    this.send({
      id: this.generateMessageId(),
      type: "session_sync",
      timestamp: new Date().toISOString(),
      sessionId,
      data: { destroy: true },
    });
  }

  // Tool Execution
  executeToolCall(
    toolName: string,
    parameters: Record<string, any>,
    sessionId?: string
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const callId = this.generateMessageId();

      // Set up response handler
      const responseHandler = (message: APXMessage) => {
        if (message.data?.callId === callId) {
          this.unsubscribe("tool_call_response", responseHandler);

          if (message.data.success) {
            resolve(message.data.result);
          } else {
            reject(new Error(message.data.error || "Tool execution failed"));
          }
        }
      };

      this.subscribe("tool_call_response", responseHandler);

      // Send tool call request
      this.send({
        id: callId,
        type: "tool_call_start",
        timestamp: new Date().toISOString(),
        sessionId,
        data: {
          callId,
          toolName,
          parameters,
        },
      });

      // Set timeout
      setTimeout(() => {
        this.unsubscribe("tool_call_response", responseHandler);
        reject(new Error("Tool execution timeout"));
      }, 30000);
    });
  }

  // Connection Management
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, "Client disconnect");
      this.ws = null;
    }

    this.connectionState.status = "disconnected";
    this.connectionId = null;
  }

  getConnectionStatus(): ConnectionState & {
    connectionId: string | null;
    bufferedMessages: number;
    activeSessions: number;
    pendingAcks: number;
    stateChecksum: string;
  } {
    return {
      ...this.connectionState,
      connectionId: this.connectionId,
      bufferedMessages: this.messageBuffer.size,
      activeSessions: this.sessionStates.size,
      pendingAcks: this.pendingAcks.size,
      stateChecksum: this.stateChecksum,
    };
  }

  getPerformanceMetrics(): {
    latency: number;
    messagesSent: number;
    messagesReceived: number;
    bytesTransferred: number;
    uptime: number;
    reconnectCount: number;
    errorCount: number;
  } {
    const uptime = this.connectionState.lastConnected
      ? Date.now() - this.connectionState.lastConnected.getTime()
      : 0;

    return {
      latency: this.connectionState.latency,
      messagesSent: this.connectionState.messagesSent,
      messagesReceived: this.connectionState.messagesReceived,
      bytesTransferred: this.connectionState.bytesTransferred,
      uptime,
      reconnectCount: this.connectionState.reconnectAttempts,
      errorCount: this.connectionState.errors.length,
    };
  }

  // Enhanced session and state management methods
  private initializeFromStorage(): void {
    if (!this.config.enableStatePersistence) {
      return;
    }

    try {
      const storedState = this.persistentStorage.getItem("apix_session_states");
      const storedChecksum = this.persistentStorage.getItem(
        "apix_state_checksum"
      );

      if (storedState && storedChecksum) {
        const parsedState = JSON.parse(storedState);
        const currentChecksum = this.calculateChecksum(parsedState);

        if (currentChecksum === storedChecksum) {
          // Restore session states
          for (const [sessionId, sessionData] of Object.entries(parsedState)) {
            this.sessionStates.set(sessionId, sessionData as SessionState);
          }

          this.stateChecksum = currentChecksum;

          this.logger.info("Restored session states from storage", {
            sessionCount: this.sessionStates.size,
            checksum: currentChecksum,
          });
        } else {
          this.logger.warn("State checksum mismatch, clearing stored state");
          this.clearStoredState();
        }
      }
    } catch (error) {
      this.logger.error("Failed to initialize from storage", { error });
      this.clearStoredState();
    }
  }

  private backupState(): void {
    if (!this.config.enableStatePersistence) {
      return;
    }

    try {
      const stateObject = Object.fromEntries(this.sessionStates.entries());
      const checksum = this.calculateChecksum(stateObject);

      this.persistentStorage.setItem(
        "apix_session_states",
        JSON.stringify(stateObject)
      );
      this.persistentStorage.setItem("apix_state_checksum", checksum);
      this.stateChecksum = checksum;

      this.logger.debug("Backed up session states", {
        sessionCount: this.sessionStates.size,
        checksum,
      });
    } catch (error) {
      this.logger.error("Failed to backup state", { error });
    }
  }

  private clearStoredState(): void {
    this.persistentStorage.removeItem("apix_session_states");
    this.persistentStorage.removeItem("apix_state_checksum");
  }

  private calculateChecksum(data: any): string {
    const str = JSON.stringify(data, Object.keys(data).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  private startStateBackup(): void {
    if (!this.config.enableStatePersistence) {
      return;
    }

    this.stateBackupTimer = setInterval(() => {
      this.backupState();
    }, this.config.stateBackupInterval);
  }

  private stopStateBackup(): void {
    if (this.stateBackupTimer) {
      clearInterval(this.stateBackupTimer);
      this.stateBackupTimer = null;
    }
  }

  private resumeSessionStates(): void {
    if (this.sessionStates.size === 0) {
      return;
    }

    // Send session resume message to server
    const sessionIds = Array.from(this.sessionStates.keys());

    this.send(
      {
        id: this.generateMessageId(),
        type: "session_resume" as any,
        timestamp: new Date().toISOString(),
        data: {
          sessionIds,
          connectionId: this.connectionId,
          checksum: this.stateChecksum,
        },
      },
      { priority: "high" }
    );

    this.logger.info("Resuming session states", {
      sessionCount: sessionIds.length,
      connectionId: this.connectionId,
    });
  }

  private pruneMessageBuffer(): void {
    // Remove oldest non-persistent messages
    const messages = Array.from(this.messageBuffer.values())
      .filter((msg) => !msg.persistent)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    const toRemove = Math.ceil(this.config.messageQueueSize * 0.1); // Remove 10%

    for (let i = 0; i < toRemove && i < messages.length; i++) {
      this.messageBuffer.delete(messages[i].id);
    }

    this.logger.debug("Pruned message buffer", {
      removed: toRemove,
      remaining: this.messageBuffer.size,
    });
  }

  private handleAcknowledgment(messageId: string): void {
    const pending = this.pendingAcks.get(messageId);
    if (pending) {
      clearTimeout(pending.timeout);
      pending.resolve();
      this.pendingAcks.delete(messageId);

      this.logger.debug("Message acknowledged", { messageId });
    }
  }

  private sendAcknowledgment(messageId: string): void {
    this.send(
      {
        id: this.generateMessageId(),
        type: "ack" as any,
        timestamp: new Date().toISOString(),
        data: { messageId },
      },
      { priority: "high" }
    ).catch((error) => {
      this.logger.warn("Failed to send acknowledgment", { messageId, error });
    });
  }

  private handleServerError(message: APXMessage): void {
    this.connectionState.errors.push(
      message.data?.error || "Unknown server error"
    );

    this.logger.error("Server error received", {
      error: message.data?.error,
      code: message.data?.code,
      messageId: message.id,
    });

    this.notifySubscribers("error", message);
  }

  private handleConnectionTimeout(): void {
    this.logger.warn("Connection timeout");
    this.connectionState.status = "error";
    this.connectionState.errors.push("Connection timeout");

    if (this.ws) {
      this.ws.close(1000, "Connection timeout");
    }
  }

  private shouldReconnect(closeCode: number): boolean {
    // Don't reconnect for certain close codes
    const noReconnectCodes = [
      1000, // Normal closure
      1001, // Going away
      1005, // No status received
      4000, // Custom: Authentication failed
      4001, // Custom: Authorization failed
      4002, // Custom: Rate limited
    ];

    return !noReconnectCodes.includes(closeCode);
  }

  private updateSessionActivity(sessionId: string): void {
    const session = this.sessionStates.get(sessionId);
    if (session) {
      session.lastActivity = new Date().toISOString();
      session.version++;
      this.sessionStates.set(sessionId, session);
    }
  }

  // Cleanup expired sessions
  private cleanupSessions(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessionStates.entries()) {
      const lastActivity = new Date(session.lastActivity).getTime();
      if (now - lastActivity > session.ttl) {
        this.destroySession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug("Cleaned up expired sessions", {
        count: cleanedCount,
        remaining: this.sessionStates.size,
      });
    }
  }
}

// Global WebSocket client instance
let apxClient: APXWebSocketClient | null = null;

export function getAPXClient(
  config?: Partial<WebSocketConfig>
): APXWebSocketClient {
  if (!apxClient) {
    apxClient = new APXWebSocketClient(config);

    // Start session cleanup interval
    setInterval(() => {
      (apxClient as any)?.cleanupSessions();
    }, 60000); // Every minute
  }
  return apxClient;
}

export { APXWebSocketClient };
