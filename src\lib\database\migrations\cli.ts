import { db } from '../index';
import { migrationManager } from './index';

async function main() {
    const command = process.argv[2];

    try {
        await db.connect();

        switch (command) {
            case 'migrate':
                console.log('Running migrations...');
                await migrationManager.migrate();
                console.log('✅ Migrations completed successfully');
                break;

            case 'rollback':
                const steps = parseInt(process.argv[3]) || 1;
                console.log(`Rolling back ${steps} migration(s)...`);
                await migrationManager.rollback(steps);
                console.log('✅ Rollback completed successfully');
                break;

            case 'status':
                console.log('Checking migration status...');
                const status = await migrationManager.status();

                console.log('\nMigration Status:');
                console.log('=================');

                status.forEach(migration => {
                    const statusSymbol = migration.applied ? '✅' : '❌';
                    console.log(`${statusSymbol} ${migration.name} (${migration.timestamp.toISOString()})`);
                });
                break;

            default:
                console.error(`
Invalid command: ${command}

Usage:
  npm run db:migrate         Run pending migrations
  npm run db:rollback [n]    Rollback last n migrations (default: 1)
  npm run db:status         Show migration status
`);
                process.exit(1);
        }
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    } finally {
        await db.disconnect();
    }
}

main(); 