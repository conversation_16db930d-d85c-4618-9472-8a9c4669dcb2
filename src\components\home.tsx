import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bell, Settings, Menu, Search } from "lucide-react";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { MetricCard } from "@/components/ui/metric-card";
import AIProviderPanel from "./dashboard/AIProviderPanel";
import AgentControlPanel from "./dashboard/AgentControlPanel";
import AnalyticsPanel from "./dashboard/AnalyticsPanel";
import EventMonitor from "./core/EventMonitor";
import DashboardOverview from "./dashboard/DashboardOverview";
import RequestOrchestrator from "./core/RequestOrchestrator";
import ProviderManager from "./core/ProviderManager";
import HITLPanel from "./core/HITLPanel";
import WidgetGenerator from "./core/WidgetGenerator";
import ToolExecutionPanel from "./core/ToolExecutionPanel";
import ThemeToggle from "@/components/ui/theme-toggle";

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-surface text-foreground flex transition-colors duration-200">
      {/* Sidebar */}
      <div className="w-64 border-r bg-card/95 backdrop-blur-xl hidden md:block shadow-large">
        <div className="p-4 flex items-center space-x-2">
          <div className="bg-gradient-primary h-8 w-8 rounded-xl flex items-center justify-center shadow-medium">
            <span className="text-white font-bold">S</span>
          </div>
          <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            SynapseAI
          </h1>
        </div>
        <nav className="p-2">
          <ul className="space-y-1">
            <li>
              <Button
                variant="ghost"
                className="w-full justify-start h-12 rounded-xl hover:bg-accent/50 hover:shadow-soft hover:scale-[1.02] transition-all duration-200"
              >
                <span className="mr-2">📊</span> Dashboard
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start">
                <span className="mr-2">🤖</span> AI Providers
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start">
                <span className="mr-2">🧠</span> Agents
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start">
                <span className="mr-2">📈</span> Analytics
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start">
                <span className="mr-2">🔄</span> Events
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start">
                <span className="mr-2">⚙️</span> Settings
              </Button>
            </li>
          </ul>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="border-b border-border/50 p-4 flex items-center justify-between bg-card/80 backdrop-blur-xl shadow-soft">
          <div className="flex items-center md:hidden">
            <Button variant="ghost" size="icon">
              <Menu className="h-5 w-5" />
            </Button>
          </div>
          <div className="relative w-full max-w-md mx-4 hidden md:block">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search..."
              className="pl-8 h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Settings className="h-5 w-5" />
            </Button>
            <ThemeToggle variant="dropdown" size="default" />
            <Avatar>
              <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin" />
              <AvatarFallback>AD</AvatarFallback>
            </Avatar>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="mb-6">
            <h1 className="text-3xl font-bold">SynapseAI Dashboard</h1>
            <p className="text-muted-foreground">
              Universal AI Orchestration Platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <MetricCard
              title="Total Providers"
              value="5"
              subtitle="OpenAI, Claude, Gemini, Groq, Mistral"
              status="success"
              trend="up"
              change={{
                value: 25,
                period: "last month",
              }}
            />
            <MetricCard
              title="Active Agents"
              value="12"
              subtitle="3 deployed in production"
              status="info"
              trend="up"
              change={{
                value: 15,
                period: "last week",
              }}
            />
            <MetricCard
              title="API Requests (24h)"
              value="1,248"
              status="success"
              trend="up"
              change={{
                value: 12,
                period: "yesterday",
              }}
            />
          </div>

          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="providers">AI Providers</TabsTrigger>
              <TabsTrigger value="agents">Agent Control</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="events">Event Monitor</TabsTrigger>
              <TabsTrigger value="orchestrator">Orchestrator</TabsTrigger>
              <TabsTrigger value="hitl">HITL Control</TabsTrigger>
              <TabsTrigger value="widgets">Widget Generator</TabsTrigger>
              <TabsTrigger value="manager">Manager</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="space-y-4">
              <DashboardOverview />
            </TabsContent>
            <TabsContent value="providers" className="space-y-4">
              <AIProviderPanel />
            </TabsContent>
            <TabsContent value="agents" className="space-y-4">
              <AgentControlPanel />
            </TabsContent>
            <TabsContent value="analytics" className="space-y-4">
              <AnalyticsPanel />
            </TabsContent>
            <TabsContent value="events" className="space-y-4">
              <EventMonitor />
            </TabsContent>
            <TabsContent value="orchestrator" className="space-y-4">
              <RequestOrchestrator />
            </TabsContent>
            <TabsContent value="hitl" className="space-y-4">
              <HITLPanel />
            </TabsContent>
            <TabsContent value="widgets" className="space-y-4">
              <WidgetGenerator />
            </TabsContent>
            <TabsContent value="manager" className="space-y-4">
              <ProviderManager showAdvanced={true} />
            </TabsContent>
            <TabsContent value="tools" className="space-y-4">
              <ToolExecutionPanel />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Home;
