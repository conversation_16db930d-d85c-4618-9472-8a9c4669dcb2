import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter } from "react-router-dom";
import "./index.css";
import App from "./App.tsx";
import { TempoDevtools } from "tempo-devtools";
import { getThemeManager } from "./lib/theme";

// Initialize Tempo Devtools

// Initialize theme manager early
getThemeManager();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </StrictMode>
);
