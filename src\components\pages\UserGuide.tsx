import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  BookOpen,
  Play,
  Search,
  Star,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Code,
  Zap,
  Brain,
  Activity,
  Settings,
  Users,
  BarChart3,
  Shield,
  Globe,
  ArrowRight,
  ExternalLink,
  Copy,
  Download,
  Video,
  FileText,
  MessageSquare,
  Rocket,
  Target,
  Clock,
  TrendingUp,
  Database,
  Server,
  Cloud,
  Lock,
  Smartphone,
  Monitor,
  Tablet,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Eye,
  EyeOff,
  ChevronRight,
  ChevronDown,
  Plus,
  Minus,
  RefreshCw,
  Filter,
  SortAsc,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Link,
  Share2,
  Bookmark,
  Heart,
  ThumbsUp,
  MessageCircle,
  Flag,
  Info,
  HelpCircle,
  X,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Toaster } from "@/components/ui/toaster";

interface GuideSection {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: string;
  content: React.ReactNode;
  videoUrl?: string;
  codeExample?: string;
  prerequisites?: string[];
  nextSteps?: string[];
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
  tags: string[];
}

interface MissingFeature {
  id: string;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "critical";
  category: string;
  votes: number;
  status: "requested" | "planned" | "in-progress" | "completed";
  estimatedRelease?: string;
}

const UserGuide: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedDifficulty, setSelectedDifficulty] = useState("all");
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [completedSections, setCompletedSections] = useState<Set<string>>(
    new Set()
  );
  const [bookmarkedSections, setBookmarkedSections] = useState<Set<string>>(
    new Set()
  );
  const [userProgress, setUserProgress] = useState(0);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [feedbackRatings, setFeedbackRatings] = useState<
    Record<string, number>
  >({});

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const pulseVariants = {
    pulse: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
      },
    },
  };

  // Guide sections data
  const guideSections: GuideSection[] = [
    {
      id: "getting-started",
      title: "Getting Started with SynapseAI",
      description:
        "Learn the basics of setting up and using SynapseAI for the first time",
      icon: <Rocket className="h-6 w-6" />,
      difficulty: "beginner",
      estimatedTime: "10 minutes",
      videoUrl: "https://example.com/getting-started",
      prerequisites: [],
      nextSteps: ["provider-setup", "first-request"],
      content: (
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              What is SynapseAI?
            </h3>
            <p className="text-gray-700 mb-4">
              SynapseAI is a Universal AI Orchestration Platform that
              intelligently routes your AI requests across multiple providers
              (OpenAI, Claude, Gemini, etc.) to optimize for cost, performance,
              and reliability.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <Brain className="h-8 w-8 text-blue-500 mb-2" />
                <h4 className="font-medium">Smart Routing</h4>
                <p className="text-sm text-gray-600">
                  Automatically selects the best AI provider for each request
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <BarChart3 className="h-8 w-8 text-green-500 mb-2" />
                <h4 className="font-medium">Cost Optimization</h4>
                <p className="text-sm text-gray-600">
                  Reduces AI costs by up to 40% through intelligent routing
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <Shield className="h-8 w-8 text-purple-500 mb-2" />
                <h4 className="font-medium">Reliability</h4>
                <p className="text-sm text-gray-600">
                  Built-in fallbacks and error handling for 99.9% uptime
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Start Steps</h3>
            <div className="space-y-3">
              {[
                {
                  step: 1,
                  title: "Sign up for SynapseAI",
                  desc: "Create your free account",
                },
                {
                  step: 2,
                  title: "Add AI Providers",
                  desc: "Connect your OpenAI, Claude, or other AI provider accounts",
                },
                {
                  step: 3,
                  title: "Send Your First Request",
                  desc: "Use our dashboard or SDK to make your first AI request",
                },
                {
                  step: 4,
                  title: "Monitor & Optimize",
                  desc: "View analytics and optimize your AI usage",
                },
              ].map((item) => (
                <div
                  key={item.step}
                  className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg"
                >
                  <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-semibold text-sm">
                    {item.step}
                  </div>
                  <div>
                    <h4 className="font-medium">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ),
      codeExample: `// Install the SynapseAI SDK
npm install @synapseai/sdk

// Initialize the client
import { SynapseAI } from '@synapseai/sdk';

const client = new SynapseAI({
  apiKey: 'your-api-key',
  environment: 'production'
});

// Send your first request
const response = await client.complete({
  messages: [{
    role: 'user',
    content: 'Hello, world!'
  }]
});

console.log(response.content);`,
    },
    {
      id: "provider-setup",
      title: "Setting Up AI Providers",
      description:
        "Configure and manage multiple AI providers for optimal performance",
      icon: <Settings className="h-6 w-6" />,
      difficulty: "beginner",
      estimatedTime: "15 minutes",
      prerequisites: ["getting-started"],
      nextSteps: ["routing-configuration", "first-request"],
      content: (
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              <h4 className="font-medium text-yellow-800">Pro Tip</h4>
            </div>
            <p className="text-yellow-700 text-sm">
              Start with 2-3 providers for the best balance of performance and
              cost optimization.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                name: "OpenAI",
                icon: "🤖",
                description: "Best for general-purpose tasks and coding",
                setup: "Add your OpenAI API key from platform.openai.com",
                models: ["GPT-4", "GPT-3.5-turbo", "GPT-4-vision"],
                pricing: "$0.03/1K tokens",
              },
              {
                name: "Anthropic Claude",
                icon: "🧠",
                description: "Excellent for analysis and long-form content",
                setup: "Get your API key from console.anthropic.com",
                models: ["Claude-3-Opus", "Claude-3-Sonnet", "Claude-3-Haiku"],
                pricing: "$0.015/1K tokens",
              },
              {
                name: "Google Gemini",
                icon: "💎",
                description: "Great for multimodal tasks and reasoning",
                setup: "Create API key in Google AI Studio",
                models: ["Gemini-Pro", "Gemini-Ultra", "Gemini-Vision"],
                pricing: "$0.0125/1K tokens",
              },
              {
                name: "Groq",
                icon: "⚡",
                description: "Ultra-fast inference for real-time applications",
                setup: "Sign up at console.groq.com for API access",
                models: ["Llama-3-70B", "Mixtral-8x7B", "Llama-3-8B"],
                pricing: "$0.001/1K tokens",
              },
            ].map((provider) => (
              <Card
                key={provider.name}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <span className="text-2xl">{provider.icon}</span>
                    {provider.name}
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    {provider.description}
                  </p>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h5 className="font-medium text-sm mb-1">Setup:</h5>
                    <p className="text-sm text-gray-600">{provider.setup}</p>
                  </div>
                  <div>
                    <h5 className="font-medium text-sm mb-1">
                      Available Models:
                    </h5>
                    <div className="flex flex-wrap gap-1">
                      {provider.models.map((model) => (
                        <Badge
                          key={model}
                          variant="secondary"
                          className="text-xs"
                        >
                          {model}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-sm font-medium text-green-600">
                      {provider.pricing}
                    </span>
                    <Button size="sm" variant="outline">
                      Add Provider
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ),
      codeExample: `// Add a new provider via API
const provider = await client.providers.create({
  name: 'My OpenAI Provider',
  type: 'openai',
  apiKey: process.env.OPENAI_API_KEY,
  models: ['gpt-4', 'gpt-3.5-turbo'],
  config: {
    maxTokens: 4096,
    temperature: 0.7
  }
});

// Test the provider
const testResult = await client.providers.test(provider.id);
console.log('Provider test:', testResult.success);`,
    },
    {
      id: "first-request",
      title: "Making Your First AI Request",
      description:
        "Send your first request through SynapseAI's intelligent routing system",
      icon: <Zap className="h-6 w-6" />,
      difficulty: "beginner",
      estimatedTime: "5 minutes",
      prerequisites: ["getting-started", "provider-setup"],
      nextSteps: ["advanced-routing", "analytics-monitoring"],
      content: (
        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">
              🎉 You're Ready!
            </h4>
            <p className="text-green-700 text-sm">
              With your providers set up, you can now send AI requests that will
              be intelligently routed for optimal performance and cost.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Request Methods</h3>

            <Tabs defaultValue="dashboard" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                <TabsTrigger value="sdk">SDK</TabsTrigger>
                <TabsTrigger value="api">REST API</TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard" className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Using the Web Dashboard</h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Navigate to the Request Orchestrator tab</li>
                    <li>Type your message in the chat interface</li>
                    <li>
                      Click Send - SynapseAI will automatically select the best
                      provider
                    </li>
                    <li>
                      View the response along with routing details and costs
                    </li>
                  </ol>
                </div>
              </TabsContent>

              <TabsContent value="sdk" className="space-y-4">
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Using the TypeScript SDK</h4>
                  <p className="text-sm mb-3">
                    The SDK provides the most flexibility and control:
                  </p>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                    <pre>{`import { SynapseAI } from '@synapseai/sdk';

const client = new SynapseAI({
  apiKey: process.env.SYNAPSEAI_API_KEY
});

const response = await client.complete({
  messages: [{
    role: 'user',
    content: 'Explain quantum computing in simple terms'
  }],
  // Optional: specify routing preferences
  context: {
    requestType: 'text',
    complexity: 'medium',
    priority: 'normal'
  }
});

console.log('Response:', response.content);
console.log('Provider used:', response.provider);
console.log('Cost:', response.cost);`}</pre>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="api" className="space-y-4">
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Using the REST API</h4>
                  <p className="text-sm mb-3">
                    Direct HTTP requests for any programming language:
                  </p>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                    <pre>{`curl -X POST https://api.synapseai.com/v1/complete \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{
      "role": "user",
      "content": "Write a haiku about AI"
    }],
    "context": {
      "requestType": "creative",
      "complexity": "low"
    }
  }'`}</pre>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      ),
    },
    {
      id: "routing-configuration",
      title: "Advanced Routing Configuration",
      description:
        "Customize how SynapseAI routes your requests for optimal results",
      icon: <Activity className="h-6 w-6" />,
      difficulty: "intermediate",
      estimatedTime: "20 minutes",
      prerequisites: ["provider-setup", "first-request"],
      nextSteps: ["analytics-monitoring", "agent-management"],
      content: (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">
              🎯 Routing Intelligence
            </h4>
            <p className="text-blue-700 text-sm">
              SynapseAI's routing system considers cost, latency, success rates,
              and provider capabilities to make optimal decisions for each
              request.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Routing Strategies
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    name: "Cost Optimization",
                    description:
                      "Prioritizes the lowest cost providers while maintaining quality",
                    icon: "💰",
                    useCase: "Bulk processing, non-critical tasks",
                  },
                  {
                    name: "Performance First",
                    description:
                      "Routes to the fastest providers for real-time applications",
                    icon: "⚡",
                    useCase: "Live chat, interactive applications",
                  },
                  {
                    name: "Quality Focused",
                    description:
                      "Uses the most capable models for complex tasks",
                    icon: "🎯",
                    useCase: "Analysis, creative writing, complex reasoning",
                  },
                  {
                    name: "Balanced",
                    description: "Optimizes across cost, speed, and quality",
                    icon: "⚖️",
                    useCase: "General purpose applications",
                  },
                ].map((strategy) => (
                  <div key={strategy.name} className="border rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-lg">{strategy.icon}</span>
                      <h5 className="font-medium">{strategy.name}</h5>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {strategy.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      Best for: {strategy.useCase}
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Custom Rules
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h5 className="font-medium text-sm mb-2">
                      Request Type Rules
                    </h5>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Code generation → Use Claude or GPT-4</li>
                      <li>• Image analysis → Route to vision-capable models</li>
                      <li>
                        • Creative writing → Prefer high-temperature models
                      </li>
                      <li>
                        • Data analysis → Use models with large context windows
                      </li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h5 className="font-medium text-sm mb-2">Fallback Rules</h5>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>
                        • Rate limit exceeded → Switch to alternative provider
                      </li>
                      <li>
                        • High latency detected → Route to faster provider
                      </li>
                      <li>• Provider error → Automatic failover</li>
                      <li>
                        • Cost threshold reached → Use cheaper alternatives
                      </li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h5 className="font-medium text-sm mb-2">
                      Time-based Rules
                    </h5>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Peak hours → Distribute load across providers</li>
                      <li>• Off-peak → Use cost-optimized routing</li>
                      <li>• Maintenance windows → Avoid specific providers</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
      codeExample: `// Configure custom routing rules
const routingConfig = {
  strategy: 'balanced', // cost, performance, quality, balanced
  rules: [
    {
      condition: { requestType: 'code' },
      preferredProviders: ['openai', 'anthropic'],
      maxCost: 0.05
    },
    {
      condition: { priority: 'urgent' },
      strategy: 'performance',
      maxLatency: 1000
    },
    {
      condition: { complexity: 'high' },
      preferredModels: ['gpt-4', 'claude-3-opus'],
      excludeProviders: ['groq']
    }
  ],
  fallback: {
    enabled: true,
    maxRetries: 3,
    backoffStrategy: 'exponential'
  }
};

// Apply routing configuration
await client.routing.configure(routingConfig);`,
    },
    {
      id: "analytics-monitoring",
      title: "Analytics & Monitoring",
      description: "Track performance, costs, and optimize your AI operations",
      icon: <BarChart3 className="h-6 w-6" />,
      difficulty: "intermediate",
      estimatedTime: "15 minutes",
      prerequisites: ["first-request"],
      nextSteps: ["cost-optimization", "troubleshooting"],
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              {
                title: "Request Volume",
                value: "12,450",
                change: "+12.5%",
                icon: <Zap className="h-5 w-5 text-blue-500" />,
                trend: "up",
              },
              {
                title: "Success Rate",
                value: "97.2%",
                change: "+0.8%",
                icon: <CheckCircle className="h-5 w-5 text-green-500" />,
                trend: "up",
              },
              {
                title: "Average Cost",
                value: "$0.0234",
                change: "-15.3%",
                icon: <TrendingUp className="h-5 w-5 text-purple-500" />,
                trend: "down",
              },
            ].map((metric) => (
              <Card key={metric.title}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">
                      {metric.title}
                    </span>
                    {metric.icon}
                  </div>
                  <div className="text-2xl font-bold mb-1">{metric.value}</div>
                  <div
                    className={`text-sm flex items-center gap-1 ${
                      metric.trend === "up" ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {metric.trend === "up" ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingUp className="h-3 w-3 rotate-180" />
                    )}
                    {metric.change}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Key Metrics to Monitor</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    category: "Performance",
                    metrics: [
                      "Response time",
                      "Success rate",
                      "Error rate",
                      "Throughput",
                    ],
                  },
                  {
                    category: "Cost",
                    metrics: [
                      "Cost per request",
                      "Monthly spend",
                      "Cost by provider",
                      "Token usage",
                    ],
                  },
                  {
                    category: "Usage",
                    metrics: [
                      "Request volume",
                      "Provider distribution",
                      "Model usage",
                      "Peak hours",
                    ],
                  },
                  {
                    category: "Quality",
                    metrics: [
                      "User satisfaction",
                      "Response quality",
                      "Routing accuracy",
                      "Fallback rate",
                    ],
                  },
                ].map((group) => (
                  <div key={group.category}>
                    <h5 className="font-medium text-sm mb-2">
                      {group.category}
                    </h5>
                    <div className="grid grid-cols-2 gap-2">
                      {group.metrics.map((metric) => (
                        <div
                          key={metric}
                          className="bg-gray-50 p-2 rounded text-sm"
                        >
                          {metric}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alerts & Notifications</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {[
                    {
                      type: "Cost Alert",
                      description: "Monthly spend exceeds $500",
                      status: "active",
                      icon: (
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      ),
                    },
                    {
                      type: "Performance Alert",
                      description: "Response time > 2 seconds",
                      status: "active",
                      icon: <Clock className="h-4 w-4 text-red-500" />,
                    },
                    {
                      type: "Error Rate Alert",
                      description: "Error rate > 5%",
                      status: "inactive",
                      icon: <CheckCircle className="h-4 w-4 text-green-500" />,
                    },
                    {
                      type: "Usage Alert",
                      description: "Daily requests > 10,000",
                      status: "inactive",
                      icon: <Info className="h-4 w-4 text-blue-500" />,
                    },
                  ].map((alert) => (
                    <div
                      key={alert.type}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {alert.icon}
                        <div>
                          <h6 className="font-medium text-sm">{alert.type}</h6>
                          <p className="text-xs text-gray-600">
                            {alert.description}
                          </p>
                        </div>
                      </div>
                      <Badge
                        variant={
                          alert.status === "active" ? "default" : "secondary"
                        }
                      >
                        {alert.status}
                      </Badge>
                    </div>
                  ))}
                </div>

                <Button className="w-full" variant="outline">
                  Configure Alerts
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
      codeExample: `// Get analytics data
const analytics = await client.analytics.get({
  timeRange: '7d',
  metrics: ['requests', 'cost', 'latency', 'errors'],
  groupBy: 'provider'
});

// Set up cost alerts
await client.alerts.create({
  name: 'Monthly Cost Alert',
  condition: {
    metric: 'monthly_cost',
    operator: 'greater_than',
    threshold: 500
  },
  notifications: [
    { type: 'email', target: '<EMAIL>' },
    { type: 'webhook', target: 'https://hooks.slack.com/...' }
  ]
});

// Export analytics data
const report = await client.analytics.export({
  format: 'csv',
  timeRange: '30d',
  includeMetrics: ['all']
});`,
    },
    {
      id: "agent-management",
      title: "AI Agent Management",
      description: "Create, deploy, and manage AI agents for specific tasks",
      icon: <Brain className="h-6 w-6" />,
      difficulty: "advanced",
      estimatedTime: "30 minutes",
      prerequisites: ["routing-configuration", "analytics-monitoring"],
      nextSteps: ["advanced-features", "troubleshooting"],
      content: (
        <div className="space-y-6">
          <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
            <h4 className="font-medium text-purple-800 mb-2">🤖 AI Agents</h4>
            <p className="text-purple-700 text-sm">
              AI Agents are specialized configurations that handle specific
              types of requests with custom prompts, routing rules, and behavior
              patterns.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Agent Types</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    type: "Customer Support",
                    icon: "🎧",
                    description:
                      "Handles customer inquiries with predefined knowledge base",
                    features: [
                      "FAQ integration",
                      "Escalation rules",
                      "Sentiment analysis",
                    ],
                  },
                  {
                    type: "Content Generator",
                    icon: "✍️",
                    description:
                      "Creates marketing content, blog posts, and social media",
                    features: [
                      "Brand voice",
                      "SEO optimization",
                      "Content templates",
                    ],
                  },
                  {
                    type: "Code Assistant",
                    icon: "💻",
                    description:
                      "Helps with code review, debugging, and documentation",
                    features: [
                      "Language detection",
                      "Best practices",
                      "Security scanning",
                    ],
                  },
                  {
                    type: "Data Analyst",
                    icon: "📊",
                    description:
                      "Analyzes data and generates insights and reports",
                    features: [
                      "Chart generation",
                      "Trend analysis",
                      "Report templates",
                    ],
                  },
                ].map((agent) => (
                  <div key={agent.type} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-xl">{agent.icon}</span>
                      <h5 className="font-medium">{agent.type}</h5>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      {agent.description}
                    </p>
                    <div className="space-y-1">
                      {agent.features.map((feature) => (
                        <div
                          key={feature}
                          className="flex items-center gap-2 text-xs"
                        >
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Agent Lifecycle</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  {[
                    {
                      phase: "Design",
                      description:
                        "Define agent purpose, capabilities, and behavior",
                      tasks: [
                        "Set system prompt",
                        "Choose providers",
                        "Configure routing",
                      ],
                    },
                    {
                      phase: "Development",
                      description: "Build and test the agent with sample data",
                      tasks: [
                        "Test responses",
                        "Validate routing",
                        "Performance testing",
                      ],
                    },
                    {
                      phase: "Deployment",
                      description: "Deploy agent to production environment",
                      tasks: [
                        "Version control",
                        "Gradual rollout",
                        "Monitor metrics",
                      ],
                    },
                    {
                      phase: "Optimization",
                      description: "Continuously improve based on usage data",
                      tasks: [
                        "Analyze performance",
                        "Update prompts",
                        "Adjust routing",
                      ],
                    },
                  ].map((phase, index) => (
                    <div key={phase.phase} className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-semibold text-sm">
                          {index + 1}
                        </div>
                        {index < 3 && (
                          <div className="w-px h-12 bg-gray-300 mt-2" />
                        )}
                      </div>
                      <div className="flex-1 pb-8">
                        <h5 className="font-medium mb-1">{phase.phase}</h5>
                        <p className="text-sm text-gray-600 mb-2">
                          {phase.description}
                        </p>
                        <ul className="text-xs text-gray-500 space-y-1">
                          {phase.tasks.map((task) => (
                            <li key={task} className="flex items-center gap-1">
                              <div className="w-1 h-1 bg-gray-400 rounded-full" />
                              {task}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
      codeExample: `// Create a new AI agent
const agent = await client.agents.create({
  name: 'Customer Support Agent',
  type: 'chatbot',
  systemPrompt: \`You are a helpful customer support agent for SynapseAI. 
    Be polite, professional, and provide accurate information about our AI orchestration platform.\`,
  routing: {
    preferredProviders: ['openai', 'anthropic'],
    strategy: 'quality',
    fallbackEnabled: true
  },
  config: {
    temperature: 0.3,
    maxTokens: 1000,
    responseFormat: 'text'
  },
  knowledgeBase: {
    sources: ['faq.json', 'documentation.md'],
    updateFrequency: 'daily'
  }
});

// Deploy the agent
const deployment = await client.agents.deploy(agent.id, {
  environment: 'production',
  version: '1.0.0',
  rolloutStrategy: 'gradual'
});

// Monitor agent performance
const metrics = await client.agents.getMetrics(agent.id, {
  timeRange: '24h',
  includeConversations: true
});`,
    },
    {
      id: "troubleshooting",
      title: "Troubleshooting & Best Practices",
      description: "Common issues, solutions, and optimization tips",
      icon: <HelpCircle className="h-6 w-6" />,
      difficulty: "intermediate",
      estimatedTime: "25 minutes",
      prerequisites: ["first-request", "analytics-monitoring"],
      nextSteps: ["advanced-features"],
      content: (
        <div className="space-y-6">
          <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
            <h4 className="font-medium text-red-800 mb-2">🚨 Common Issues</h4>
            <p className="text-red-700 text-sm">
              Most issues can be resolved by checking API keys, rate limits, and
              request formatting. Use the diagnostic tools below to identify
              problems quickly.
            </p>
          </div>

          <Accordion type="single" collapsible className="w-full">
            {[
              {
                id: "auth-errors",
                title: "Authentication Errors",
                icon: <Lock className="h-4 w-4" />,
                content: (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Common Symptoms:</h5>
                      <ul className="text-sm space-y-1">
                        <li>• 401 Unauthorized responses</li>
                        <li>• "Invalid API key" errors</li>
                        <li>• "Authentication failed" messages</li>
                      </ul>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Solutions:</h5>
                      <ol className="text-sm space-y-2 list-decimal list-inside">
                        <li>
                          Verify your SynapseAI API key is correct and active
                        </li>
                        <li>
                          Check that provider API keys are valid and have
                          sufficient credits
                        </li>
                        <li>
                          Ensure API keys are properly set in environment
                          variables
                        </li>
                        <li>
                          Test individual provider connections in the dashboard
                        </li>
                      </ol>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Prevention:</h5>
                      <ul className="text-sm space-y-1">
                        <li>• Set up API key rotation schedules</li>
                        <li>• Monitor API key usage and expiration dates</li>
                        <li>
                          • Use separate keys for development and production
                        </li>
                        <li>• Enable API key usage alerts</li>
                      </ul>
                    </div>
                  </div>
                ),
              },
              {
                id: "rate-limits",
                title: "Rate Limiting Issues",
                icon: <Clock className="h-4 w-4" />,
                content: (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Common Symptoms:</h5>
                      <ul className="text-sm space-y-1">
                        <li>• 429 Too Many Requests errors</li>
                        <li>• Requests timing out or failing</li>
                        <li>• Inconsistent response times</li>
                      </ul>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Solutions:</h5>
                      <ol className="text-sm space-y-2 list-decimal list-inside">
                        <li>
                          Enable automatic fallback to alternative providers
                        </li>
                        <li>
                          Implement exponential backoff in your retry logic
                        </li>
                        <li>Distribute requests across multiple providers</li>
                        <li>
                          Use request queuing for high-volume applications
                        </li>
                      </ol>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Best Practices:</h5>
                      <ul className="text-sm space-y-1">
                        <li>• Monitor rate limit usage in real-time</li>
                        <li>• Set up alerts before hitting limits</li>
                        <li>• Use batch processing for bulk requests</li>
                        <li>
                          • Consider upgrading provider plans for higher limits
                        </li>
                      </ul>
                    </div>
                  </div>
                ),
              },
              {
                id: "performance",
                title: "Performance Optimization",
                icon: <TrendingUp className="h-4 w-4" />,
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h5 className="font-medium mb-2">Speed Optimization</h5>
                        <ul className="text-sm space-y-1">
                          <li>• Use streaming for real-time responses</li>
                          <li>• Enable request caching for repeated queries</li>
                          <li>
                            • Choose faster providers for time-sensitive tasks
                          </li>
                          <li>• Optimize prompt length and complexity</li>
                        </ul>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <h5 className="font-medium mb-2">Cost Optimization</h5>
                        <ul className="text-sm space-y-1">
                          <li>• Use cost-aware routing strategies</li>
                          <li>• Set budget limits and alerts</li>
                          <li>
                            • Choose appropriate models for task complexity
                          </li>
                          <li>• Implement request deduplication</li>
                        </ul>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Quality Optimization</h5>
                      <ul className="text-sm space-y-1">
                        <li>
                          • A/B test different providers for your use case
                        </li>
                        <li>• Fine-tune system prompts based on feedback</li>
                        <li>• Monitor response quality metrics</li>
                        <li>• Use provider-specific optimizations</li>
                      </ul>
                    </div>
                  </div>
                ),
              },
              {
                id: "debugging",
                title: "Debugging Tools",
                icon: <Code className="h-4 w-4" />,
                content: (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Built-in Debugging</h5>
                      <ul className="text-sm space-y-1">
                        <li>• Request/response logging in dashboard</li>
                        <li>• Routing decision explanations</li>
                        <li>• Provider performance metrics</li>
                        <li>• Error tracking and categorization</li>
                      </ul>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">SDK Debugging</h5>
                      <div className="bg-gray-900 text-gray-100 p-3 rounded text-sm font-mono">
                        <pre>{`// Enable debug mode
const client = new SynapseAI({
  apiKey: 'your-key',
  debug: true,
  logLevel: 'verbose'
});

// Get detailed request info
const response = await client.complete({
  messages: [{ role: 'user', content: 'test' }],
  includeDebugInfo: true
});

console.log('Routing decision:', response.debug.routing);
console.log('Provider used:', response.debug.provider);
console.log('Request timing:', response.debug.timing);`}</pre>
                      </div>
                    </div>
                  </div>
                ),
              },
            ].map((item) => (
              <AccordionItem key={item.id} value={item.id}>
                <AccordionTrigger className="flex items-center gap-2">
                  {item.icon}
                  {item.title}
                </AccordionTrigger>
                <AccordionContent>{item.content}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      ),
      codeExample: `// Comprehensive error handling
try {
  const response = await client.complete({
    messages: [{ role: 'user', content: 'Hello' }],
    timeout: 30000,
    retries: 3,
    fallbackProviders: ['openai', 'anthropic']
  });
  
  return response;
} catch (error) {
  if (error.code === 'RATE_LIMITED') {
    // Wait and retry with different provider
    await new Promise(resolve => setTimeout(resolve, 1000));
    return client.complete({
      messages: [{ role: 'user', content: 'Hello' }],
      excludeProviders: [error.provider]
    });
  }
  
  if (error.code === 'AUTHENTICATION_FAILED') {
    // Check API key validity
    await client.providers.validateKeys();
  }
  
  // Log error for debugging
  console.error('SynapseAI Error:', {
    code: error.code,
    message: error.message,
    provider: error.provider,
    requestId: error.requestId
  });
  
  throw error;
}`,
    },
  ];

  // FAQ data
  const faqs: FAQ[] = [
    {
      id: "pricing-model",
      question: "How does SynapseAI pricing work?",
      answer:
        "SynapseAI uses a pay-per-request model with no markup on provider costs. You pay the actual cost of the AI provider plus a small routing fee (typically $0.001 per request). This ensures you get the best value while benefiting from intelligent routing.",
      category: "pricing",
      helpful: 45,
      tags: ["pricing", "billing", "costs"],
    },
    {
      id: "provider-support",
      question: "Which AI providers are supported?",
      answer:
        "SynapseAI supports all major AI providers including OpenAI (GPT-4, GPT-3.5), Anthropic (Claude), Google (Gemini), Groq, Mistral, and more. We're constantly adding new providers based on user demand.",
      category: "providers",
      helpful: 38,
      tags: ["providers", "integrations", "models"],
    },
    {
      id: "routing-logic",
      question: "How does the intelligent routing work?",
      answer:
        "Our routing system considers multiple factors: cost, latency, success rates, provider capabilities, current load, and your preferences. It uses machine learning to continuously optimize decisions based on historical performance data.",
      category: "technical",
      helpful: 52,
      tags: ["routing", "ai", "optimization"],
    },
    {
      id: "data-privacy",
      question: "Is my data secure and private?",
      answer:
        "Yes, we take security seriously. All data is encrypted in transit and at rest. We don't store your request content permanently and follow SOC 2 compliance standards. You can also use your own provider keys for additional control.",
      category: "security",
      helpful: 41,
      tags: ["security", "privacy", "compliance"],
    },
    {
      id: "rate-limits",
      question: "What happens when I hit rate limits?",
      answer:
        "SynapseAI automatically handles rate limits by routing requests to alternative providers. You can configure fallback strategies and we'll ensure your requests are processed even if one provider is temporarily unavailable.",
      category: "technical",
      helpful: 29,
      tags: ["rate-limits", "fallback", "reliability"],
    },
    {
      id: "sdk-languages",
      question: "What programming languages are supported?",
      answer:
        "We provide official SDKs for TypeScript/JavaScript, Python, and Go. You can also use our REST API with any programming language. Community SDKs are available for PHP, Ruby, and other languages.",
      category: "development",
      helpful: 33,
      tags: ["sdk", "languages", "api"],
    },
  ];

  // Missing features data
  const missingFeatures: MissingFeature[] = [
    {
      id: "real-time-streaming",
      title: "Enhanced Real-time Streaming",
      description:
        "Server-sent events for real-time response streaming with better error handling and reconnection logic",
      priority: "high",
      category: "core",
      votes: 127,
      status: "in-progress",
      estimatedRelease: "Q2 2024",
    },
    {
      id: "custom-models",
      title: "Custom Model Integration",
      description:
        "Support for fine-tuned and custom models from various providers, including model versioning and A/B testing",
      priority: "high",
      category: "providers",
      votes: 89,
      status: "planned",
      estimatedRelease: "Q3 2024",
    },
    {
      id: "advanced-caching",
      title: "Advanced Response Caching",
      description:
        "Intelligent caching system with semantic similarity matching to reduce costs and improve response times",
      priority: "medium",
      category: "performance",
      votes: 76,
      status: "planned",
      estimatedRelease: "Q2 2024",
    },
    {
      id: "workflow-automation",
      title: "Workflow Automation",
      description:
        "Visual workflow builder for complex AI pipelines with conditional logic, loops, and multi-step processing",
      priority: "high",
      category: "automation",
      votes: 134,
      status: "requested",
    },
    {
      id: "multi-modal-support",
      title: "Enhanced Multi-modal Support",
      description:
        "Better support for image, audio, and video processing with automatic format conversion and optimization",
      priority: "medium",
      category: "features",
      votes: 92,
      status: "planned",
      estimatedRelease: "Q4 2024",
    },
    {
      id: "team-collaboration",
      title: "Team Collaboration Features",
      description:
        "Shared workspaces, role-based access control, team analytics, and collaborative prompt engineering tools",
      priority: "medium",
      category: "collaboration",
      votes: 68,
      status: "requested",
    },
    {
      id: "on-premise-deployment",
      title: "On-Premise Deployment",
      description:
        "Self-hosted version of SynapseAI for enterprise customers with air-gapped environments",
      priority: "low",
      category: "enterprise",
      votes: 45,
      status: "requested",
    },
    {
      id: "mobile-sdk",
      title: "Mobile SDKs",
      description:
        "Native SDKs for iOS and Android with offline capabilities and optimized for mobile use cases",
      priority: "medium",
      category: "mobile",
      votes: 58,
      status: "requested",
    },
    {
      id: "graphql-api",
      title: "GraphQL API",
      description:
        "GraphQL endpoint for more flexible data fetching and better integration with modern frontend frameworks",
      priority: "low",
      category: "api",
      votes: 34,
      status: "requested",
    },
    {
      id: "cost-budgeting",
      title: "Advanced Cost Budgeting",
      description:
        "Detailed budget controls with department-level allocation, spending forecasts, and automatic cost optimization",
      priority: "high",
      category: "billing",
      votes: 103,
      status: "planned",
      estimatedRelease: "Q3 2024",
    },
  ];

  // Filter functions
  const filteredSections = guideSections.filter((section) => {
    const matchesSearch =
      section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      section.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDifficulty =
      selectedDifficulty === "all" || section.difficulty === selectedDifficulty;
    return matchesSearch && matchesDifficulty;
  });

  const filteredFAQs = faqs.filter((faq) => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Helper functions
  const toggleSectionCompletion = (sectionId: string) => {
    const newCompleted = new Set(completedSections);
    if (newCompleted.has(sectionId)) {
      newCompleted.delete(sectionId);
    } else {
      newCompleted.add(sectionId);
    }
    setCompletedSections(newCompleted);

    // Update progress
    const progress = (newCompleted.size / guideSections.length) * 100;
    setUserProgress(progress);
  };

  const toggleBookmark = (sectionId: string) => {
    const newBookmarked = new Set(bookmarkedSections);
    if (newBookmarked.has(sectionId)) {
      newBookmarked.delete(sectionId);
    } else {
      newBookmarked.add(sectionId);
    }
    setBookmarkedSections(newBookmarked);
  };

  const copyCodeExample = (code: string) => {
    navigator.clipboard.writeText(code);
  };

  const rateFAQ = (faqId: string, rating: number) => {
    setFeedbackRatings((prev) => ({ ...prev, [faqId]: rating }));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "advanced":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "planned":
        return "bg-purple-100 text-purple-800";
      case "requested":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-background to-purple-50 dark:from-slate-900 dark:via-background dark:to-slate-800 text-foreground transition-colors duration-200">
      {/* Header */}
      <motion.div
        className="bg-background/80 backdrop-blur-sm border-b sticky top-0 z-50"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <motion.div
                className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <BookOpen className="h-8 w-8 text-white" />
              </motion.div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  SynapseAI User Guide
                </h1>
                <p className="text-gray-600">
                  Complete guide to mastering AI orchestration
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-600">Your Progress</div>
                <div className="flex items-center gap-2">
                  <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                      initial={{ width: 0 }}
                      animate={{ width: `${userProgress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  <span className="text-sm font-medium">
                    {Math.round(userProgress)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search guides, FAQs, and features..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-2">
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="px-3 py-2 border rounded-md bg-white"
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md bg-white"
              >
                <option value="all">All Categories</option>
                <option value="pricing">Pricing</option>
                <option value="providers">Providers</option>
                <option value="technical">Technical</option>
                <option value="security">Security</option>
                <option value="development">Development</option>
              </select>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="guides" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="guides" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Guides
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              FAQ
            </TabsTrigger>
            <TabsTrigger value="features" className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Missing Features
            </TabsTrigger>
            <TabsTrigger value="support" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Support
            </TabsTrigger>
          </TabsList>

          {/* Guides Tab */}
          <TabsContent value="guides">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-6"
            >
              {filteredSections.map((section) => (
                <motion.div key={section.id} variants={itemVariants}>
                  <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <motion.div
                            className="p-2 bg-white rounded-lg shadow-sm"
                            whileHover={{ scale: 1.1 }}
                          >
                            {section.icon}
                          </motion.div>
                          <div>
                            <CardTitle className="text-xl">
                              {section.title}
                            </CardTitle>
                            <p className="text-gray-600 mt-1">
                              {section.description}
                            </p>
                            <div className="flex items-center gap-3 mt-2">
                              <Badge
                                className={getDifficultyColor(
                                  section.difficulty
                                )}
                              >
                                {section.difficulty}
                              </Badge>
                              <div className="flex items-center gap-1 text-sm text-gray-500">
                                <Clock className="h-3 w-3" />
                                {section.estimatedTime}
                              </div>
                              {section.videoUrl && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setSelectedVideo(section.videoUrl!);
                                    setShowVideoModal(true);
                                  }}
                                >
                                  <Video className="h-3 w-3 mr-1" />
                                  Watch Video
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleBookmark(section.id)}
                            className={
                              bookmarkedSections.has(section.id)
                                ? "text-yellow-600"
                                : ""
                            }
                          >
                            <Bookmark
                              className={`h-4 w-4 ${bookmarkedSections.has(section.id) ? "fill-current" : ""}`}
                            />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleSectionCompletion(section.id)}
                            className={
                              completedSections.has(section.id)
                                ? "text-green-600"
                                : ""
                            }
                          >
                            <CheckCircle
                              className={`h-4 w-4 ${completedSections.has(section.id) ? "fill-current" : ""}`}
                            />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              setActiveSection(
                                activeSection === section.id ? null : section.id
                              )
                            }
                          >
                            {activeSection === section.id ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <AnimatePresence>
                      {activeSection === section.id && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <CardContent className="pt-6">
                            {/* Prerequisites */}
                            {section.prerequisites &&
                              section.prerequisites.length > 0 && (
                                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                  <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
                                    <Info className="h-4 w-4" />
                                    Prerequisites
                                  </h4>
                                  <div className="flex flex-wrap gap-2">
                                    {section.prerequisites.map((prereq) => {
                                      const prereqSection = guideSections.find(
                                        (s) => s.id === prereq
                                      );
                                      return (
                                        <Badge
                                          key={prereq}
                                          variant="outline"
                                          className="text-blue-700"
                                        >
                                          {prereqSection?.title || prereq}
                                        </Badge>
                                      );
                                    })}
                                  </div>
                                </div>
                              )}

                            {/* Main Content */}
                            <div className="mb-6">{section.content}</div>

                            {/* Code Example */}
                            {section.codeExample && (
                              <div className="mb-6">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium flex items-center gap-2">
                                    <Code className="h-4 w-4" />
                                    Code Example
                                  </h4>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      copyCodeExample(section.codeExample!)
                                    }
                                  >
                                    <Copy className="h-3 w-3 mr-1" />
                                    Copy
                                  </Button>
                                </div>
                                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                                  <pre className="text-sm font-mono">
                                    <code>{section.codeExample}</code>
                                  </pre>
                                </div>
                              </div>
                            )}

                            {/* Next Steps */}
                            {section.nextSteps &&
                              section.nextSteps.length > 0 && (
                                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                                  <h4 className="font-medium text-green-800 mb-2 flex items-center gap-2">
                                    <ArrowRight className="h-4 w-4" />
                                    Next Steps
                                  </h4>
                                  <div className="space-y-2">
                                    {section.nextSteps.map((nextStep) => {
                                      const nextSection = guideSections.find(
                                        (s) => s.id === nextStep
                                      );
                                      return (
                                        <Button
                                          key={nextStep}
                                          variant="ghost"
                                          size="sm"
                                          className="justify-start h-auto p-2 text-green-700 hover:bg-green-100"
                                          onClick={() =>
                                            setActiveSection(nextStep)
                                          }
                                        >
                                          <ArrowRight className="h-3 w-3 mr-2" />
                                          {nextSection?.title || nextStep}
                                        </Button>
                                      );
                                    })}
                                  </div>
                                </div>
                              )}
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          {/* FAQ Tab */}
          <TabsContent value="faq">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-4"
            >
              {filteredFAQs.map((faq) => (
                <motion.div key={faq.id} variants={itemVariants}>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg">
                            {faq.question}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline">{faq.category}</Badge>
                            {faq.tags.map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <ThumbsUp className="h-4 w-4" />
                          {faq.helpful}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 mb-4">{faq.answer}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">
                            Was this helpful?
                          </span>
                          <div className="flex gap-1">
                            {[1, 2, 3, 4, 5].map((rating) => (
                              <Button
                                key={rating}
                                variant="ghost"
                                size="sm"
                                onClick={() => rateFAQ(faq.id, rating)}
                                className={`p-1 h-auto ${
                                  feedbackRatings[faq.id] >= rating
                                    ? "text-yellow-500"
                                    : "text-gray-300"
                                }`}
                              >
                                <Star
                                  className={`h-3 w-3 ${
                                    feedbackRatings[faq.id] >= rating
                                      ? "fill-current"
                                      : ""
                                  }`}
                                />
                              </Button>
                            ))}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm">
                            <Share2 className="h-3 w-3 mr-1" />
                            Share
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Flag className="h-3 w-3 mr-1" />
                            Report
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </TabsContent>

          {/* Missing Features Tab */}
          <TabsContent value="features">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-6"
            >
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border">
                <h2 className="text-2xl font-bold mb-2">
                  🚀 Roadmap & Feature Requests
                </h2>
                <p className="text-gray-700 mb-4">
                  Help us prioritize development by voting on features you need
                  most. Our team actively reviews and implements the most
                  requested features.
                </p>
                <div className="flex gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Completed</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>In Progress</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span>Planned</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span>Requested</span>
                  </div>
                </div>
              </div>

              <div className="grid gap-4">
                {missingFeatures
                  .sort((a, b) => b.votes - a.votes)
                  .map((feature) => (
                    <motion.div key={feature.id} variants={itemVariants}>
                      <Card className="hover:shadow-md transition-shadow">
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <CardTitle className="text-lg">
                                  {feature.title}
                                </CardTitle>
                                <Badge
                                  className={getPriorityColor(feature.priority)}
                                >
                                  {feature.priority}
                                </Badge>
                                <Badge
                                  className={getStatusColor(feature.status)}
                                >
                                  {feature.status}
                                </Badge>
                              </div>
                              <p className="text-gray-600 mb-3">
                                {feature.description}
                              </p>
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Users className="h-4 w-4" />
                                  {feature.votes} votes
                                </div>
                                <Badge variant="outline">
                                  {feature.category}
                                </Badge>
                                {feature.estimatedRelease && (
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-4 w-4" />
                                    {feature.estimatedRelease}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex flex-col items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <ThumbsUp className="h-3 w-3" />
                                Vote
                              </Button>
                              <span className="text-xs text-gray-500">
                                {feature.votes}
                              </span>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    </motion.div>
                  ))}
              </div>

              <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-dashed border-2 border-blue-300">
                <CardContent className="p-6 text-center">
                  <Lightbulb className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">
                    Have a Feature Idea?
                  </h3>
                  <p className="text-gray-600 mb-4">
                    We'd love to hear your suggestions! Submit your feature
                    requests and help shape the future of SynapseAI.
                  </p>
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Submit Feature Request
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Support Tab */}
          <TabsContent value="support">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    title: "Documentation",
                    description: "Comprehensive API docs and guides",
                    icon: <FileText className="h-8 w-8 text-blue-500" />,
                    link: "https://docs.synapseai.com",
                    action: "Browse Docs",
                  },
                  {
                    title: "Community Forum",
                    description: "Connect with other developers",
                    icon: <Users className="h-8 w-8 text-green-500" />,
                    link: "https://community.synapseai.com",
                    action: "Join Community",
                  },
                  {
                    title: "Discord Chat",
                    description: "Real-time help and discussions",
                    icon: <MessageCircle className="h-8 w-8 text-purple-500" />,
                    link: "https://discord.gg/synapseai",
                    action: "Join Discord",
                  },
                  {
                    title: "Email Support",
                    description: "Direct support from our team",
                    icon: <Mail className="h-8 w-8 text-red-500" />,
                    link: "mailto:<EMAIL>",
                    action: "Send Email",
                  },
                  {
                    title: "Video Tutorials",
                    description: "Step-by-step video guides",
                    icon: <Video className="h-8 w-8 text-orange-500" />,
                    link: "https://youtube.com/synapseai",
                    action: "Watch Videos",
                  },
                  {
                    title: "Status Page",
                    description: "System status and uptime",
                    icon: <Activity className="h-8 w-8 text-teal-500" />,
                    link: "https://status.synapseai.com",
                    action: "Check Status",
                  },
                ].map((item) => (
                  <motion.div key={item.title} variants={itemVariants}>
                    <Card className="h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                      <CardContent className="p-6 text-center">
                        <motion.div
                          className="mb-4"
                          whileHover={{ scale: 1.1 }}
                          transition={{ duration: 0.2 }}
                        >
                          {item.icon}
                        </motion.div>
                        <h3 className="text-lg font-semibold mb-2">
                          {item.title}
                        </h3>
                        <p className="text-gray-600 mb-4">{item.description}</p>
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => window.open(item.link, "_blank")}
                        >
                          {item.action}
                          <ExternalLink className="h-3 w-3 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              <Card className="bg-gradient-to-r from-green-50 to-blue-50">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-green-100 p-3 rounded-full">
                      <HelpCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-2">
                        Need Immediate Help?
                      </h3>
                      <p className="text-gray-600 mb-4">
                        For urgent issues or enterprise support, contact our
                        team directly. We typically respond within 2 hours
                        during business hours.
                      </p>
                      <div className="flex gap-3">
                        <Button>
                          <Phone className="h-4 w-4 mr-2" />
                          Schedule Call
                        </Button>
                        <Button variant="outline">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Live Chat
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Video Modal */}
      <Dialog open={showVideoModal} onOpenChange={setShowVideoModal}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Tutorial Video</DialogTitle>
            <DialogDescription>
              Watch this step-by-step tutorial to learn more.
            </DialogDescription>
          </DialogHeader>
          <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Video className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">
                Video player would be embedded here
              </p>
              <p className="text-sm text-gray-500 mt-2">{selectedVideo}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserGuide;
