// Logging System for SynapseAI

import { getConfig } from "./config";

export type LogLevel = "debug" | "info" | "warn" | "error";

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  source?: string;
  userId?: string;
  sessionId?: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enabled: boolean;
  console: boolean;
  remote: boolean;
  storage: boolean;
  maxEntries: number;
}

class Logger {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];
  private sessionId: string;
  private remoteEndpoint?: string;

  constructor() {
    const appConfig = getConfig();

    this.config = {
      level: appConfig.getLogLevel(),
      enabled: appConfig.isLoggingEnabled(),
      console: true,
      remote: appConfig.isProduction(),
      storage: !appConfig.isProduction(),
      maxEntries: 1000,
    };

    this.sessionId = this.generateSessionId();
    this.remoteEndpoint = appConfig.get("monitoring").sentryDsn;

    // Initialize remote logging if configured
    if (this.config.remote && this.remoteEndpoint) {
      this.initializeRemoteLogging();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private async initializeRemoteLogging(): Promise<void> {
    // Initialize remote logging if configured
    if (typeof window !== "undefined" && this.remoteEndpoint) {
      try {
        const Sentry = await import("@sentry/react");
        Sentry.init({ dsn: this.remoteEndpoint });
      } catch (error) {
        // Sentry not available, continue without remote logging
        console.warn("Sentry not available for remote logging");
      }
    }
  }

  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return false;

    const levels: LogLevel[] = ["debug", "info", "warn", "error"];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex >= currentLevelIndex;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    data?: any,
    source?: string
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      source,
      sessionId: this.sessionId,
    };
  }

  private writeLog(entry: LogEntry): void {
    // Store in memory
    if (this.config.storage) {
      this.logs.push(entry);

      // Maintain max entries limit
      if (this.logs.length > this.config.maxEntries) {
        this.logs = this.logs.slice(-this.config.maxEntries);
      }
    }

    // Console output
    if (this.config.console) {
      const logMethod = {
        debug: console.debug,
        info: console.info,
        warn: console.warn,
        error: console.error,
      }[entry.level];

      const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`;
      const source = entry.source ? ` [${entry.source}]` : "";

      if (entry.data) {
        logMethod(`${prefix}${source} ${entry.message}`, entry.data);
      } else {
        logMethod(`${prefix}${source} ${entry.message}`);
      }
    }

    // Remote logging
    if (this.config.remote) {
      this.sendToRemote(entry);
    }
  }

  private async sendToRemote(entry: LogEntry): Promise<void> {
    try {
      // Send to remote logging service
      if (entry.level === "error") {
        // Send errors to Sentry if available
        try {
          const Sentry = await import("@sentry/react");
          Sentry.captureException(new Error(entry.message), {
            extra: entry.data,
            tags: { source: entry.source },
          });
        } catch (sentryError) {
          // Sentry not available, continue without error reporting
        }
      }

      // Send all logs to custom endpoint if configured
      const config = getConfig();
      if (config.get("monitoring").analyticsTrackingId) {
        await fetch(`${config.getApiBaseUrl()}/logs`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(entry),
        }).catch(() => {
          // Silently fail remote logging to avoid infinite loops
        });
      }
    } catch (error) {
      // Silently fail remote logging
    }
  }

  debug(message: string, data?: any, source?: string): void {
    if (this.shouldLog("debug")) {
      const entry = this.createLogEntry("debug", message, data, source);
      this.writeLog(entry);
    }
  }

  info(message: string, data?: any, source?: string): void {
    if (this.shouldLog("info")) {
      const entry = this.createLogEntry("info", message, data, source);
      this.writeLog(entry);
    }
  }

  warn(message: string, data?: any, source?: string): void {
    if (this.shouldLog("warn")) {
      const entry = this.createLogEntry("warn", message, data, source);
      this.writeLog(entry);
    }
  }

  error(message: string, data?: any, source?: string): void {
    if (this.shouldLog("error")) {
      const entry = this.createLogEntry("error", message, data, source);
      this.writeLog(entry);
    }
  }

  // Performance logging
  time(label: string): void {
    if (this.shouldLog("debug")) {
      console.time(label);
    }
  }

  timeEnd(label: string): void {
    if (this.shouldLog("debug")) {
      console.timeEnd(label);
    }
  }

  // Get stored logs
  getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = this.logs.filter((log) => log.level === level);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return [...filteredLogs];
  }

  // Clear stored logs
  clearLogs(): void {
    this.logs = [];
  }

  // Export logs
  exportLogs(): string {
    return JSON.stringify(
      {
        sessionId: this.sessionId,
        exportTime: new Date().toISOString(),
        logs: this.logs,
      },
      null,
      2
    );
  }

  // Get logger statistics
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<LogLevel, number>;
    sessionId: string;
    oldestLog?: string;
    newestLog?: string;
  } {
    const logsByLevel: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
    };

    this.logs.forEach((log) => {
      logsByLevel[log.level]++;
    });

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      sessionId: this.sessionId,
      oldestLog: this.logs[0]?.timestamp,
      newestLog: this.logs[this.logs.length - 1]?.timestamp,
    };
  }

  // Update configuration
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // Create child logger with source
  createChild(source: string): ChildLogger {
    return new ChildLogger(this, source);
  }
}

// Child logger for specific components/modules
class ChildLogger {
  constructor(
    private parent: Logger,
    private source: string
  ) { }

  debug(message: string, data?: any): void {
    this.parent.debug(message, data, this.source);
  }

  info(message: string, data?: any): void {
    this.parent.info(message, data, this.source);
  }

  warn(message: string, data?: any): void {
    this.parent.warn(message, data, this.source);
  }

  error(message: string, data?: any): void {
    this.parent.error(message, data, this.source);
  }

  time(label: string): void {
    this.parent.time(`${this.source}:${label}`);
  }

  timeEnd(label: string): void {
    this.parent.timeEnd(`${this.source}:${label}`);
  }
}

// Global logger instance
let logger: Logger | null = null;

export function getLogger(): Logger {
  if (!logger) {
    logger = new Logger();
  }
  return logger;
}

export { Logger, ChildLogger };
