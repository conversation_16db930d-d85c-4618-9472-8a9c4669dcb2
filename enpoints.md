// Backend API Endpoints

POST   /api/v1/auth/login
POST   /api/v1/auth/logout
POST   /api/v1/auth/refresh
GET    /api/v1/auth/profile
PUT    /api/v1/auth/profile
POST   /api/v1/auth/register
GET    /api/v1/auth/sessions
DELETE /api/v1/auth/sessions/:id

GET    /api/v1/providers
POST   /api/v1/providers
GET    /api/v1/providers/:id
PUT    /api/v1/providers/:id
DELETE /api/v1/providers/:id
POST   /api/v1/providers/:id/test
GET    /api/v1/providers/:id/metrics
GET    /api/v1/providers/:id/models
PUT    /api/v1/providers/:id/status
GET    /api/v1/providers/:id/health
POST   /api/v1/providers/:id/chat

GET    /api/v1/agents
POST   /api/v1/agents
GET    /api/v1/agents/:id
PUT    /api/v1/agents/:id
DELETE /api/v1/agents/:id
POST   /api/v1/agents/:id/deploy
POST   /api/v1/agents/:id/stop
POST   /api/v1/agents/:id/restart
GET    /api/v1/agents/:id/logs
GET    /api/v1/agents/:id/metrics
POST   /api/v1/agents/:id/test

POST   /api/v1/requests
GET    /api/v1/requests
GET    /api/v1/requests/:id
POST   /api/v1/requests/:id/retry
DELETE /api/v1/requests/:id
GET    /api/v1/requests/stream

GET    /api/v1/analytics/overview
GET    /api/v1/analytics/realtime
GET    /api/v1/analytics/usage
GET    /api/v1/analytics/costs
GET    /api/v1/analytics/performance
POST   /api/v1/analytics/events
GET    /api/v1/analytics/events

GET    /api/v1/events
POST   /api/v1/events
GET    /api/v1/events/:id
GET    /api/v1/events/stream
POST   /api/v1/events/subscribe
POST   /api/v1/events/unsubscribe

POST   /api/v1/routing/request
GET    /api/v1/routing/rules
POST   /api/v1/routing/rules
PUT    /api/v1/routing/rules/:id
DELETE /api/v1/routing/rules/:id
GET    /api/v1/routing/strategies
GET    /api/v1/routing/performance

GET    /api/v1/users
POST   /api/v1/users
GET    /api/v1/users/:id
PUT    /api/v1/users/:id
DELETE /api/v1/users/:id

GET    /api/v1/tenants
POST   /api/v1/tenants
GET    /api/v1/tenants/:id
PUT    /api/v1/tenants/:id
DELETE /api/v1/tenants/:id

POST   /api/v1/sessions
GET    /api/v1/sessions
GET    /api/v1/sessions/:id
PUT    /api/v1/sessions/:id
DELETE /api/v1/sessions/:id

GET    /api/v1/widgets
POST   /api/v1/widgets
GET    /api/v1/widgets/:id
PUT    /api/v1/widgets/:id
DELETE /api/v1/widgets/:id
GET    /api/v1/widgets/:id/embed

GET    /api/v1/notifications
POST   /api/v1/notifications
GET    /api/v1/notifications/:id
PUT    /api/v1/notifications/:id/read
DELETE /api/v1/notifications/:id

GET    /api/v1/health
GET    /api/v1/health/detailed
GET    /api/v1/system/status
GET    /api/v1/system/metrics

GET    /api/v1/hitl/requests
POST   /api/v1/hitl/requests
GET    /api/v1/hitl/requests/:id
PUT    /api/v1/hitl/requests/:id/response
POST   /api/v1/hitl/requests/:id/escalate

GET    /api/v1/hitl/requests
POST   /api/v1/hitl/requests
GET    /api/v1/hitl/requests/:id
PUT    /api/v1/hitl/requests/:id/response

WS     /api/v1/ws/events
WS     /api/v1/ws/requests
WS     /api/v1/ws/agents
WS     /api/v1/ws/providers
WS     /api/v1/ws/analytics
