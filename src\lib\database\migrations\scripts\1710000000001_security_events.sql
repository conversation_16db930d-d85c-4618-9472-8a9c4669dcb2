-- Create enum types
CREATE TYPE security_event_type AS ENUM (
  'LOGIN_ATTEMPT',
  'API_ACCESS',
  'UNAUTHORIZED_ACCESS',
  'CONFIG_CHANGE',
  'DATA_ACCESS',
  'SYSTEM_ERROR'
);

CREATE TYPE security_event_severity AS ENUM (
  'INFO',
  'WARNING',
  'ERROR',
  'CRITICAL'
);

-- Create security events table
CREATE TABLE security_events (
  id UUID PRIMARY KEY,
  type security_event_type NOT NULL,
  severity security_event_severity NOT NULL,
  user_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  details JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on user_id for faster lookups
CREATE INDEX idx_security_events_user_id ON security_events(user_id);

-- <PERSON>reate index on created_at for time-based queries
CREATE INDEX idx_security_events_created_at ON security_events(created_at);

-- Create index on type and severity for filtering
CREATE INDEX idx_security_events_type_severity ON security_events(type, severity);

-- Create security alerts table
CREATE TABLE security_alerts (
  id UUID PRIMARY KEY,
  event_id UUID NOT NULL REFERENCES security_events(id),
  severity security_event_severity NOT NULL,
  user_id TEXT,
  details JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  resolved BOOLEAN NOT NULL DEFAULT FALSE,
  resolution_details JSONB
);

-- Create index on resolved status for active alerts
CREATE INDEX idx_security_alerts_resolved ON security_alerts(resolved);

-- Create index on user_id for faster lookups
CREATE INDEX idx_security_alerts_user_id ON security_alerts(user_id);

-- Create index on created_at for time-based queries
CREATE INDEX idx_security_alerts_created_at ON security_alerts(created_at);

-- Create view for recent security events
CREATE VIEW recent_security_events AS
SELECT *
FROM security_events
WHERE timestamp >= NOW() - INTERVAL '24 hours'
ORDER BY timestamp DESC; 