{"extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2020": true, "node": true}, "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "react-hooks/exhaustive-deps": "warn"}, "ignorePatterns": ["dist", "node_modules", "*.config.js"]}