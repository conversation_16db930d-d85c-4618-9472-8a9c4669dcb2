import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { tempo } from "tempo-devtools/dist/vite";

// https://vitejs.dev/config/
export default defineConfig({
  base:
    process.env.NODE_ENV === "development"
      ? "/"
      : process.env.VITE_BASE_PATH || "/",
  optimizeDeps: {
    entries: ["src/main.tsx", "src/tempobook/**/*"],
    exclude: ["pg", "pg-native"], // Exclude server-side packages
  },
  plugins: [
    react(),
    tempo(),
    {
      name: "security-headers",
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          // Security headers
          res.setHeader("X-Content-Type-Options", "nosniff");
          res.setHeader("X-Frame-Options", "DENY");
          res.setHeader("X-XSS-Protection", "1; mode=block");
          res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
          res.setHeader(
            "Permissions-Policy",
            "camera=(), microphone=(), geolocation=()"
          );

          // Content Security Policy
          const csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://api.tempo.new",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "connect-src 'self' wss: https:",
            "font-src 'self' https:",
            "object-src 'none'",
            "media-src 'self'",
            "frame-src 'none'",
          ].join("; ");
          res.setHeader("Content-Security-Policy", csp);

          // HSTS (only in production)
          if (process.env.NODE_ENV === "production") {
            res.setHeader(
              "Strict-Transport-Security",
              "max-age=31536000; includeSubDomains; preload"
            );
          }

          next();
        });
      },
    },
  ],
  resolve: {
    preserveSymlinks: true,
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    // @ts-ignore
    allowedHosts: process.env.NODE_ENV === "development" ? true : false,
    https: process.env.NODE_ENV === "production" ? true : false,
    cors: {
      origin: process.env.VITE_CORS_ORIGINS?.split(",") || [
        "http://localhost:3000",
      ],
      credentials: true,
    },
  },
  define: {
    global: "globalThis", // Fix for some Node.js globals
  },
  build: {
    sourcemap: process.env.NODE_ENV !== "production",
    minify: process.env.NODE_ENV === "production" ? "terser" : false,
    terserOptions:
      process.env.NODE_ENV === "production"
        ? {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
          }
        : undefined,
  },
});
