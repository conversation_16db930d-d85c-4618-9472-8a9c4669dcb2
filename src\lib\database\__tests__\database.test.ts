import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { db } from '..';
import { DatabaseError, QueryError, ConnectionError } from '../errors';

describe('Database Module', () => {
    beforeAll(async () => {
        await db.connect();
    });

    afterAll(async () => {
        await db.disconnect();
    });

    describe('Connection Management', () => {
        it('should connect to the database successfully', async () => {
            const healthCheck = await db.healthCheck();
            expect(healthCheck.status).toBe('healthy');
            expect(healthCheck.latency).toBeDefined();
            expect(healthCheck.latency).toBeGreaterThan(0);
        });

        it('should handle connection errors gracefully', async () => {
            // Force a connection error by trying to connect to an invalid port
            process.env.DATABASE_URL = 'postgresql://test:test@localhost:5433/nonexistent';

            const newDb = db;
            await expect(newDb.connect()).rejects.toThrow(ConnectionError);

            // Reset the DATABASE_URL
            process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/synapseai_test';
        });
    });

    describe('Query Execution', () => {
        it('should execute simple queries successfully', async () => {
            const result = await db.query('SELECT 1 as number');
            expect(result.rows[0].number).toBe(1);
        });

        it('should handle parameterized queries', async () => {
            const result = await db.query(
                'SELECT $1::text as message',
                ['Hello World']
            );
            expect(result.rows[0].message).toBe('Hello World');
        });

        it('should handle query timeouts', async () => {
            await expect(
                db.query('SELECT pg_sleep(2)', [], { timeout: 1000 })
            ).rejects.toThrow(QueryError);
        });

        it('should handle invalid queries', async () => {
            await expect(
                db.query('SELECT * FROM nonexistent_table')
            ).rejects.toThrow(DatabaseError);
        });
    });

    describe('Transactions', () => {
        it('should handle successful transactions', async () => {
            const result = await db.transaction(async (client) => {
                await client.query('CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, value TEXT)');
                await client.query('INSERT INTO test_table (value) VALUES ($1)', ['test']);
                const result = await client.query('SELECT * FROM test_table');
                await client.query('DROP TABLE test_table');
                return result;
            });

            expect(result.rows[0].value).toBe('test');
        });

        it('should rollback failed transactions', async () => {
            await db.transaction(async (client) => {
                await client.query('CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, value TEXT)');
            });

            try {
                await db.transaction(async (client) => {
                    await client.query('INSERT INTO test_table (value) VALUES ($1)', ['test1']);
                    await client.query('INSERT INTO test_table (invalid) VALUES ($1)', ['test2']);
                });
            } catch (error) {
                // Expected to fail
            }

            // Verify the first insert was rolled back
            const result = await db.query('SELECT COUNT(*) as count FROM test_table');
            expect(result.rows[0].count).toBe('0');

            // Clean up
            await db.query('DROP TABLE test_table');
        });
    });

    describe('Monitoring', () => {
        it('should track query metrics', async () => {
            // Execute some queries
            await db.query('SELECT 1');
            await db.query('SELECT 2');

            const metrics = db.getMetrics();
            expect(metrics.totalQueries).toBeGreaterThan(0);
            expect(metrics.averageDuration).toBeGreaterThan(0);
            expect(metrics.successRate).toBe(100);
        });

        it('should track failed queries', async () => {
            try {
                await db.query('SELECT * FROM nonexistent_table');
            } catch (error) {
                // Expected to fail
            }

            const metrics = db.getMetrics();
            expect(metrics.errorRate).toBeGreaterThan(0);
            expect(metrics.recentErrors.length).toBeGreaterThan(0);
        });
    });
});
