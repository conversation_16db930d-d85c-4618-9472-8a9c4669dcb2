// Input Validation and Sanitization Library

import { securityManager } from "./security";

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  type?: "string" | "number" | "email" | "url" | "uuid";
  custom?: (value: any) => boolean | string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData: Record<string, any>;
}

class Validator {
  private emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private urlRegex =
    /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
  private uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  validate(
    data: Record<string, any>,
    schema: ValidationSchema
  ): ValidationResult {
    const errors: Record<string, string[]> = {};
    const sanitizedData: Record<string, any> = {};

    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field];
      const fieldErrors: string[] = [];

      // Required validation
      if (
        rule.required &&
        (value === undefined || value === null || value === "")
      ) {
        fieldErrors.push(`${field} is required`);
        continue;
      }

      // Skip further validation if field is not required and empty
      if (
        !rule.required &&
        (value === undefined || value === null || value === "")
      ) {
        sanitizedData[field] = value;
        continue;
      }

      // Type validation and sanitization
      let sanitizedValue = value;
      switch (rule.type) {
        case "string":
          sanitizedValue = this.sanitizeString(String(value));
          break;
        case "number":
          sanitizedValue = Number(value);
          if (isNaN(sanitizedValue)) {
            fieldErrors.push(`${field} must be a valid number`);
          }
          break;
        case "email":
          sanitizedValue = this.sanitizeString(String(value).toLowerCase());
          if (!this.emailRegex.test(sanitizedValue)) {
            fieldErrors.push(`${field} must be a valid email address`);
          }
          break;
        case "url":
          sanitizedValue = this.sanitizeString(String(value));
          if (!this.urlRegex.test(sanitizedValue)) {
            fieldErrors.push(`${field} must be a valid URL`);
          }
          break;
        case "uuid":
          sanitizedValue = this.sanitizeString(String(value));
          if (!this.uuidRegex.test(sanitizedValue)) {
            fieldErrors.push(`${field} must be a valid UUID`);
          }
          break;
      }

      // Length validation
      if (rule.minLength && String(sanitizedValue).length < rule.minLength) {
        fieldErrors.push(
          `${field} must be at least ${rule.minLength} characters long`
        );
      }
      if (rule.maxLength && String(sanitizedValue).length > rule.maxLength) {
        fieldErrors.push(
          `${field} must be no more than ${rule.maxLength} characters long`
        );
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(String(sanitizedValue))) {
        fieldErrors.push(`${field} format is invalid`);
      }

      // Custom validation
      if (rule.custom) {
        const customResult = rule.custom(sanitizedValue);
        if (typeof customResult === "string") {
          fieldErrors.push(customResult);
        } else if (!customResult) {
          fieldErrors.push(`${field} is invalid`);
        }
      }

      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors;
      } else {
        sanitizedData[field] = sanitizedValue;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitizedData,
    };
  }

  private sanitizeString(input: string): string {
    try {
      return securityManager.sanitizeInput(input);
    } catch (error) {
      // If security manager throws, return empty string
      return "";
    }
  }

  // Specific validation methods
  validateProviderConfig(config: any): ValidationResult {
    const schema: ValidationSchema = {
      name: { required: true, type: "string", minLength: 1, maxLength: 100 },
      type: {
        required: true,
        type: "string",
        custom: (value) =>
          ["openai", "anthropic", "google", "mistral", "groq"].includes(value),
      },
      apiKey: { required: true, type: "string", minLength: 10, maxLength: 200 },
      maxTokens: {
        type: "number",
        custom: (value) => value > 0 && value <= 100000,
      },
      temperature: {
        type: "number",
        custom: (value) => value >= 0 && value <= 2,
      },
      timeout: {
        type: "number",
        custom: (value) => value > 0 && value <= 300000,
      },
    };

    return this.validate(config, schema);
  }

  validateAgentConfig(config: any): ValidationResult {
    const schema: ValidationSchema = {
      name: { required: true, type: "string", minLength: 1, maxLength: 100 },
      description: { type: "string", maxLength: 500 },
      type: {
        required: true,
        type: "string",
        custom: (value) =>
          ["chatbot", "assistant", "analyzer", "generator", "custom"].includes(
            value
          ),
      },
      provider: { required: true, type: "string", minLength: 1 },
      model: { required: true, type: "string", minLength: 1 },
      systemPrompt: { type: "string", maxLength: 5000 },
      temperature: {
        type: "number",
        custom: (value) => value >= 0 && value <= 2,
      },
      maxTokens: {
        type: "number",
        custom: (value) => value > 0 && value <= 100000,
      },
    };

    return this.validate(config, schema);
  }

  validateLoginCredentials(credentials: any): ValidationResult {
    const schema: ValidationSchema = {
      email: { required: true, type: "email", maxLength: 254 },
      password: {
        required: true,
        type: "string",
        minLength: 6,
        maxLength: 128,
      },
    };

    return this.validate(credentials, schema);
  }

  validateRegistrationData(data: any): ValidationResult {
    const schema: ValidationSchema = {
      email: { required: true, type: "email", maxLength: 254 },
      password: {
        required: true,
        type: "string",
        minLength: 8,
        maxLength: 128,
        custom: (value) => {
          // Password strength validation
          const hasUpper = /[A-Z]/.test(value);
          const hasLower = /[a-z]/.test(value);
          const hasNumber = /\d/.test(value);
          const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);

          if (!hasUpper)
            return "Password must contain at least one uppercase letter";
          if (!hasLower)
            return "Password must contain at least one lowercase letter";
          if (!hasNumber) return "Password must contain at least one number";
          if (!hasSpecial)
            return "Password must contain at least one special character";

          return true;
        },
      },
      name: { required: true, type: "string", minLength: 1, maxLength: 100 },
    };

    return this.validate(data, schema);
  }
}

// Global validator instance
let validator: Validator | null = null;

export function getValidator(): Validator {
  if (!validator) {
    validator = new Validator();
  }
  return validator;
}

export { Validator };
