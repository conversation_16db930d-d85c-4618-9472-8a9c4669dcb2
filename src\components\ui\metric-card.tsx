import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    period: string;
    type?: "percentage" | "absolute";
  };
  trend?: "up" | "down" | "neutral";
  progress?: {
    value: number;
    max?: number;
    label?: string;
  };
  icon?: React.ReactNode;
  status?: "success" | "warning" | "error" | "info";
  subtitle?: string;
  badge?: string;
  className?: string;
  onClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  trend,
  progress,
  icon,
  status,
  subtitle,
  badge,
  className,
  onClick,
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-success-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-destructive" />;
      default:
        return <Minus className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case "up":
        return "text-success-600";
      case "down":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "success":
        return "border-success-200 bg-success-50/30";
      case "warning":
        return "border-warning-200 bg-warning-50/30";
      case "error":
        return "border-destructive/20 bg-destructive/5";
      case "info":
        return "border-info-200 bg-info-50/30";
      default:
        return "";
    }
  };

  return (
    <Card
      className={cn(
        "relative overflow-hidden transition-all duration-300 hover:shadow-medium",
        status && getStatusColor(),
        onClick && "cursor-pointer hover:scale-[1.02]",
        className
      )}
      onClick={onClick}
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-500/5 pointer-events-none" />

      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          {icon && (
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              {icon}
            </div>
          )}
          <div>
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {title}
            </CardTitle>
            {subtitle && (
              <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {badge && (
            <Badge variant="secondary" className="text-xs">
              {badge}
            </Badge>
          )}
          {onClick && (
            <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="flex items-baseline justify-between">
          <div className="text-3xl font-bold tracking-tight">{value}</div>
          {change && (
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className={cn("text-sm font-medium", getTrendColor())}>
                {change.type === "absolute" ? "" : "+"}
                {change.value}
                {change.type !== "absolute" && "%"}
              </span>
            </div>
          )}
        </div>

        {change && (
          <p className="text-xs text-muted-foreground">
            {trend === "up" ? "↗" : trend === "down" ? "↘" : "→"} from{" "}
            {change.period}
          </p>
        )}

        {progress && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">
                {progress.label || "Progress"}
              </span>
              <span className="font-medium">
                {progress.value}
                {progress.max ? `/${progress.max}` : "%"}
              </span>
            </div>
            <Progress
              value={
                progress.max
                  ? (progress.value / progress.max) * 100
                  : progress.value
              }
              className="h-2"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { MetricCard };
