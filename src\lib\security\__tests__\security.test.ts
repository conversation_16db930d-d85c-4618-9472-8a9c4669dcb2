import { describe, it, expect, beforeEach } from 'vitest';
import { db } from '../../database';
import { SecurityEventType, SecurityEventSeverity } from '../types';
import { SecurityManager } from '../index';

describe('Security Module', () => {
    let securityManager: SecurityManager;

    beforeEach(async () => {
        securityManager = new SecurityManager(db);

        // Clean up existing events
        await db.query('TRUNCATE TABLE security_events CASCADE');
    });

    describe('Event Logging', () => {
        it('should log security events successfully', async () => {
            const event = {
                type: SecurityEventType.LOGIN_ATTEMPT,
                severity: SecurityEventSeverity.INFO,
                userId: 'test-user',
                ipAddress: '127.0.0.1',
                userAgent: 'Mozilla/5.0',
                details: { success: true },
            };

            await securityManager.logEvent(event);

            const result = await db.query('SELECT * FROM security_events ORDER BY created_at DESC LIMIT 1');
            expect(result.rows[0]).toMatchObject({
                type: event.type,
                severity: event.severity,
                user_id: event.userId,
                ip_address: event.ipAddress,
                user_agent: event.userAgent,
                details: event.details,
            });
        });

        it('should handle events with missing optional fields', async () => {
            const event = {
                type: SecurityEventType.API_ACCESS,
                severity: SecurityEventSeverity.WARNING,
                details: { endpoint: '/api/test' },
            };

            await securityManager.logEvent(event);

            const result = await db.query('SELECT * FROM security_events ORDER BY created_at DESC LIMIT 1');
            expect(result.rows[0]).toMatchObject({
                type: event.type,
                severity: event.severity,
                details: event.details,
            });
            expect(result.rows[0].user_id).toBeNull();
            expect(result.rows[0].ip_address).toBeNull();
            expect(result.rows[0].user_agent).toBeNull();
        });
    });

    describe('Event Querying', () => {
        beforeEach(async () => {
            // Insert test events
            const events = [
                {
                    type: SecurityEventType.LOGIN_ATTEMPT,
                    severity: SecurityEventSeverity.INFO,
                    userId: 'user1',
                    details: { success: true },
                },
                {
                    type: SecurityEventType.LOGIN_ATTEMPT,
                    severity: SecurityEventSeverity.WARNING,
                    userId: 'user1',
                    details: { success: false },
                },
                {
                    type: SecurityEventType.API_ACCESS,
                    severity: SecurityEventSeverity.ERROR,
                    userId: 'user2',
                    details: { endpoint: '/api/sensitive' },
                },
            ];

            for (const event of events) {
                await securityManager.logEvent(event);
            }
        });

        it('should query events by type', async () => {
            const loginEvents = await securityManager.getEvents({
                type: SecurityEventType.LOGIN_ATTEMPT,
            });

            expect(loginEvents).toHaveLength(2);
            expect(loginEvents.every(e => e.type === SecurityEventType.LOGIN_ATTEMPT)).toBe(true);
        });

        it('should query events by user', async () => {
            const user1Events = await securityManager.getEvents({
                userId: 'user1',
            });

            expect(user1Events).toHaveLength(2);
            expect(user1Events.every(e => e.userId === 'user1')).toBe(true);
        });

        it('should query events by severity', async () => {
            const errorEvents = await securityManager.getEvents({
                severity: SecurityEventSeverity.ERROR,
            });

            expect(errorEvents).toHaveLength(1);
            expect(errorEvents[0].severity).toBe(SecurityEventSeverity.ERROR);
        });

        it('should query events by time range', async () => {
            const now = new Date();
            const hourAgo = new Date(now.getTime() - 3600000);

            const recentEvents = await securityManager.getEvents({
                startTime: hourAgo,
                endTime: now,
            });

            expect(recentEvents).toHaveLength(3);
        });
    });

    describe('Security Analysis', () => {
        it('should detect suspicious login patterns', async () => {
            // Simulate multiple failed login attempts
            for (let i = 0; i < 5; i++) {
                await securityManager.logEvent({
                    type: SecurityEventType.LOGIN_ATTEMPT,
                    severity: SecurityEventSeverity.WARNING,
                    userId: 'target-user',
                    ipAddress: '***********',
                    details: { success: false },
                });
            }

            const analysis = await securityManager.analyzeSuspiciousActivity('target-user');
            expect(analysis.suspiciousLoginAttempts).toBe(true);
            expect(analysis.failedLoginCount).toBe(5);
        });

        it('should detect suspicious API access patterns', async () => {
            // Simulate rapid API calls
            for (let i = 0; i < 10; i++) {
                await securityManager.logEvent({
                    type: SecurityEventType.API_ACCESS,
                    severity: SecurityEventSeverity.WARNING,
                    userId: 'api-user',
                    ipAddress: '***********',
                    details: { endpoint: '/api/sensitive' },
                });
            }

            const analysis = await securityManager.analyzeSuspiciousActivity('api-user');
            expect(analysis.suspiciousApiAccess).toBe(true);
            expect(analysis.rapidApiCallCount).toBeGreaterThan(5);
        });
    });

    describe('Security Alerts', () => {
        it('should generate alerts for high-severity events', async () => {
            await securityManager.logEvent({
                type: SecurityEventType.UNAUTHORIZED_ACCESS,
                severity: SecurityEventSeverity.CRITICAL,
                userId: 'hacker',
                ipAddress: '********',
                details: { resource: 'admin-panel' },
            });

            const alerts = await securityManager.getActiveAlerts();
            expect(alerts).toHaveLength(1);
            expect(alerts[0]).toMatchObject({
                severity: SecurityEventSeverity.CRITICAL,
                userId: 'hacker',
                resolved: false,
            });
        });

        it('should resolve security alerts', async () => {
            // Create an alert
            const event = {
                type: SecurityEventType.UNAUTHORIZED_ACCESS,
                severity: SecurityEventSeverity.CRITICAL,
                userId: 'hacker',
                details: { resource: 'admin-panel' },
            };
            await securityManager.logEvent(event);

            // Get the alert ID
            const alerts = await securityManager.getActiveAlerts();
            const alertId = alerts[0].id;

            // Resolve the alert
            await securityManager.resolveAlert(alertId, {
                resolvedBy: 'admin',
                resolution: 'Investigated and blocked IP',
            });

            // Verify resolution
            const resolvedAlerts = await securityManager.getActiveAlerts();
            expect(resolvedAlerts).toHaveLength(0);

            const alert = await securityManager.getAlertById(alertId);
            expect(alert.resolved).toBe(true);
            expect(alert.resolutionDetails).toMatchObject({
                resolvedBy: 'admin',
                resolution: 'Investigated and blocked IP',
            });
        });
    });
});
