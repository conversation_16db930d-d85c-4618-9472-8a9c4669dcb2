# SynapseAI Environment Configuration

# Application Environment
NODE_ENV=development
VITE_APP_ENV=development
VITE_APP_VERSION=1.0.0

# API Configuration
VITE_API_BASE_URL=https://api.synapseai.com
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# Authentication
VITE_AUTH_ENABLED=true
VITE_JWT_SECRET=your-jwt-secret-key
VITE_SESSION_TIMEOUT=3600000

# AI Provider API Keys (Production - Set in deployment environment)
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
GOOGLE_API_KEY=AIza-your-google-key
GROQ_API_KEY=gsk_your-groq-key
MISTRAL_API_KEY=your-mistral-key

# Database Configuration
VITE_DATABASE_ENABLED=false
DATABASE_URL=postgresql://user:password@localhost:5432/synapseai
DATABASE_POOL_SIZE=20

# Feature Flags
VITE_FEATURE_ROUTING=true
VITE_FEATURE_EVENT_SYSTEM=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_MONITORING=true
VITE_FEATURE_REAL_TIME=true
VITE_FEATURE_STREAMING=true
VITE_FEATURE_MULTI_MODAL=true

# Monitoring & Observability
VITE_LOGGING_ENABLED=true
VITE_LOGGING_LEVEL=info
VITE_SENTRY_DSN=your-sentry-dsn
VITE_ANALYTICS_TRACKING_ID=your-analytics-id

# Performance Configuration
VITE_ENABLE_CODE_SPLITTING=true
VITE_ENABLE_LAZY_LOADING=true
VITE_BUNDLE_ANALYZER=false

# Rate Limiting
VITE_RATE_LIMIT_ENABLED=true
VITE_RATE_LIMIT_REQUESTS_PER_MINUTE=60
VITE_RATE_LIMIT_REQUESTS_PER_DAY=10000

# Security
VITE_CORS_ENABLED=true
VITE_CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
VITE_CSRF_PROTECTION=true

# Development Tools
VITE_TEMPO=true
VITE_DEBUG_MODE=false
VITE_MOCK_API=false
