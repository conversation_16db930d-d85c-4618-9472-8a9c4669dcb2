import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { MetricCard } from "@/components/ui/metric-card";
import { StatusIndicator } from "@/components/ui/status-indicator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Settings,
  Trash2,
  Play,
  Pause,
  Copy,
  Edit,
  Brain,
  Activity,
  Clock,
  Users,
  Upload,
  CheckCircle,
  AlertCircle,
  Loader2,
  GitBranch,
  Rocket,
} from "lucide-react";

interface AIAgent {
  id: string;
  name: string;
  description: string;
  type: "chatbot" | "assistant" | "analyzer" | "generator" | "custom";
  status: "active" | "paused" | "draft" | "error";
  version: string;
  provider: string;
  model: string;
  systemPrompt: string;
  config: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
  };
  metrics: {
    totalInteractions: number;
    successRate: number;
    avgResponseTime: number;
    lastUsed?: string;
  };
  createdAt: string;
  updatedAt: string;
  deployedAt?: string;
}

interface AgentControlPanelProps {
  onAgentChange?: (agents: AIAgent[]) => void;
}

const AgentControlPanel: React.FC<AgentControlPanelProps> = ({
  onAgentChange = () => {},
}) => {
  const [agents, setAgents] = useState<AIAgent[]>([]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<AIAgent | null>(null);
  const [deployingAgent, setDeployingAgent] = useState<string | null>(null);
  const [deploymentHistory, setDeploymentHistory] = useState<
    Record<
      string,
      Array<{
        version: string;
        timestamp: string;
        status: "success" | "failed" | "rollback";
        environment: "development" | "staging" | "production";
        notes?: string;
      }>
    >
  >({});
  const [newAgent, setNewAgent] = useState({
    name: "",
    description: "",
    type: "chatbot" as const,
    provider: "openai-1",
    model: "gpt-4",
    systemPrompt: "",
    temperature: 0.7,
    maxTokens: 1000,
  });

  useEffect(() => {
    loadAgents();
  }, []);

  useEffect(() => {
    onAgentChange(agents);
  }, [agents, onAgentChange]);

  const loadAgents = async () => {
    try {
      const { getAPIClient } = await import("@/lib/apiClient");
      const apiClient = getAPIClient();
      const response = await apiClient.getAgents();

      if (response && Array.isArray(response)) {
        const transformedAgents = response.map((agent: any) => ({
          id: agent.id,
          name: agent.name,
          description: agent.description,
          type: agent.type,
          status: agent.status,
          version: agent.version,
          provider: agent.provider_id,
          model: agent.model,
          systemPrompt: agent.system_prompt,
          config: agent.config || {
            temperature: 0.7,
            maxTokens: 1000,
            topP: 0.9,
            frequencyPenalty: 0.1,
          },
          metrics: agent.metrics || {
            totalInteractions: 0,
            successRate: 0,
            avgResponseTime: 0,
          },
          createdAt: agent.created_at,
          updatedAt: agent.updated_at,
          deployedAt: agent.deployed_at,
        }));
        setAgents(transformedAgents);
      } else {
        console.warn("No agents found or invalid response format");
        setAgents([]);
      }
    } catch (error) {
      console.error("Failed to load agents:", error);
      // Implement proper error handling
      if (error instanceof Error) {
        console.error("Error loading agents:", error.message);
      }
      setAgents([]);
    }
  };

  const handleCreateAgent = async () => {
    try {
      const { getAPIClient } = await import("@/lib/apiClient");
      const apiClient = getAPIClient();
      const agentData = {
        name: newAgent.name,
        description: newAgent.description,
        type: newAgent.type,
        status: "draft",
        version: "v1.0",
        provider_id: newAgent.provider,
        model: newAgent.model,
        system_prompt: newAgent.systemPrompt,
        config: {
          temperature: newAgent.temperature,
          maxTokens: newAgent.maxTokens,
          topP: 0.9,
          frequencyPenalty: 0.1,
        },
      };

      const createdAgent = await apiClient.createAgent(agentData);

      const agent: AIAgent = {
        id: createdAgent.id,
        name: createdAgent.name,
        description: createdAgent.description,
        type: createdAgent.type,
        status: createdAgent.status,
        version: createdAgent.version,
        provider: createdAgent.provider_id,
        model: createdAgent.model,
        systemPrompt: createdAgent.system_prompt,
        config: createdAgent.config,
        metrics: {
          totalInteractions: 0,
          successRate: 0,
          avgResponseTime: 0,
        },
        createdAt: createdAgent.created_at,
        updatedAt: createdAgent.updated_at,
        deployedAt: createdAgent.deployed_at,
      };

      setAgents((prev) => [...prev, agent]);
      setNewAgent({
        name: "",
        description: "",
        type: "chatbot",
        provider: "openai-1",
        model: "gpt-4",
        systemPrompt: "",
        temperature: 0.7,
        maxTokens: 1000,
      });
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Failed to create agent:", error);
    }
  };

  const handleToggleAgent = (id: string) => {
    setAgents((prev) =>
      prev.map((agent) => {
        if (agent.id === id) {
          const newStatus =
            agent.status === "active"
              ? "paused"
              : agent.status === "paused"
                ? "active"
                : agent.status;
          return {
            ...agent,
            status: newStatus,
            deployedAt:
              newStatus === "active"
                ? new Date().toISOString()
                : agent.deployedAt,
          };
        }
        return agent;
      })
    );
  };

  const handleDeployAgent = async (
    id: string,
    environment: "development" | "staging" | "production" = "production"
  ) => {
    setDeployingAgent(id);

    try {
      const { getAPIClient } = await import("@/lib/apiClient");
      const apiClient = getAPIClient();
      const deployResult = await apiClient.deployAgent(id);

      const agent = agents.find((a) => a.id === id);
      if (!agent) throw new Error("Agent not found");

      // Increment version
      const currentVersion = agent.version;
      const versionParts = currentVersion.replace("v", "").split(".");
      const newVersion = `v${versionParts[0]}.${parseInt(versionParts[1]) + 1}`;

      setAgents((prev) =>
        prev.map((agent) =>
          agent.id === id
            ? {
                ...agent,
                status: "active",
                version: newVersion,
                deployedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              }
            : agent
        )
      );

      // Add to deployment history
      setDeploymentHistory((prev) => ({
        ...prev,
        [id]: [
          {
            version: newVersion,
            timestamp: new Date().toISOString(),
            status: "success",
            environment,
            notes: `Deployed to ${environment} environment`,
          },
          ...(prev[id] || []),
        ],
      }));
    } catch (error) {
      // Handle deployment failure
      setDeploymentHistory((prev) => ({
        ...prev,
        [id]: [
          {
            version: agents.find((a) => a.id === id)?.version || "unknown",
            timestamp: new Date().toISOString(),
            status: "failed",
            environment,
            notes: `Deployment failed: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
          ...(prev[id] || []),
        ],
      }));
    } finally {
      setDeployingAgent(null);
    }
  };

  const handleDeleteAgent = async (id: string) => {
    try {
      const { getAPIClient } = await import("@/lib/apiClient");
      const apiClient = getAPIClient();
      const success = await apiClient.deleteAgent(id);

      if (success) {
        setAgents((prev) => prev.filter((agent) => agent.id !== id));
      }
    } catch (error) {
      console.error("Failed to delete agent:", error);
    }
  };

  const handleCloneAgent = (agent: AIAgent) => {
    const clonedAgent: AIAgent = {
      ...agent,
      id: Math.random().toString(36).substring(2, 9),
      name: `${agent.name} (Copy)`,
      status: "draft",
      version: "v1.0",
      metrics: {
        totalInteractions: 0,
        successRate: 0,
        avgResponseTime: 0,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deployedAt: undefined,
    };

    setAgents((prev) => [...prev, clonedAgent]);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Play className="h-4 w-4 text-green-500" />;
      case "paused":
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <Activity className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "paused":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case "error":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "chatbot":
        return "💬";
      case "assistant":
        return "🤖";
      case "analyzer":
        return "📊";
      case "generator":
        return "✨";
      default:
        return "🧠";
    }
  };

  return (
    <EnhancedCard className="w-full" gradient interactive>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Agent Control
        </CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="flex items-center gap-1">
              <Plus className="h-4 w-4" />
              Create Agent
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create AI Agent</DialogTitle>
              <DialogDescription>
                Configure a new AI agent for your specific use case.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="agent-name"
                  value={newAgent.name}
                  onChange={(e) =>
                    setNewAgent((prev) => ({ ...prev, name: e.target.value }))
                  }
                  className="col-span-3"
                  placeholder="Customer Support Bot"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="agent-description"
                  value={newAgent.description}
                  onChange={(e) =>
                    setNewAgent((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  className="col-span-3"
                  placeholder="Describe what this agent does..."
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-type" className="text-right">
                  Type
                </Label>
                <Select
                  value={newAgent.type}
                  onValueChange={(value: any) =>
                    setNewAgent((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chatbot">Chatbot</SelectItem>
                    <SelectItem value="assistant">Assistant</SelectItem>
                    <SelectItem value="analyzer">Analyzer</SelectItem>
                    <SelectItem value="generator">Generator</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="system-prompt" className="text-right">
                  System Prompt
                </Label>
                <Textarea
                  id="system-prompt"
                  value={newAgent.systemPrompt}
                  onChange={(e) =>
                    setNewAgent((prev) => ({
                      ...prev,
                      systemPrompt: e.target.value,
                    }))
                  }
                  className="col-span-3"
                  placeholder="You are a helpful assistant..."
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateAgent}>Create Agent</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="agents" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="deployment">Deployment</TabsTrigger>
            <TabsTrigger value="history">Deployment History</TabsTrigger>
            <TabsTrigger value="versions">Versions</TabsTrigger>
          </TabsList>

          <TabsContent value="agents" className="space-y-4">
            {agents.map((agent) => (
              <EnhancedCard
                key={agent.id}
                className="p-4"
                interactive
                status={
                  agent.status === "active"
                    ? "success"
                    : agent.status === "error"
                      ? "error"
                      : "info"
                }
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <StatusIndicator
                      status={
                        agent.status === "active"
                          ? "online"
                          : agent.status === "error"
                            ? "error"
                            : "offline"
                      }
                      size="md"
                      variant="dot"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{agent.name}</h4>
                        <Badge variant="outline">{agent.version}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {agent.description}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline">{agent.type}</Badge>
                        <Badge className={getStatusBadgeColor(agent.status)}>
                          {agent.status}
                        </Badge>
                        <Badge variant="secondary">{agent.provider}</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {agent.status === "draft" && (
                      <Button
                        size="sm"
                        onClick={() => handleDeployAgent(agent.id)}
                        disabled={deployingAgent === agent.id}
                      >
                        {deployingAgent === agent.id ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Rocket className="h-4 w-4 mr-1" />
                        )}
                        {deployingAgent === agent.id
                          ? "Deploying..."
                          : "Deploy"}
                      </Button>
                    )}
                    {agent.status === "active" && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeployAgent(agent.id)}
                        disabled={deployingAgent === agent.id}
                      >
                        {deployingAgent === agent.id ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Upload className="h-4 w-4 mr-1" />
                        )}
                        {deployingAgent === agent.id ? "Updating..." : "Update"}
                      </Button>
                    )}
                    {(agent.status === "active" ||
                      agent.status === "paused") && (
                      <Switch
                        checked={agent.status === "active"}
                        onCheckedChange={() => handleToggleAgent(agent.id)}
                      />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCloneAgent(agent)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500"
                      onClick={() => handleDeleteAgent(agent.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Interactions</p>
                    <p className="font-medium">
                      {agent.metrics.totalInteractions}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Success Rate</p>
                    <p className="font-medium">{agent.metrics.successRate}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Avg Response</p>
                    <p className="font-medium">
                      {agent.metrics.avgResponseTime}ms
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Model</p>
                    <p className="font-medium">{agent.model}</p>
                  </div>
                </div>

                {agent.deployedAt && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    Deployed: {new Date(agent.deployedAt).toLocaleString()}
                  </div>
                )}

                {deploymentHistory[agent.id] &&
                  deploymentHistory[agent.id].length > 0 && (
                    <div className="mt-2 p-2 bg-muted/50 rounded">
                      <div className="text-xs font-medium mb-1">
                        Latest Deployment:
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="flex items-center gap-1">
                          {deploymentHistory[agent.id][0].status ===
                          "success" ? (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          ) : (
                            <AlertCircle className="h-3 w-3 text-red-500" />
                          )}
                          {deploymentHistory[agent.id][0].version}
                        </span>
                        <span className="text-muted-foreground">
                          {new Date(
                            deploymentHistory[agent.id][0].timestamp
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {deploymentHistory[agent.id][0].environment} •{" "}
                        {deploymentHistory[agent.id][0].status}
                      </div>
                    </div>
                  )}
              </EnhancedCard>
            ))}
          </TabsContent>

          <TabsContent value="deployment" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <MetricCard
                title="Active Agents"
                value={agents.filter((a) => a.status === "active").length}
                icon={<Play className="h-4 w-4" />}
                status="success"
                trend="up"
                change={{
                  value: 12.5,
                  period: "last week",
                }}
              />
              <MetricCard
                title="Total Interactions"
                value={agents
                  .reduce((sum, a) => sum + a.metrics.totalInteractions, 0)
                  .toLocaleString()}
                icon={<Users className="h-4 w-4" />}
                status="info"
                trend="up"
                change={{
                  value: 8.3,
                  period: "last week",
                }}
              />
              <MetricCard
                title="Avg Success Rate"
                value={`${(agents.reduce((sum, a) => sum + a.metrics.successRate, 0) / agents.length).toFixed(1)}%`}
                icon={<Activity className="h-4 w-4" />}
                status="success"
                trend="up"
                change={{
                  value: 2.1,
                  period: "last week",
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Deployment History</h3>
              {Object.entries(deploymentHistory).map(([agentId, history]) => {
                const agent = agents.find((a) => a.id === agentId);
                if (!agent || history.length === 0) return null;

                return (
                  <Card key={agentId}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base flex items-center gap-2">
                        <GitBranch className="h-4 w-4" />
                        {agent.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {history.slice(0, 5).map((deployment, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 border rounded"
                          >
                            <div className="flex items-center space-x-3">
                              {deployment.status === "success" ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : deployment.status === "failed" ? (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              ) : (
                                <Activity className="h-4 w-4 text-yellow-500" />
                              )}
                              <div>
                                <div className="font-medium text-sm">
                                  {deployment.version}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {deployment.environment} • {deployment.status}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm">
                                {new Date(
                                  deployment.timestamp
                                ).toLocaleDateString()}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(
                                  deployment.timestamp
                                ).toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        ))}
                        {history.length > 5 && (
                          <div className="text-center text-sm text-muted-foreground">
                            +{history.length - 5} more deployments
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
              {Object.keys(deploymentHistory).length === 0 && (
                <div className="text-center py-8">
                  <GitBranch className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">
                    No deployment history available
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="versions" className="space-y-4">
            <div className="text-center py-8">
              <Settings className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                Version management features will be available here.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </EnhancedCard>
  );
};

export default AgentControlPanel;
