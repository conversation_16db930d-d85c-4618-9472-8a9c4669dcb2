// Mock API Server for Development

import { db } from "./database/index";
import { getConfig } from "./config";
import { RealAPIClient, RealAPIResponse } from "./realApiClient";
import { getLogger } from "./logger";

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId: string;
}

class MockAPIServer extends RealAPIClient {
  private static instance: MockAPIServer;

  constructor() {
    super();
  } 

  login(credentials: { email: string; password: string; }): Promise<any> {
    throw new Error("Method not implemented.");
  }

  getProviders(): Promise<any[]> {
    throw new Error("Method not implemented.");
  }

  createProvider(provider: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
    updateProvider(id: string, updates: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  deleteProvider(id: string): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  testProvider(id: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  getAgents(): Promise<any[]> {
    throw new Error("Method not implemented.");
  }
  createAgent(agent: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  updateAgent(id: string, updates: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  deleteAgent(id: string): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  deployAgent(id: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  getAnalytics(timeRange?: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  getUsageStats(timeRange?: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  healthCheck(): Promise<RealAPIResponse<{ status: string; timestamp: string; }>> {
    throw new Error("Method not implemented.");
  }

  static getInstance(): MockAPIServer {
    if (!MockAPIServer.instance) {
      MockAPIServer.instance = new MockAPIServer();
    }
    return MockAPIServer.instance;
  }

  private setupMockEndpoints(): void {
    // Intercept fetch requests for API endpoints
    const originalFetch = window.fetch;

    window.fetch = async (
      input: RequestInfo | URL,
      init?: RequestInit
    ): Promise<Response> => {
      const url = typeof input === "string" ? input : input.toString();

      // Check if this is an API request we should mock
      if (url.includes("/api/v1/")) {
        return this.handleMockRequest(url, init);
      }

      // Otherwise, use the original fetch
      return originalFetch(input, init);
    };
  }

  private async handleMockRequest(
    url: string,
    init?: RequestInit
  ): Promise<Response> {
    const method = init?.method || "GET";
    const body = init?.body ? JSON.parse(init.body as string) : null;

    try {
      let result: APIResponse;

      // Route to appropriate handler
      if (url.includes("/providers")) {
        result = await this.handleProviderRequest(url, method, body);
      } else if (url.includes("/agents")) {
        result = await this.handleAgentRequest(url, method, body);
      } else if (url.includes("/analytics")) {
        result = await this.handleAnalyticsRequest(url, method, body);
      } else if (url.includes("/health")) {
        result = await this.handleHealthRequest();
      } else {
        result = {
          success: false,
          error: "Endpoint not found",
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };
      }

      return new Response(JSON.stringify(result), {
        status: result.success ? 200 : 400,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      const errorResult: APIResponse = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
      };

      return new Response(JSON.stringify(errorResult), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }

  private async handleProviderRequest(
    url: string,
    method: string,
    body: any
  ): Promise<APIResponse> {
    const pathParts = url.split("/");
    const providerId = pathParts[pathParts.indexOf("providers") + 1];

    switch (method) {
      case "GET":
        if (providerId && providerId !== "providers") {
          const provider = await db.getProvider(providerId);
          return {
            success: !!provider,
            data: provider,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        } else {
          const providers = await db.getProviders();
          return {
            success: true,
            data: providers,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        }

      case "POST":
        if (url.includes("/test")) {
          // Mock provider test
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return {
            success: Math.random() > 0.2, // 80% success rate
            data: {
              responseTime: Math.floor(Math.random() * 2000) + 500,
              message: "Test completed",
            },
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        } else {
          const provider = await db.createProvider(body);
          return {
            success: true,
            data: provider,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        }

      case "PUT":
        const updatedProvider = await db.updateProvider(providerId, body);
        return {
          success: !!updatedProvider,
          data: updatedProvider,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };

      case "DELETE":
        const deleted = await db.deleteProvider(providerId);
        return {
          success: deleted,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };

      default:
        return {
          success: false,
          error: "Method not allowed",
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };
    }
  }

  private async handleAgentRequest(
    url: string,
    method: string,
    body: any
  ): Promise<APIResponse> {
    const pathParts = url.split("/");
    const agentId = pathParts[pathParts.indexOf("agents") + 1];

    switch (method) {
      case "GET":
        if (agentId && agentId !== "agents") {
          const agent = await db.getAgent(agentId);
          return {
            success: !!agent,
            data: agent,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        } else {
          const agents = await db.getAgents();
          return {
            success: true,
            data: agents,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        }

      case "POST":
        if (url.includes("/deploy")) {
          // Mock agent deployment
          await new Promise((resolve) => setTimeout(resolve, 2000));
          const updatedAgent = await db.updateAgent(agentId, {
            status: "active",
            deployed_at: new Date(),
            updated_at: new Date(),
            });
          return {
            success: true,
            data: updatedAgent,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        } else {
          const agent = await db.createAgent(body);
          return {
            success: true,
            data: agent,
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId(),
          };
        }

      case "PUT":
        const updatedAgent = await db.updateAgent(agentId, body);
        return {
          success: !!updatedAgent,
          data: updatedAgent,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };

      case "DELETE":
        const deleted = await db.deleteAgent(agentId);
        return {
          success: deleted,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };

      default:
        return {
          success: false,
          error: "Method not allowed",
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId(),
        };
    }
  }

  private async handleAnalyticsRequest(
    url: string,
    method: string,
    body: any
  ): Promise<APIResponse> {
    if (method !== "GET") {
      return {
        success: false,
        error: "Method not allowed",
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
      };
    }

    if (url.includes("/overview")) {
      const providerStats = await db.getMetrics("provider", "provider", 100);
      const agentStats = await db.getMetrics("agent", "agent", 100);

      return {
        success: true,
        data: {
          providers: providerStats,
          agents: agentStats,
          summary: {
            totalRequests: providerStats.reduce(
              (sum, p) => sum + (p.metrics.requests || 0),
              0
            ),
            totalCost: providerStats.reduce(
              (sum, p) => sum + (p.metrics.cost || 0),
              0
            ),
            avgLatency:
              providerStats.reduce((sum, p) => sum + (p.metrics.avgLatency || 0), 0) /
                providerStats.length || 0,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
      };
    }

    return {
      success: true,
      data: {},
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
    };
  }

    private async handleHealthRequest(): Promise<APIResponse> {
    const healthStatus = await db.healthCheck();

    return {
      success: true,
      data: {
        status: healthStatus.status,
        timestamp: new Date().toISOString(),
        database: healthStatus.status,
        latency: healthStatus.latency,
      },
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
    };
  }

  private generateRequestId(): string {
    return `mock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
}

// Initialize mock API server
export const APIServer = MockAPIServer.getInstance();
