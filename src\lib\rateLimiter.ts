// Rate Limiting and DDoS Protection for SynapseAI

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDuration?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface RateLimitInfo {
  totalHits: number;
  totalHitsInWindow: number;
  remainingPoints: number;
  msBeforeNext: number;
  isBlocked: boolean;
}

class RateLimiter {
  private static instance: RateLimiter;
  private requestCounts = new Map<
    string,
    { count: number; resetTime: number; blocked?: number }
  >();
  private blockedIPs = new Set<string>();
  private suspiciousIPs = new Map<
    string,
    { violations: number; lastViolation: number }
  >();

  private constructor() {
    // Clean up old entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  // Check if request should be allowed
  checkLimit(identifier: string, config: RateLimitConfig): RateLimitInfo {
    const now = Date.now();
    const record = this.requestCounts.get(identifier);

    // Check if IP is blocked
    if (this.blockedIPs.has(identifier)) {
      return {
        totalHits: record?.count || 0,
        totalHitsInWindow: record?.count || 0,
        remainingPoints: 0,
        msBeforeNext: config.blockDuration || 3600000, // 1 hour default
        isBlocked: true,
      };
    }

    // Initialize or reset if window expired
    if (!record || now > record.resetTime) {
      this.requestCounts.set(identifier, {
        count: 1,
        resetTime: now + config.windowMs,
      });

      return {
        totalHits: 1,
        totalHitsInWindow: 1,
        remainingPoints: config.maxRequests - 1,
        msBeforeNext: 0,
        isBlocked: false,
      };
    }

    // Increment counter
    record.count++;
    const remainingPoints = Math.max(0, config.maxRequests - record.count);
    const isBlocked = record.count > config.maxRequests;

    // Block if limit exceeded
    if (isBlocked) {
      this.handleViolation(identifier, config);
    }

    return {
      totalHits: record.count,
      totalHitsInWindow: record.count,
      remainingPoints,
      msBeforeNext: record.resetTime - now,
      isBlocked,
    };
  }

  // Handle rate limit violations
  private handleViolation(identifier: string, config: RateLimitConfig): void {
    const now = Date.now();
    const suspicious = this.suspiciousIPs.get(identifier);

    if (!suspicious) {
      this.suspiciousIPs.set(identifier, {
        violations: 1,
        lastViolation: now,
      });
    } else {
      suspicious.violations++;
      suspicious.lastViolation = now;

      // Block IP if too many violations
      if (suspicious.violations >= 3) {
        this.blockIP(identifier, config.blockDuration || 3600000);
      }
    }
  }

  // Block an IP address
  blockIP(ip: string, duration: number = 3600000): void {
    this.blockedIPs.add(ip);

    // Auto-unblock after duration
    setTimeout(() => {
      this.blockedIPs.delete(ip);
      this.suspiciousIPs.delete(ip);
    }, duration);

    console.warn(
      `IP ${ip} blocked for ${duration}ms due to rate limit violations`
    );
  }

  // Manually unblock an IP
  unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
    this.suspiciousIPs.delete(ip);
    this.requestCounts.delete(ip);
  }

  // Check if IP is blocked
  isBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  // Get current status for an identifier
  getStatus(identifier: string): {
    isBlocked: boolean;
    requestCount: number;
    violations: number;
    resetTime?: number;
  } {
    const record = this.requestCounts.get(identifier);
    const suspicious = this.suspiciousIPs.get(identifier);

    return {
      isBlocked: this.blockedIPs.has(identifier),
      requestCount: record?.count || 0,
      violations: suspicious?.violations || 0,
      resetTime: record?.resetTime,
    };
  }

  // Clean up old entries
  private cleanup(): void {
    const now = Date.now();

    // Clean up expired request counts
    for (const [key, record] of this.requestCounts.entries()) {
      if (now > record.resetTime) {
        this.requestCounts.delete(key);
      }
    }

    // Clean up old suspicious IPs (older than 24 hours)
    for (const [key, record] of this.suspiciousIPs.entries()) {
      if (now - record.lastViolation > 24 * 60 * 60 * 1000) {
        this.suspiciousIPs.delete(key);
      }
    }
  }

  // Get statistics
  getStats(): {
    totalTrackedIPs: number;
    blockedIPs: number;
    suspiciousIPs: number;
    activeRequests: number;
  } {
    return {
      totalTrackedIPs: this.requestCounts.size,
      blockedIPs: this.blockedIPs.size,
      suspiciousIPs: this.suspiciousIPs.size,
      activeRequests: Array.from(this.requestCounts.values()).reduce(
        (sum, record) => sum + record.count,
        0
      ),
    };
  }

  // Reset all data (for testing/admin purposes)
  reset(): void {
    this.requestCounts.clear();
    this.blockedIPs.clear();
    this.suspiciousIPs.clear();
  }
}

// Predefined rate limit configurations
export const rateLimitConfigs = {
  // General API requests
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    blockDuration: 60 * 60 * 1000, // 1 hour
  },

  // Authentication attempts
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    blockDuration: 30 * 60 * 1000, // 30 minutes
  },

  // Password reset attempts
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    blockDuration: 2 * 60 * 60 * 1000, // 2 hours
  },

  // File uploads
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    blockDuration: 10 * 60 * 1000, // 10 minutes
  },

  // Search requests
  search: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    blockDuration: 5 * 60 * 1000, // 5 minutes
  },
};

export const rateLimiter = RateLimiter.getInstance();

// Utility function to get client IP (mock for frontend)
export function getClientIP(): string {
  // In a real application, this would be extracted from request headers
  // For frontend demo, we'll use a session-based identifier
  let clientId = sessionStorage.getItem("client_id");
  if (!clientId) {
    clientId = "client_" + Math.random().toString(36).substring(2, 15);
    sessionStorage.setItem("client_id", clientId);
  }
  return clientId;
}

// Middleware-like function for rate limiting
export function withRateLimit<T extends (...args: any[]) => any>(
  fn: T,
  config: RateLimitConfig,
  identifier?: string
): T {
  return ((...args: any[]) => {
    const id = identifier || getClientIP();
    const limitInfo = rateLimiter.checkLimit(id, config);

    if (limitInfo.isBlocked) {
      throw new Error(
        `Rate limit exceeded. Try again in ${Math.ceil(limitInfo.msBeforeNext / 1000)} seconds.`
      );
    }

    return fn(...args);
  }) as T;
}
