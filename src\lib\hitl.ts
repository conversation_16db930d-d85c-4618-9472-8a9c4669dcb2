// Human-in-the-Loop (HITL) Control System for SynapseAI

import { getLogger } from "./logger";
import { getE<PERSON>r<PERSON><PERSON><PERSON> } from "./errorHandler";
import { getAPXClient } from "./websocket";
import { getConfig } from "./config";

export interface HITLRequest {
  id: string;
  type: "approval" | "clarification" | "override" | "review";
  priority: "low" | "normal" | "high" | "urgent";
  title: string;
  description: string;
  context: {
    metadata: Record<string, any>;
    sessionId?: string;
    agentId?: string;
    userId?: string;
    toolName?: string;
    originalRequest?: any;
    suggestedResponse?: any;
  };
  options?: HITLOption[];
  timeout: number;
  createdAt: string;
  assignedTo?: string;
  status: "pending" | "in_progress" | "completed" | "timeout" | "cancelled";
  response?: HITLResponse;
  metadata: Record<string, any>;
}

export interface HITLOption {
  id: string;
  label: string;
  value: any;
  description?: string;
  recommended?: boolean;
}

export interface HITLResponse {
  requestId: string;
  action: "approve" | "reject" | "modify" | "retry" | "escalate";
  data?: any;
  feedback?: string;
  respondedBy: string;
  respondedAt: string;
  confidence: number;
}

export interface HITLRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  conditions: HITLCondition[];
  action:
    | "require_approval"
    | "request_clarification"
    | "allow_override"
    | "auto_escalate";
  priority: "low" | "normal" | "high" | "urgent";
  timeout: number;
  assignees: string[];
  metadata: Record<string, any>;
}

export interface HITLCondition {
  field: string;
  operator:
    | "equals"
    | "contains"
    | "greater_than"
    | "less_than"
    | "in"
    | "regex";
  value: any;
  negate?: boolean;
}

export interface HITLConfig {
  enabled: boolean;
  defaultTimeout: number;
  maxConcurrentRequests: number;
  autoEscalationTime: number;
  notificationChannels: string[];
  fallbackBehavior: "allow" | "deny" | "queue";
}

class HITLController {
  private requests: Map<string, HITLRequest> = new Map();
  private rules: Map<string, HITLRule> = new Map();
  private config: HITLConfig;
  private logger = getLogger().createChild("HITL");
  private errorHandler = getErrorHandler();
  private apxClient = getAPXClient();
  private timeouts: Map<string, NodeJS.Timeout> = new Map();

  constructor(config?: Partial<HITLConfig>) {
    this.config = {
      enabled: true,
      defaultTimeout: 300000, // 5 minutes
      maxConcurrentRequests: 50,
      autoEscalationTime: 600000, // 10 minutes
      notificationChannels: ["email", "slack"],
      fallbackBehavior: "queue",
      ...config,
    };

    this.setupDefaultRules();
    this.setupEventHandlers();
  }

  private setupDefaultRules(): void {
    // High-cost operation approval
    this.addRule({
      id: "high_cost_approval",
      name: "High Cost Operation Approval",
      description: "Require approval for operations with estimated cost > $10",
      enabled: true,
      conditions: [
        {
          field: "estimatedCost",
          operator: "greater_than",
          value: 10,
        },
      ],
      action: "require_approval",
      priority: "high",
      timeout: 600000, // 10 minutes
      assignees: ["admin", "finance_team"],
      metadata: { category: "cost_control" },
    });

    // Sensitive data access
    this.addRule({
      id: "sensitive_data_access",
      name: "Sensitive Data Access Control",
      description: "Require approval for accessing sensitive data",
      enabled: true,
      conditions: [
        {
          field: "dataClassification",
          operator: "in",
          value: ["confidential", "restricted", "pii"],
        },
      ],
      action: "require_approval",
      priority: "urgent",
      timeout: 300000, // 5 minutes
      assignees: ["security_team", "data_protection_officer"],
      metadata: { category: "data_security" },
    });

    // External API calls
    this.addRule({
      id: "external_api_review",
      name: "External API Call Review",
      description: "Review external API calls for security",
      enabled: true,
      conditions: [
        {
          field: "toolName",
          operator: "equals",
          value: "api_call",
        },
        {
          field: "url",
          operator: "regex",
          value: "^https?://(?!internal\\.).*",
        },
      ],
      action: "request_clarification",
      priority: "normal",
      timeout: 180000, // 3 minutes
      assignees: ["security_team"],
      metadata: { category: "api_security" },
    });

    // Model confidence threshold
    this.addRule({
      id: "low_confidence_review",
      name: "Low Confidence Response Review",
      description: "Review responses with low confidence scores",
      enabled: true,
      conditions: [
        {
          field: "confidence",
          operator: "less_than",
          value: 0.7,
        },
      ],
      action: "allow_override",
      priority: "low",
      timeout: 120000, // 2 minutes
      assignees: ["quality_team"],
      metadata: { category: "quality_control" },
    });
  }

  private setupEventHandlers(): void {
    // Listen for HITL requests from agents
    this.apxClient.subscribe("hitl_request", (message) => {
      this.handleIncomingRequest(message.data);
    });

    // Listen for HITL responses from humans
    this.apxClient.subscribe("hitl_response", (message) => {
      this.handleHumanResponse(message.data);
    });
  }

  private handleIncomingRequest(requestData: any): void {
    try {
      // Validate required fields
      if (!requestData.title || !requestData.description) {
        throw new Error("Title and description are required for HITL requests");
      }

      const request: HITLRequest = {
        id: requestData.id || this.generateId(),
        type: requestData.type || "review",
        priority: requestData.priority || "normal",
        title: requestData.title,
        description: requestData.description,
        context: requestData.context || {},
        options: requestData.options,
        timeout: requestData.timeout || this.config.defaultTimeout,
        createdAt: new Date().toISOString(),
        status: "pending",
        metadata: requestData.metadata || {},
      };

      // Validate priority and type
      const validPriorities = ["low", "medium", "high", "urgent"];
      const validTypes = ["approval", "clarification", "override", "review"];

      if (!validPriorities.includes(request.priority)) {
        request.priority = "normal";
      }

      if (!validTypes.includes(request.type)) {
        request.type = "review";
      }

      this.processRequest(request);
    } catch (error) {
      this.errorHandler.handleError({
        message: "Failed to handle incoming HITL request",
        stack: error instanceof Error ? error.stack : undefined,
        component: "HITL",
        severity: "medium",
        category: "system",
        metadata: { requestData },
      });
    }
  }

  private handleHumanResponse(responseData: HITLResponse): void {
    const request = this.requests.get(responseData.requestId);
    if (!request) {
      this.logger.warn("Received response for unknown HITL request", {
        requestId: responseData.requestId,
      });
      return;
    }

    this.completeRequest(request.id, responseData);
  }

  async createRequest(
    type: HITLRequest["type"],
    title: string,
    description: string,
    context: HITLRequest["context"],
    options?: HITLOption[],
    priority: HITLRequest["priority"] = "normal",
    timeout?: number
  ): Promise<string> {
    if (!this.config.enabled) {
      throw new Error("HITL is disabled");
    }

    if (this.requests.size >= this.config.maxConcurrentRequests) {
      // Try to clean up old requests first
      this.cleanupOldRequests();

      if (this.requests.size >= this.config.maxConcurrentRequests) {
        throw new Error("Maximum concurrent HITL requests reached");
      }
    }

    // Validate inputs
    if (!title?.trim() || !description?.trim()) {
      throw new Error("Title and description cannot be empty");
    }

    if (!context?.agentId && !context?.sessionId) {
      throw new Error(
        "Either agentId or sessionId must be provided in context"
      );
    }

    const request: HITLRequest = {
      id: this.generateId(),
      type,
      priority,
      title: title.trim(),
      description: description.trim(),
      context,
      options,
      timeout: timeout || this.config.defaultTimeout,
      createdAt: new Date().toISOString(),
      status: "pending",
      metadata: {
        createdBy: "system",
        source: "api",
        ...context.metadata,
      },
    };

    await this.processRequest(request);
    return request.id;
  }

  private async processRequest(request: HITLRequest): Promise<void> {
    this.requests.set(request.id, request);

    this.logger.info("Processing HITL request", {
      requestId: request.id,
      type: request.type,
      priority: request.priority,
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      this.handleTimeout(request.id);
    }, request.timeout);
    this.timeouts.set(request.id, timeoutId);

    // Notify assigned users
    await this.notifyAssignees(request);

    // Send to APIX for real-time updates
    this.apxClient.send({
      id: this.generateId(),
      type: "hitl_request",
      timestamp: new Date().toISOString(),
      data: request,
    });
  }

  private async notifyAssignees(request: HITLRequest): Promise<void> {
    // Find applicable rules to determine assignees
    const applicableRules = this.findApplicableRules(request.context);
    const assignees = new Set<string>();

    applicableRules.forEach((rule) => {
      rule.assignees.forEach((assignee) => assignees.add(assignee));
    });

    if (assignees.size === 0) {
      assignees.add("default_reviewer");
    }

    // Send notifications
    for (const assignee of assignees) {
      await this.sendNotification(assignee, request);
    }
  }

  private async sendNotification(
    assignee: string,
    request: HITLRequest
  ): Promise<void> {
    try {
      // In production, integrate with actual notification systems
      this.logger.info("Sending HITL notification", {
        assignee,
        requestId: request.id,
        type: request.type,
        priority: request.priority,
      });

      // Mock notification - replace with actual implementation
      console.log(`📧 HITL Notification to ${assignee}: ${request.title}`);
    } catch (error) {
      this.logger.error("Failed to send HITL notification", {
        assignee,
        requestId: request.id,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  async respondToRequest(
    requestId: string,
    action: HITLResponse["action"],
    data?: any,
    feedback?: string,
    respondedBy: string = "system"
  ): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request) {
      throw new Error(`HITL request ${requestId} not found`);
    }

    if (request.status !== "pending" && request.status !== "in_progress") {
      throw new Error(
        `HITL request ${requestId} is not in a respondable state (current: ${request.status})`
      );
    }

    // Validate action
    const validActions = ["approve", "reject", "modify", "retry", "escalate"];
    if (!validActions.includes(action)) {
      throw new Error(
        `Invalid action: ${action}. Valid actions: ${validActions.join(", ")}`
      );
    }

    // Validate respondedBy
    if (!respondedBy?.trim()) {
      throw new Error("respondedBy cannot be empty");
    }

    const response: HITLResponse = {
      requestId,
      action,
      data,
      feedback: feedback?.trim(),
      respondedBy: respondedBy.trim(),
      respondedAt: new Date().toISOString(),
      confidence: this.calculateResponseConfidence(action, feedback),
    };

    // Log the response decision
    this.logger.info("HITL response recorded", {
      requestId,
      action,
      respondedBy,
      confidence: response.confidence,
      hasData: !!data,
      hasFeedback: !!feedback,
    });

    await this.completeRequest(requestId, response);
  }

  private async completeRequest(
    requestId: string,
    response: HITLResponse
  ): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request) {
      return;
    }

    // Clear timeout
    const timeoutId = this.timeouts.get(requestId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.timeouts.delete(requestId);
    }

    // Update request
    request.status = "completed";
    request.response = response;

    this.logger.info("HITL request completed", {
      requestId,
      action: response.action,
      respondedBy: response.respondedBy,
    });

    // Send response via APIX
    this.apxClient.send({
      id: this.generateId(),
      type: "hitl_response",
      timestamp: new Date().toISOString(),
      data: response,
    });

    // Clean up old requests
    this.cleanupOldRequests();
  }

  private handleTimeout(requestId: string): void {
    const request = this.requests.get(requestId);
    if (!request) {
      return;
    }

    request.status = "timeout";
    this.timeouts.delete(requestId);

    this.logger.warn("HITL request timed out", {
      requestId,
      type: request.type,
      timeout: request.timeout,
    });

    // Handle based on fallback behavior
    switch (this.config.fallbackBehavior) {
      case "allow":
        this.completeRequest(requestId, {
          requestId,
          action: "approve",
          respondedBy: "system_timeout",
          respondedAt: new Date().toISOString(),
          confidence: 0.1,
          feedback: "Auto-approved due to timeout",
        });
        break;
      case "deny":
        this.completeRequest(requestId, {
          requestId,
          action: "reject",
          respondedBy: "system_timeout",
          respondedAt: new Date().toISOString(),
          confidence: 0.1,
          feedback: "Auto-rejected due to timeout",
        });
        break;
      case "queue":
        // Keep in queue for manual review
        break;
    }
  }

  private calculateResponseConfidence(
    action: string,
    feedback?: string
  ): number {
    let confidence = 0.8; // Base confidence

    // Adjust based on action type
    switch (action) {
      case "approve":
      case "reject":
        confidence = 0.9;
        break;
      case "modify":
        confidence = 0.7;
        break;
      case "retry":
        confidence = 0.6;
        break;
      case "escalate":
        confidence = 0.5;
        break;
    }

    // Adjust based on feedback quality
    if (feedback && feedback.length > 50) {
      confidence += 0.1;
    }

    return Math.min(1.0, confidence);
  }

  checkRequiresHITL(context: Record<string, any>): HITLRule[] {
    if (!this.config.enabled) {
      return [];
    }

    return this.findApplicableRules(context);
  }

  private findApplicableRules(context: Record<string, any>): HITLRule[] {
    const applicableRules: HITLRule[] = [];

    for (const rule of this.rules.values()) {
      if (!rule.enabled) {
        continue;
      }

      const matches = rule.conditions.every((condition) =>
        this.evaluateCondition(condition, context)
      );

      if (matches) {
        applicableRules.push(rule);
      }
    }

    return applicableRules.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private evaluateCondition(
    condition: HITLCondition,
    context: Record<string, any>
  ): boolean {
    const value = this.getNestedValue(context, condition.field);
    let result = false;

    switch (condition.operator) {
      case "equals":
        result = value === condition.value;
        break;
      case "contains":
        result = String(value).includes(String(condition.value));
        break;
      case "greater_than":
        result = Number(value) > Number(condition.value);
        break;
      case "less_than":
        result = Number(value) < Number(condition.value);
        break;
      case "in":
        result =
          Array.isArray(condition.value) && condition.value.includes(value);
        break;
      case "regex":
        result = new RegExp(condition.value).test(String(value));
        break;
    }

    return condition.negate ? !result : result;
  }

  private getNestedValue(obj: Record<string, any>, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  private cleanupOldRequests(): void {
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

    for (const [id, request] of this.requests.entries()) {
      if (new Date(request.createdAt).getTime() < cutoff) {
        this.requests.delete(id);

        const timeoutId = this.timeouts.get(id);
        if (timeoutId) {
          clearTimeout(timeoutId);
          this.timeouts.delete(id);
        }
      }
    }
  }

  private generateId(): string {
    return `hitl_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  // Rule Management
  addRule(rule: HITLRule): void {
    this.rules.set(rule.id, rule);
    this.logger.info(`Added HITL rule: ${rule.name}`);
  }

  removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      this.logger.info(`Removed HITL rule: ${ruleId}`);
    }
    return removed;
  }

  updateRule(ruleId: string, updates: Partial<HITLRule>): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return false;
    }

    const updatedRule = { ...rule, ...updates };
    this.rules.set(ruleId, updatedRule);
    this.logger.info(`Updated HITL rule: ${ruleId}`);
    return true;
  }

  // Public API
  getRequests(status?: HITLRequest["status"]): HITLRequest[] {
    const requests = Array.from(this.requests.values());
    return status ? requests.filter((r) => r.status === status) : requests;
  }

  getRequest(id: string): HITLRequest | undefined {
    return this.requests.get(id);
  }

  getRules(): HITLRule[] {
    return Array.from(this.rules.values());
  }

  getRule(id: string): HITLRule | undefined {
    return this.rules.get(id);
  }

  getStats(): {
    totalRequests: number;
    pendingRequests: number;
    completedRequests: number;
    timeoutRequests: number;
    averageResponseTime: number;
    ruleUsage: Record<string, number>;
  } {
    const requests = Array.from(this.requests.values());
    const completed = requests.filter((r) => r.status === "completed");

    const avgResponseTime =
      completed.reduce((sum, r) => {
        if (r.response) {
          const responseTime =
            new Date(r.response.respondedAt).getTime() -
            new Date(r.createdAt).getTime();
          return sum + responseTime;
        }
        return sum;
      }, 0) / completed.length || 0;

    const ruleUsage: Record<string, number> = {};
    requests.forEach((r) => {
      const applicableRules = this.findApplicableRules(r.context);
      applicableRules.forEach((rule) => {
        ruleUsage[rule.id] = (ruleUsage[rule.id] || 0) + 1;
      });
    });

    return {
      totalRequests: requests.length,
      pendingRequests: requests.filter((r) => r.status === "pending").length,
      completedRequests: completed.length,
      timeoutRequests: requests.filter((r) => r.status === "timeout").length,
      averageResponseTime: avgResponseTime,
      ruleUsage,
    };
  }

  updateConfig(config: Partial<HITLConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.info("Updated HITL configuration", { config });
  }
}

// Global HITL controller instance
let hitlController: HITLController | null = null;

export function getHITLController(
  config?: Partial<HITLConfig>
): HITLController {
  if (!hitlController) {
    hitlController = new HITLController(config);
  }
  return hitlController;
}

export { HITLController };
