import { Suspense, useEffect, useState } from "react";
import { useRoutes, Routes, Route, Navigate } from "react-router-dom";
import Home from "./components/home";
import routes from "tempo-routes";
import { initializeSynapseAI } from "./lib/sdk";
import { useAuth } from "./lib/auth";
import { getThemeManager } from "./lib/theme";
import { getAPXClient } from "./lib/websocket";
import { getToolFramework } from "./lib/toolExecution";
import { getHITLController } from "./lib/hitl";
import { db } from "./lib/database";
import DashboardLayout from "./components/layout/DashboardLayout";
import AIProviderPanel from "./components/dashboard/AIProviderPanel";
import AgentControlPanel from "./components/dashboard/AgentControlPanel";
import AnalyticsPanel from "./components/dashboard/AnalyticsPanel";
import EventMonitor from "./components/core/EventMonitor";
import EventSubscription from "./components/core/EventSubscription";
import { But<PERSON> } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";
import LandingPage from "./components/pages/LandingPage";
import UserGuide from "./components/pages/UserGuide";
import Documentation from "./components/pages/Documentation";
import { ErrorBoundary } from "./lib/errorHandler";
import ThemeToggle from "./components/ui/theme-toggle";

function LoginForm() {
  const { login, isLoading, error } = useAuth();
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(credentials);
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  const handleGetStarted = () => {
    // Demo login with secure credentials
    const demoCredentials = {
      email: "<EMAIL>",
      password: "demo123",
    };
    login(demoCredentials).catch(console.error);
  };

  return (
    <div className="min-h-screen bg-background text-foreground flex items-center justify-center p-4 transition-colors duration-200">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center justify-center space-x-2 flex-1">
                <div className="bg-primary h-8 w-8 rounded-md flex items-center justify-center">
                  <span className="text-primary-foreground font-bold">S</span>
                </div>
                <span>SynapseAI</span>
              </div>
              <ThemeToggle variant="button" size="sm" />
            </div>
            <p className="text-sm text-muted-foreground font-normal">
              Universal AI Orchestration Platform
            </p>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={credentials.email}
                onChange={(e) =>
                  setCredentials((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter your email"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) =>
                  setCredentials((prev) => ({
                    ...prev,
                    password: e.target.value,
                  }))
                }
                placeholder="Enter your password"
                required
              />
            </div>
            {error && (
              <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                {error}
              </div>
            )}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Signing In..." : "Sign In"}
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleGetStarted}
              disabled={isLoading}
            >
              Get Started (Demo)
            </Button>
          </form>
          <div className="mt-4 text-xs text-muted-foreground text-center">
            Use "Get Started (Demo)" for demo access
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function Dashboard() {
  const [activeView, setActiveView] = useState("overview");

  const sidebarItems = [
    {
      id: "overview",
      label: "Overview",
      icon: "📊",
      isActive: activeView === "overview",
      onClick: () => setActiveView("overview"),
    },
    {
      id: "providers",
      label: "AI Providers",
      icon: "🤖",
      isActive: activeView === "providers",
      onClick: () => setActiveView("providers"),
      badge: "4",
    },
    {
      id: "agents",
      label: "Agents",
      icon: "🧠",
      isActive: activeView === "agents",
      onClick: () => setActiveView("agents"),
      badge: "3",
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: "📈",
      isActive: activeView === "analytics",
      onClick: () => setActiveView("analytics"),
    },
    {
      id: "events",
      label: "Events",
      icon: "🔄",
      isActive: activeView === "events",
      onClick: () => setActiveView("events"),
    },
    {
      id: "subscriptions",
      label: "Subscriptions",
      icon: "🔔",
      isActive: activeView === "subscriptions",
      onClick: () => setActiveView("subscriptions"),
    },
  ];

  const renderContent = () => {
    switch (activeView) {
      case "providers":
        return <AIProviderPanel />;
      case "agents":
        return <AgentControlPanel />;
      case "analytics":
        return <AnalyticsPanel />;
      case "events":
        return <EventMonitor />;
      case "subscriptions":
        return <EventSubscription />;
      default:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AIProviderPanel />
            <AgentControlPanel />
            <AnalyticsPanel />
            <EventMonitor />
          </div>
        );
    }
  };

  return (
    <DashboardLayout
      title="SynapseAI Dashboard"
      subtitle="Universal AI Orchestration Platform"
    >
      <div className="p-6">{renderContent()}</div>
    </DashboardLayout>
  );
}

function AppContent() {
  const { isAuthenticated } = useAuth();
  const [sdkInitialized, setSdkInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    const initSDK = async () => {
      try {
        console.log("🚀 Starting SynapseAI initialization...");

        // Initialize theme manager first
        getThemeManager();
        console.log("✅ Theme manager initialized");

        // Frontend connects to backend via API - no direct database connection
        console.log("🔧 Frontend initialized - connecting to real backend API");

        // Initialize SynapseAI SDK
        await initializeSynapseAI({
          environment: import.meta.env.PROD ? "production" : "development",
          enableLogging: !import.meta.env.PROD,
          enableRouting: true,
          enableEventSystem: true,
          enableDatabase: true,
          retryAttempts: 3,
          timeout: 30000,
        });
        console.log("✅ SynapseAI SDK initialized");

        // Initialize APIX WebSocket client
        const apxClient = getAPXClient({
          url: import.meta.env.VITE_APIX_URL || "wss://api.synapseai.com/apix",
          reconnectInterval: 5000,
          maxReconnectAttempts: 10,
          heartbeatInterval: 30000,
        });
        console.log("✅ APIX WebSocket client initialized");

        // Initialize Tool Execution Framework
        const toolFramework = getToolFramework();
        console.log("✅ Tool Execution Framework initialized");

        // Initialize HITL Controller
        const hitlController = getHITLController({
          enabled: true,
          defaultTimeout: 300000,
          maxConcurrentRequests: 50,
          fallbackBehavior: "queue",
        });
        console.log("✅ HITL Controller initialized");

        setSdkInitialized(true);
        console.log("🎉 SynapseAI initialization completed successfully!");
      } catch (error) {
        console.error("❌ Failed to initialize SynapseAI SDK:", error);
        setInitError(error instanceof Error ? error.message : "Unknown error");
      }
    };

    initSDK();
  }, []);

  if (initError) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center transition-colors duration-200">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Initialization Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">{initError}</p>
            <div className="flex gap-2">
              <Button
                className="flex-1"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
              <ThemeToggle variant="button" size="default" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!sdkInitialized) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center transition-colors duration-200">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing SynapseAI...</p>
          <div className="mt-4">
            <ThemeToggle variant="button" size="sm" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <Suspense fallback={<p>Loading...</p>}>
        <>
          <Routes>
            <Route
              path="/"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <LandingPage
                    onGetStarted={() => (window.location.href = "/login")}
                  />
                )
              }
            />
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <LoginForm />
                )
              }
            />
            <Route
              path="/dashboard"
              element={
                isAuthenticated ? (
                  <Dashboard />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/dashboard/*"
              element={
                isAuthenticated ? (
                  <Dashboard />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route path="/guide" element={<UserGuide />} />
            <Route path="/docs" element={<Documentation />} />
            <Route
              path="*"
              element={
                <Navigate to={isAuthenticated ? "/dashboard" : "/"} replace />
              }
            />
          </Routes>
          {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
        </>
      </Suspense>
    </ErrorBoundary>
  );
}

function App() {
  return <AppContent />;
}

export default App;
