import { QueryResult } from 'pg';
import { performance } from 'perf_hooks';

export interface QueryMetrics {
    sql: string;
    params?: any[];
    duration: number;
    rowCount: number;
    timestamp: Date;
    success: boolean;
    error?: string;
}

export interface ConnectionMetrics {
    total: number;
    idle: number;
    active: number;
    waiting: number;
    latency: number;
}

class DatabaseMonitor {
    private static instance: DatabaseMonitor;
    private metrics: QueryMetrics[] = [];
    private readonly maxMetricsLength = 1000;

    private constructor() { }

    public static getInstance(): DatabaseMonitor {
        if (!DatabaseMonitor.instance) {
            DatabaseMonitor.instance = new DatabaseMonitor();
        }
        return DatabaseMonitor.instance;
    }

    public recordQuery(
        sql: string,
        params: any[] | undefined,
        result: QueryResult | Error,
        duration: number
    ): void {
        const metrics: QueryMetrics = {
            sql,
            params,
            duration,
            rowCount: result instanceof Error ? 0 : result.rowCount,
            timestamp: new Date(),
            success: !(result instanceof Error),
            error: result instanceof Error ? result.message : undefined,
        };

        this.metrics.push(metrics);

        // Keep only the last maxMetricsLength metrics
        if (this.metrics.length > this.maxMetricsLength) {
            this.metrics = this.metrics.slice(-this.maxMetricsLength);
        }

        // Log in development
        if (process.env.NODE_ENV === 'development') {
            this.logQueryMetrics(metrics);
        }
    }

    private logQueryMetrics(metrics: QueryMetrics): void {
        const status = metrics.success ? '✅' : '❌';
        console.log(`
Database Query ${status}
SQL: ${metrics.sql}
Parameters: ${JSON.stringify(metrics.params)}
Duration: ${metrics.duration}ms
Rows: ${metrics.rowCount}
${metrics.error ? `Error: ${metrics.error}` : ''}
    `);
    }

    public getRecentMetrics(limit: number = 100): QueryMetrics[] {
        return this.metrics.slice(-limit);
    }

    public getAverageQueryDuration(): number {
        if (this.metrics.length === 0) return 0;
        const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
        return totalDuration / this.metrics.length;
    }

    public getSuccessRate(): number {
        if (this.metrics.length === 0) return 0;
        const successCount = this.metrics.filter(m => m.success).length;
        return (successCount / this.metrics.length) * 100;
    }

    public getErrorRate(): number {
        return 100 - this.getSuccessRate();
    }

    public async measureQueryLatency(): Promise<number> {
        const start = performance.now();
        try {
            // Simple query to measure latency
            await this.executeTestQuery();
            return performance.now() - start;
        } catch (error) {
            console.error('Failed to measure query latency:', error);
            return -1;
        }
    }

    private async executeTestQuery(): Promise<void> {
        // This would be implemented in the database client
        // For now, it's a placeholder
        await Promise.resolve();
    }

    public clearMetrics(): void {
        this.metrics = [];
    }

    public getMetricsSummary() {
        return {
            totalQueries: this.metrics.length,
            averageDuration: this.getAverageQueryDuration(),
            successRate: this.getSuccessRate(),
            errorRate: this.getErrorRate(),
            recentErrors: this.metrics
                .filter(m => !m.success)
                .slice(-5)
                .map(m => ({
                    sql: m.sql,
                    error: m.error,
                    timestamp: m.timestamp,
                })),
        };
    }
}

export const databaseMonitor = DatabaseMonitor.getInstance(); 