import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { MetricCard } from "@/components/ui/metric-card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
} from "recharts";
import {
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  Clock,
  Users,
  Zap,
  AlertTriangle,
  Download,
  RefreshCw,
} from "lucide-react";
import { getRealAPIClient } from "@/lib/realApiClient";

interface AnalyticsData {
  performance: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    totalCost: number;
    costTrend: number;
  };
  usage: {
    daily: Array<{
      date: string;
      requests: number;
      cost: number;
      latency: number;
    }>;
    providers: Array<{
      name: string;
      requests: number;
      cost: number;
      successRate: number;
      color: string;
    }>;
    models: Array<{
      name: string;
      usage: number;
      cost: number;
    }>;
  };
  errors: Array<{
    timestamp: string;
    provider: string;
    error: string;
    count: number;
  }>;
}

interface AnalyticsPanelProps {
  timeRange?: "24h" | "7d" | "30d" | "90d";
  onDataUpdate?: (data: AnalyticsData) => void;
}

const AnalyticsPanel: React.FC<AnalyticsPanelProps> = ({
  timeRange = "7d",
  onDataUpdate = () => {},
}) => {
  const [data, setData] = useState<AnalyticsData>({
    performance: {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalCost: 0,
      costTrend: 0,
    },
    usage: {
      daily: [],
      providers: [],
      models: [],
    },
    errors: [],
  });

  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeRange]);

  useEffect(() => {
    onDataUpdate(data);
  }, [data, onDataUpdate]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      const apiClient = getRealAPIClient();
      const [analyticsResponse, usageResponse] = await Promise.all([
        apiClient.getAnalytics(selectedTimeRange),
        apiClient.getUsageStats(selectedTimeRange),
      ]);

      if (analyticsResponse.success && analyticsResponse.data) {
        const analyticsData = analyticsResponse.data;
        const usageData = usageResponse.success ? usageResponse.data : null;

        setData({
          performance: {
            totalRequests: analyticsData.totalRequests || 0,
            successfulRequests: analyticsData.successfulRequests || 0,
            failedRequests: analyticsData.failedRequests || 0,
            averageResponseTime: analyticsData.averageResponseTime || 0,
            totalCost: analyticsData.totalCost || 0,
            costTrend: analyticsData.costTrend || 0,
          },
          usage: {
            daily: usageData?.daily || [],
            providers: usageData?.providers || [],
            models: usageData?.models || [],
          },
          errors: analyticsData.errors || [],
        });
      } else {
        // Handle case where API returns no data
        console.warn("No analytics data available");
        setData({
          performance: {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            totalCost: 0,
            costTrend: 0,
          },
          usage: {
            daily: [],
            providers: [],
            models: [],
          },
          errors: [],
        });
      }
    } catch (error) {
      console.error("Failed to load analytics data:", error);
      // Set empty data on error to prevent UI crashes
      setData({
        performance: {
          totalRequests: 0,
          successfulRequests: 0,
          failedRequests: 0,
          averageResponseTime: 0,
          totalCost: 0,
          costTrend: 0,
        },
        usage: {
          daily: [],
          providers: [],
          models: [],
        },
        errors: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshData = async () => {
    await loadAnalyticsData();
  };

  const handleExportData = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      timeRange: selectedTimeRange,
      data,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `synapseai-analytics-${selectedTimeRange}-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const successRate = (
    (data.performance.successfulRequests / data.performance.totalRequests) *
    100
  ).toFixed(1);

  const COLORS = ["#10B981", "#3B82F6", "#8B5CF6", "#F59E0B", "#EF4444"];

  return (
    <EnhancedCard className="w-full" gradient interactive>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Analytics Dashboard
        </CardTitle>
        <div className="flex items-center space-x-2">
          <Select
            value={selectedTimeRange}
            onValueChange={(value: any) => setSelectedTimeRange(value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshData}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportData}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <MetricCard
            title="Total Requests"
            value={data.performance.totalRequests.toLocaleString()}
            icon={<Zap className="h-4 w-4" />}
            status="success"
            trend="up"
            change={{
              value: 12.5,
              period: "last period",
            }}
          />
          <MetricCard
            title="Success Rate"
            value={`${successRate}%`}
            icon={<Activity className="h-4 w-4" />}
            status="success"
            trend="up"
            change={{
              value: 0.8,
              period: "last period",
            }}
          />
          <MetricCard
            title="Avg Response Time"
            value={`${data.performance.averageResponseTime}ms`}
            icon={<Clock className="h-4 w-4" />}
            status="warning"
            trend="down"
            change={{
              value: -5.2,
              period: "last period",
            }}
          />
          <MetricCard
            title="Total Cost"
            value={`${data.performance.totalCost.toFixed(2)}`}
            icon={<DollarSign className="h-4 w-4" />}
            status="info"
            trend="up"
            change={{
              value: data.performance.costTrend,
              period: "last period",
            }}
          />
        </div>

        <Tabs defaultValue="usage" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="usage">Usage Trends</TabsTrigger>
            <TabsTrigger value="providers">Provider Performance</TabsTrigger>
            <TabsTrigger value="costs">Cost Analysis</TabsTrigger>
            <TabsTrigger value="errors">Error Monitoring</TabsTrigger>
          </TabsList>

          <TabsContent value="usage" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Daily Requests</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={data.usage.daily}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) =>
                          new Date(value).toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                          })
                        }
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(value) =>
                          new Date(value).toLocaleDateString()
                        }
                      />
                      <Area
                        type="monotone"
                        dataKey="requests"
                        stroke="#3B82F6"
                        fill="#3B82F6"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Response Time Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data.usage.daily}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) =>
                          new Date(value).toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                          })
                        }
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(value) =>
                          new Date(value).toLocaleDateString()
                        }
                        formatter={(value) => [`${value}ms`, "Latency"]}
                      />
                      <Line
                        type="monotone"
                        dataKey="latency"
                        stroke="#10B981"
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="providers" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Provider Usage Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={data.usage.providers}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="requests"
                      >
                        {data.usage.providers.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.color || COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Success Rate Comparison
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.usage.providers}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis domain={[90, 100]} />
                      <Tooltip
                        formatter={(value) => [`${value}%`, "Success Rate"]}
                      />
                      <Bar dataKey="successRate" fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Cost Efficiency Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart
                      data={data.usage.providers.map((p) => ({
                        ...p,
                        costPerRequest: p.cost / p.requests,
                      }))}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) => [
                          `${Number(value).toFixed(4)}`,
                          "Cost per Request",
                        ]}
                      />
                      <Bar dataKey="costPerRequest" fill="#F59E0B" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Provider Performance Matrix
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.usage.providers.map((provider, index) => (
                      <div
                        key={provider.name}
                        className="space-y-3 p-3 border rounded"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: provider.color }}
                            />
                            <span className="font-medium">{provider.name}</span>
                          </div>
                          <Badge variant="outline">Rank #{index + 1}</Badge>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">
                              Requests:
                            </span>
                            <span className="ml-2 font-medium">
                              {provider.requests.toLocaleString()}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              Success Rate:
                            </span>
                            <span className="ml-2 font-medium text-green-600">
                              {provider.successRate}%
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              Total Cost:
                            </span>
                            <span className="ml-2 font-medium">
                              ${provider.cost.toFixed(2)}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              Cost/Request:
                            </span>
                            <span className="ml-2 font-medium">
                              ${(provider.cost / provider.requests).toFixed(4)}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span>Performance Score</span>
                            <span>
                              {(
                                (provider.successRate / 100) * 0.6 +
                                (1 - provider.cost / provider.requests / 0.01) *
                                  0.4
                              ).toFixed(2)}
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full"
                              style={{
                                width: `${((provider.successRate / 100) * 0.6 + (1 - provider.cost / provider.requests / 0.01) * 0.4) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="costs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">
                  Daily Cost Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={data.usage.daily}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                      formatter={(value) => [`$${value}`, "Cost"]}
                    />
                    <Bar dataKey="cost" fill="#10B981" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="errors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  Recent Errors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.errors.length > 0 ? (
                    data.errors.map((error, index) => (
                      <div
                        key={index}
                        className="p-3 rounded-md border bg-red-50 border-red-200"
                      >
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <Badge variant="destructive">
                              {error.provider}
                            </Badge>
                            <span className="font-medium text-sm">
                              {error.error}
                            </span>
                          </div>
                          <Badge variant="outline">
                            {error.count} occurrences
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(error.timestamp).toLocaleString()}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">No recent errors</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </EnhancedCard>
  );
};

export default AnalyticsPanel;
