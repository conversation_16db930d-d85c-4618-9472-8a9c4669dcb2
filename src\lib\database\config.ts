import { z } from "zod";

// Database configuration schema
const databaseConfigSchema = z.object({
  url: z.string().url(),
  poolSize: z.number().int().min(1).max(100).default(10),
  idleTimeout: z.number().int().min(1000).max(300000).default(30000),
  connectionTimeout: z.number().int().min(1000).max(30000).default(2000),
  statementTimeout: z.number().int().min(1000).max(300000).default(30000),
  queryTimeout: z.number().int().min(1000).max(300000).default(30000),
  ssl: z.boolean().default(false),
  applicationName: z.string().default("synapseai"),
});

export type DatabaseConfig = z.infer<typeof databaseConfigSchema>;

// Environment variable schema
const envSchema = z.object({
  DATABASE_URL: z.string(),
  NODE_ENV: z
    .enum(["development", "test", "production"])
    .default("development"),
  DB_POOL_SIZE: z.string().transform(Number).optional(),
  DB_IDLE_TIMEOUT: z.string().transform(Number).optional(),
  DB_CONNECTION_TIMEOUT: z.string().transform(Number).optional(),
  DB_STATEMENT_TIMEOUT: z.string().transform(Number).optional(),
  DB_QUERY_TIMEOUT: z.string().transform(Number).optional(),
  DB_SSL: z
    .enum(["true", "false"])
    .transform((val) => val === "true")
    .optional(),
});

export class DatabaseConfigurationError extends Error {
  constructor(
    message: string,
    public readonly validationErrors?: z.ZodError
  ) {
    super(message);
    this.name = "DatabaseConfigurationError";
  }
}

export function getDatabaseConfig(): DatabaseConfig {
  try {
    // Skip in browser environment
    if (typeof window !== "undefined") {
      throw new Error("Database config not available in browser");
    }

    // Validate environment variables
    const env = envSchema.parse(process.env);

    // Create config object
    const config = databaseConfigSchema.parse({
      url: env.DATABASE_URL,
      poolSize: env.DB_POOL_SIZE,
      idleTimeout: env.DB_IDLE_TIMEOUT,
      connectionTimeout: env.DB_CONNECTION_TIMEOUT,
      statementTimeout: env.DB_STATEMENT_TIMEOUT,
      queryTimeout: env.DB_QUERY_TIMEOUT,
      ssl: env.DB_SSL || env.NODE_ENV === "production",
      applicationName: "synapseai",
    });

    return config;
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new DatabaseConfigurationError(
        "Invalid database configuration",
        error
      );
    }
    throw error;
  }
}

export function createPoolConfig(config: DatabaseConfig): any {
  const { url, ...poolConfig } = config;

  return {
    connectionString: url,
    max: poolConfig.poolSize,
    idleTimeoutMillis: poolConfig.idleTimeout,
    connectionTimeoutMillis: poolConfig.connectionTimeout,
    application_name: poolConfig.applicationName,
    statement_timeout: poolConfig.statementTimeout,
    query_timeout: poolConfig.queryTimeout,
    ssl: poolConfig.ssl ? { rejectUnauthorized: false } : undefined,
  };
}
