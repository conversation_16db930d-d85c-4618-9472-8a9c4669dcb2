import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Book,
  Code,
  Settings,
  Zap,
  Brain,
  Activity,
  Users,
  Shield,
  Database,
  Globe,
  ChevronDown,
  ChevronRight,
  Copy,
  ExternalLink,
  Download,
  Play,
  CheckCircle,
  AlertTriangle,
  Info,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface DocumentationProps {
  className?: string;
}

const Documentation: React.FC<DocumentationProps> = ({ className }) => {
  const [activeSection, setActiveSection] = useState("overview");
  const [expandedSections, setExpandedSections] = useState<string[]>([
    "overview",
  ]);

  const toggleSection = (section: string) => {
    setExpandedSections((prev) =>
      prev.includes(section)
        ? prev.filter((s) => s !== section)
        : [...prev, section]
    );
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const navigationItems = [
    {
      id: "overview",
      label: "Project Overview",
      icon: <Book className="h-4 w-4" />,
    },
    {
      id: "architecture",
      label: "Architecture",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      id: "modules",
      label: "Core Modules",
      icon: <Code className="h-4 w-4" />,
    },
    {
      id: "features",
      label: "Features Guide",
      icon: <Zap className="h-4 w-4" />,
    },
    {
      id: "integration",
      label: "Integration",
      icon: <Globe className="h-4 w-4" />,
    },
    {
      id: "configuration",
      label: "Configuration",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      id: "techstack",
      label: "Tech Stack",
      icon: <Database className="h-4 w-4" />,
    },
    { id: "usecases", label: "Use Cases", icon: <Users className="h-4 w-4" /> },
  ];

  const coreModules = [
    {
      name: "AI Provider Management",
      description:
        "Intelligent routing and management of multiple AI providers",
      features: [
        "Multi-provider support",
        "Smart routing",
        "Fallback handling",
        "Cost optimization",
      ],
      icon: <Brain className="h-5 w-5" />,
      color: "bg-blue-500",
    },
    {
      name: "Agent Control System",
      description:
        "Lifecycle management for AI agents with deployment controls",
      features: [
        "Agent creation",
        "Version management",
        "Deployment pipeline",
        "Performance monitoring",
      ],
      icon: <Zap className="h-5 w-5" />,
      color: "bg-green-500",
    },
    {
      name: "Real-Time Event System",
      description:
        "Event-driven architecture with WebSocket-based APIX protocol",
      features: [
        "Real-time streaming",
        "Event pub/sub",
        "Session management",
        "Auto-reconnection",
      ],
      icon: <Activity className="h-5 w-5" />,
      color: "bg-purple-500",
    },
    {
      name: "Analytics & Monitoring",
      description: "Comprehensive analytics and performance monitoring",
      features: [
        "Usage analytics",
        "Cost tracking",
        "Performance metrics",
        "Error monitoring",
      ],
      icon: <Activity className="h-5 w-5" />,
      color: "bg-orange-500",
    },
    {
      name: "Security & RBAC",
      description: "Multi-tenant security with role-based access control",
      features: [
        "JWT authentication",
        "Role management",
        "Tenant isolation",
        "Audit logging",
      ],
      icon: <Shield className="h-5 w-5" />,
      color: "bg-red-500",
    },
    {
      name: "Tool Execution Framework",
      description:
        "Declarative tool definitions with orchestration capabilities",
      features: [
        "Tool registration",
        "Execution pipeline",
        "Fallback logic",
        "External API integration",
      ],
      icon: <Settings className="h-5 w-5" />,
      color: "bg-indigo-500",
    },
  ];

  const useCases = [
    {
      title: "Enterprise Customer Support",
      description:
        "Deploy intelligent chatbots across multiple channels with seamless escalation to human agents.",
      benefits: [
        "24/7 availability",
        "Multi-language support",
        "Intelligent routing",
        "Human handoff",
      ],
      implementation:
        "Configure multiple AI providers for redundancy, set up HITL controls for complex queries, and use analytics to optimize response quality.",
    },
    {
      title: "Content Generation Platform",
      description:
        "Build scalable content creation workflows with multiple AI models for different content types.",
      benefits: [
        "Multi-model optimization",
        "Cost efficiency",
        "Quality consistency",
        "Scalable workflows",
      ],
      implementation:
        "Route blog posts to GPT-4, social media to Claude, and technical docs to specialized models based on content type and quality requirements.",
    },
    {
      title: "Developer Tool Integration",
      description:
        "Embed AI capabilities into existing applications with minimal code changes.",
      benefits: [
        "Easy integration",
        "Type-safe SDK",
        "Real-time updates",
        "Flexible deployment",
      ],
      implementation:
        "Use the TypeScript SDK to add AI features, subscribe to real-time events, and leverage the widget system for quick UI integration.",
    },
    {
      title: "Multi-Tenant SaaS Platform",
      description:
        "Provide AI services to multiple clients with isolated environments and custom configurations.",
      benefits: [
        "Tenant isolation",
        "Custom branding",
        "Usage tracking",
        "Flexible pricing",
      ],
      implementation:
        "Configure tenant-specific AI providers, set up RBAC for different user roles, and use analytics for usage-based billing.",
    },
  ];

  const techStack = [
    {
      category: "Frontend",
      technologies: [
        {
          name: "React 18",
          reason: "Modern UI with concurrent features and improved performance",
        },
        {
          name: "TypeScript",
          reason: "Type safety and better developer experience",
        },
        {
          name: "Tailwind CSS",
          reason: "Utility-first styling with consistent design system",
        },
        {
          name: "Vite",
          reason: "Fast development server and optimized builds",
        },
        { name: "Radix UI", reason: "Accessible, unstyled UI primitives" },
      ],
    },
    {
      category: "State Management",
      technologies: [
        {
          name: "React Context",
          reason: "Built-in state management for auth and global state",
        },
        {
          name: "Custom Hooks",
          reason: "Reusable logic and clean component separation",
        },
      ],
    },
    {
      category: "Real-Time Communication",
      technologies: [
        {
          name: "WebSocket (APIX)",
          reason: "Custom protocol for AI-specific real-time events",
        },
        {
          name: "Event-Driven Architecture",
          reason: "Scalable pub/sub system for cross-component communication",
        },
      ],
    },
    {
      category: "Security",
      technologies: [
        {
          name: "JWT",
          reason: "Stateless authentication with role-based claims",
        },
        {
          name: "RBAC",
          reason: "Fine-grained access control for multi-tenant environments",
        },
        {
          name: "Crypto API",
          reason: "Client-side encryption and secure token handling",
        },
      ],
    },
    {
      category: "Development Tools",
      technologies: [
        {
          name: "ESLint + Prettier",
          reason: "Code quality and consistent formatting",
        },
        { name: "Vitest", reason: "Fast unit testing with Vite integration" },
        { name: "Husky", reason: "Git hooks for pre-commit quality checks" },
      ],
    },
  ];

  return (
    <div className={cn("min-h-screen bg-gradient-surface", className)}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent mb-4">
            SynapseAI Documentation
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Universal AI Orchestration Platform - Complete Developer Guide
          </p>
          <div className="flex justify-center gap-4 mt-6">
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              API Reference
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="text-lg">Navigation</CardTitle>
              </CardHeader>
              <CardContent>
                <nav className="space-y-2">
                  {navigationItems.map((item) => (
                    <Button
                      key={item.id}
                      variant={activeSection === item.id ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => setActiveSection(item.id)}
                    >
                      {item.icon}
                      <span className="ml-2">{item.label}</span>
                    </Button>
                  ))}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs value={activeSection} onValueChange={setActiveSection}>
              {/* Project Overview */}
              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Book className="h-5 w-5" />
                      Project Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-3">
                        What is SynapseAI?
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        SynapseAI is a unified platform that intelligently
                        orchestrates multiple AI providers through a
                        transparent, click-based interface while enabling
                        cross-application workflows via a TypeScript SDK built
                        on the Universal AI-UI Protocol.
                      </p>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Key Capabilities
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Brain className="h-5 w-5 text-blue-500" />
                            <h4 className="font-medium">
                              Multi-Provider Intelligence
                            </h4>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Smart routing between OpenAI, Claude, Gemini, and
                            others based on context and performance
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Settings className="h-5 w-5 text-green-500" />
                            <h4 className="font-medium">
                              Click-Based Admin Panel
                            </h4>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Manage AI providers, monitor usage, and control
                            agent lifecycle through intuitive UI
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Activity className="h-5 w-5 text-purple-500" />
                            <h4 className="font-medium">
                              Real-Time Event System
                            </h4>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Event-driven architecture with pub/sub model for
                            cross-app synchronization
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Code className="h-5 w-5 text-orange-500" />
                            <h4 className="font-medium">
                              Developer-Friendly SDK
                            </h4>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Fully typed TypeScript SDK with adapters for easy
                            third-party integration
                          </p>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Core Features
                      </h3>
                      <div className="space-y-3">
                        {[
                          "Session-Aware Memory Engine – Real-time Redis-backed agent state with TTL, continuity, and replay support",
                          "Tool Execution Framework – Declarative tool definitions, internal/external API orchestration, fallback logic",
                          "Human-in-the-Loop (HITL) – Toggleable HITL control with override, retry, or clarification prompts",
                          "Widget Integration Layer – Embed-ready script generator with style configuration, domain restriction, and live preview",
                          "Secure Multi-Tenant RBAC – JWT authentication with scoped roles per tenant/project/app",
                          "Zero-Downtime Event Bus – Stateless WebSocket APIX protocol with reconnect and auto-resume",
                        ].map((feature, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <p className="text-sm">{feature}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Architecture */}
              <TabsContent value="architecture" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      System Architecture
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Architectural Flow
                      </h3>
                      <div className="bg-muted/50 p-6 rounded-lg">
                        <div className="space-y-4">
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              1
                            </div>
                            <div>
                              <h4 className="font-medium">
                                User Authentication
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                JWT-based auth with RBAC and tenant isolation
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              2
                            </div>
                            <div>
                              <h4 className="font-medium">
                                Request Processing
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                Context analysis and intelligent provider
                                routing
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              3
                            </div>
                            <div>
                              <h4 className="font-medium">AI Orchestration</h4>
                              <p className="text-sm text-muted-foreground">
                                Multi-provider execution with fallback handling
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              4
                            </div>
                            <div>
                              <h4 className="font-medium">Real-Time Events</h4>
                              <p className="text-sm text-muted-foreground">
                                WebSocket-based APIX protocol for live updates
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              5
                            </div>
                            <div>
                              <h4 className="font-medium">Response Delivery</h4>
                              <p className="text-sm text-muted-foreground">
                                Processed results with analytics and monitoring
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Component Architecture
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border rounded-lg">
                          <h4 className="font-medium mb-2">Frontend Layer</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• React 18 with TypeScript</li>
                            <li>• Tailwind CSS + Radix UI</li>
                            <li>• Real-time WebSocket client</li>
                            <li>• Context-based state management</li>
                          </ul>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="font-medium mb-2">SDK Layer</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• TypeScript SDK with full typing</li>
                            <li>• Provider adapters</li>
                            <li>• Event subscription system</li>
                            <li>• Widget integration tools</li>
                          </ul>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="font-medium mb-2">Core Services</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• AI Provider Management</li>
                            <li>• Agent Control System</li>
                            <li>• Tool Execution Framework</li>
                            <li>• HITL Control System</li>
                          </ul>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="font-medium mb-2">Infrastructure</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• WebSocket APIX protocol</li>
                            <li>• Redis session storage</li>
                            <li>• Multi-tenant security</li>
                            <li>• Analytics & monitoring</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Core Modules */}
              <TabsContent value="modules" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      Core Modules
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {coreModules.map((module, index) => (
                        <Collapsible key={index}>
                          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 border rounded-lg hover:bg-muted/50">
                            <div className="flex items-center gap-3">
                              <div
                                className={cn(
                                  "w-10 h-10 rounded-lg flex items-center justify-center text-white",
                                  module.color
                                )}
                              >
                                {module.icon}
                              </div>
                              <div className="text-left">
                                <h3 className="font-semibold">{module.name}</h3>
                                <p className="text-sm text-muted-foreground">
                                  {module.description}
                                </p>
                              </div>
                            </div>
                            <ChevronDown className="h-4 w-4" />
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-4 p-4 border rounded-lg bg-muted/20">
                            <h4 className="font-medium mb-3">Key Features:</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {module.features.map((feature, featureIndex) => (
                                <div
                                  key={featureIndex}
                                  className="flex items-center gap-2"
                                >
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                  <span className="text-sm">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Features Guide */}
              <TabsContent value="features" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Features Guide
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* AI Provider Management */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold">
                        AI Provider Management
                      </h3>
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Setup Guide:</h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm">
                          <li>
                            Navigate to the AI Providers panel in the dashboard
                          </li>
                          <li>
                            Click "Add Provider" and select your AI service
                            (OpenAI, Claude, Gemini, etc.)
                          </li>
                          <li>
                            Configure API credentials and endpoint settings
                          </li>
                          <li>Set routing rules and fallback priorities</li>
                          <li>Test the connection and save configuration</li>
                        </ol>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div className="flex items-start gap-2">
                          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-blue-900">
                              Pro Tip
                            </h4>
                            <p className="text-sm text-blue-800">
                              Configure multiple providers for the same model
                              type to enable automatic failover and load
                              balancing.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Agent Control */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold">
                        Agent Control System
                      </h3>
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Creating an Agent:</h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm">
                          <li>Go to the Agent Control panel</li>
                          <li>
                            Click "Create Agent" and choose agent type (chatbot,
                            assistant, analyzer, etc.)
                          </li>
                          <li>
                            Define the system prompt and behavior parameters
                          </li>
                          <li>
                            Configure model settings (temperature, max tokens,
                            etc.)
                          </li>
                          <li>
                            Deploy to development, staging, or production
                            environment
                          </li>
                        </ol>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                        <div className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-green-900">
                              Best Practice
                            </h4>
                            <p className="text-sm text-green-800">
                              Always test agents in development environment
                              before deploying to production. Use version
                              management for safe rollbacks.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Real-Time Events */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold">
                        Real-Time Event System
                      </h3>
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Event Types:</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <h5 className="font-medium">System Events:</h5>
                            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                              <li>tool_call_start</li>
                              <li>thinking_status</li>
                              <li>text_chunk</li>
                              <li>agent_update</li>
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-medium">Management Events:</h5>
                            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                              <li>session_sync</li>
                              <li>error</li>
                              <li>heartbeat</li>
                              <li>connection</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* HITL Control */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold">
                        Human-in-the-Loop (HITL)
                      </h3>
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">
                          Configuration Options:
                        </h4>
                        <div className="space-y-3 text-sm">
                          <div className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                            <div>
                              <strong>Automatic Escalation:</strong> Set
                              confidence thresholds for automatic human handoff
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                              <strong>Manual Override:</strong> Allow operators
                              to intervene in any conversation
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                            <div>
                              <strong>Clarification Prompts:</strong> Request
                              additional context when needed
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Integration */}
              <TabsContent value="integration" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      Integration Guide
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        TypeScript SDK Integration
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Installation</h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard("npm install @synapseai/sdk")
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <code>npm install @synapseai/sdk</code>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Basic Setup</h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`import { SynapseAI } from '@synapseai/sdk';

const synapse = new SynapseAI({
  apiKey: 'your-api-key',
  environment: 'production',
  enableLogging: true
});

// Initialize connection
await synapse.initialize();`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`import { SynapseAI } from '@synapseai/sdk';

const synapse = new SynapseAI({
  apiKey: 'your-api-key',
  environment: 'production',
  enableLogging: true
});

// Initialize connection
await synapse.initialize();`}
                            </pre>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">
                            Making AI Requests
                          </h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`// Simple chat completion
const response = await synapse.chat.complete({
  messages: [
    { role: 'user', content: 'Hello, how can you help me?' }
  ],
  model: 'gpt-4', // Optional: let SynapseAI choose optimal provider
  temperature: 0.7
});

console.log(response.content);`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`// Simple chat completion
const response = await synapse.chat.complete({
  messages: [
    { role: 'user', content: 'Hello, how can you help me?' }
  ],
  model: 'gpt-4', // Optional: let SynapseAI choose optimal provider
  temperature: 0.7
});

console.log(response.content);`}
                            </pre>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">
                            Real-Time Event Subscription
                          </h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`// Subscribe to real-time events
synapse.events.subscribe('text_chunk', (event) => {
  console.log('Received text chunk:', event.data.content);
  // Update UI with streaming text
});

synapse.events.subscribe('tool_call_start', (event) => {
  console.log('Tool execution started:', event.data.toolName);
  // Show loading indicator
});`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`// Subscribe to real-time events
synapse.events.subscribe('text_chunk', (event) => {
  console.log('Received text chunk:', event.data.content);
  // Update UI with streaming text
});

synapse.events.subscribe('tool_call_start', (event) => {
  console.log('Tool execution started:', event.data.toolName);
  // Show loading indicator
});`}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Widget Integration
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">
                            Embed Widget Script
                          </h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`<script src="https://cdn.synapseai.com/widget.js"></script>
<script>
  SynapseWidget.init({
    apiKey: 'your-widget-key',
    containerId: 'synapse-chat',
    theme: 'light',
    position: 'bottom-right'
  });
</script>`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`<script src="https://cdn.synapseai.com/widget.js"></script>
<script>
  SynapseWidget.init({
    apiKey: 'your-widget-key',
    containerId: 'synapse-chat',
    theme: 'light',
    position: 'bottom-right'
  });
</script>`}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        REST API Integration
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Authentication</h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`curl -X POST https://api.synapseai.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`curl -X POST https://api.synapseai.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'`}
                            </pre>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Chat Completion</h4>
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                              onClick={() =>
                                copyToClipboard(`curl -X POST https://api.synapseai.com/v1/chat/completions \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "model": "gpt-4",
    "temperature": 0.7
  }'`)
                              }
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <pre className="text-sm">
                              {`curl -X POST https://api.synapseai.com/v1/chat/completions \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "model": "gpt-4",
    "temperature": 0.7
  }'`}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Configuration */}
              <TabsContent value="configuration" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Configuration Guide
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Environment Variables
                      </h3>
                      <div className="bg-gray-900 text-gray-100 p-4 rounded-lg relative">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
                          onClick={() =>
                            copyToClipboard(`# Core Configuration
VITE_APP_ENV=production
VITE_API_BASE_URL=https://api.synapseai.com
VITE_WEBSOCKET_URL=wss://api.synapseai.com/apix

# Authentication
VITE_JWT_SECRET=your-super-secret-jwt-key
VITE_JWT_EXPIRES_IN=15m
VITE_JWT_REFRESH_EXPIRES_IN=7d

# Features
VITE_FEATURE_ROUTING=true
VITE_FEATURE_EVENT_SYSTEM=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_MONITORING=true

# Security
VITE_MULTI_TENANT_ENABLED=true
VITE_RBAC_ENABLED=true

# Database (Optional)
DATABASE_URL=postgresql://user:password@localhost:5432/synapseai
DATABASE_POOL_SIZE=20`)
                          }
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <pre className="text-sm">
                          {`# Core Configuration
VITE_APP_ENV=production
VITE_API_BASE_URL=https://api.synapseai.com
VITE_WEBSOCKET_URL=wss://api.synapseai.com/apix

# Authentication
VITE_JWT_SECRET=your-super-secret-jwt-key
VITE_JWT_EXPIRES_IN=15m
VITE_JWT_REFRESH_EXPIRES_IN=7d

# Features
VITE_FEATURE_ROUTING=true
VITE_FEATURE_EVENT_SYSTEM=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_MONITORING=true

# Security
VITE_MULTI_TENANT_ENABLED=true
VITE_RBAC_ENABLED=true

# Database (Optional)
DATABASE_URL=postgresql://user:password@localhost:5432/synapseai
DATABASE_POOL_SIZE=20`}
                        </pre>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Provider Configuration
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">
                            OpenAI Configuration
                          </h4>
                          <div className="bg-muted/50 p-4 rounded-lg">
                            <div className="space-y-2 text-sm">
                              <div>
                                <strong>API Key:</strong> Your OpenAI API key
                              </div>
                              <div>
                                <strong>Base URL:</strong>{" "}
                                https://api.openai.com/v1 (default)
                              </div>
                              <div>
                                <strong>Models:</strong> gpt-4, gpt-3.5-turbo,
                                gpt-4-turbo
                              </div>
                              <div>
                                <strong>Rate Limits:</strong> Configure based on
                                your OpenAI tier
                              </div>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">
                            Claude Configuration
                          </h4>
                          <div className="bg-muted/50 p-4 rounded-lg">
                            <div className="space-y-2 text-sm">
                              <div>
                                <strong>API Key:</strong> Your Anthropic API key
                              </div>
                              <div>
                                <strong>Base URL:</strong>{" "}
                                https://api.anthropic.com (default)
                              </div>
                              <div>
                                <strong>Models:</strong> claude-3-opus,
                                claude-3-sonnet, claude-3-haiku
                              </div>
                              <div>
                                <strong>Version:</strong> 2023-06-01 (API
                                version)
                              </div>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">
                            Gemini Configuration
                          </h4>
                          <div className="bg-muted/50 p-4 rounded-lg">
                            <div className="space-y-2 text-sm">
                              <div>
                                <strong>API Key:</strong> Your Google AI API key
                              </div>
                              <div>
                                <strong>Base URL:</strong>{" "}
                                https://generativelanguage.googleapis.com
                                (default)
                              </div>
                              <div>
                                <strong>Models:</strong> gemini-pro,
                                gemini-pro-vision
                              </div>
                              <div>
                                <strong>Safety Settings:</strong> Configure
                                content filtering
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-xl font-semibold mb-4">
                        Deployment Configuration
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                            <div>
                              <h4 className="font-medium text-yellow-900">
                                Production Checklist
                              </h4>
                              <ul className="text-sm text-yellow-800 mt-2 space-y-1">
                                <li>• Set strong JWT secrets</li>
                                <li>• Configure HTTPS endpoints</li>
                                <li>• Enable rate limiting</li>
                                <li>• Set up monitoring and logging</li>
                                <li>• Configure CORS origins</li>
                                <li>• Enable database connection pooling</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tech Stack */}
              <TabsContent value="techstack" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      Technology Stack
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {techStack.map((category, index) => (
                      <div key={index}>
                        <h3 className="text-xl font-semibold mb-4">
                          {category.category}
                        </h3>
                        <div className="space-y-3">
                          {category.technologies.map((tech, techIndex) => (
                            <div
                              key={techIndex}
                              className="p-4 border rounded-lg"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium">{tech.name}</h4>
                                <Badge variant="outline">Core</Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {tech.reason}
                              </p>
                            </div>
                          ))}
                        </div>
                        {index < techStack.length - 1 && (
                          <Separator className="mt-6" />
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Use Cases */}
              <TabsContent value="usecases" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Real-World Use Cases
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {useCases.map((useCase, index) => (
                      <div key={index} className="space-y-4">
                        <div>
                          <h3 className="text-xl font-semibold mb-2">
                            {useCase.title}
                          </h3>
                          <p className="text-muted-foreground">
                            {useCase.description}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Key Benefits:</h4>
                          <div className="grid grid-cols-2 gap-2">
                            {useCase.benefits.map((benefit, benefitIndex) => (
                              <div
                                key={benefitIndex}
                                className="flex items-center gap-2"
                              >
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="text-sm">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">
                            Implementation Approach:
                          </h4>
                          <div className="bg-muted/50 p-4 rounded-lg">
                            <p className="text-sm">{useCase.implementation}</p>
                          </div>
                        </div>

                        {index < useCases.length - 1 && (
                          <Separator className="mt-6" />
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Documentation;
