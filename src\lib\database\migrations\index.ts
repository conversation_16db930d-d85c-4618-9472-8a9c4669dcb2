import { db } from '../index';
import { promises as fs } from 'fs';
import path from 'path';

interface Migration {
    id: string;
    name: string;
    timestamp: Date;
    applied: boolean;
}

export class MigrationManager {
    private static instance: MigrationManager;
    private migrationsDir: string;
    private migrations: Migration[] = [];

    private constructor() {
        this.migrationsDir = path.join(__dirname, 'scripts');
    }

    public static getInstance(): MigrationManager {
        if (!MigrationManager.instance) {
            MigrationManager.instance = new MigrationManager();
        }
        return MigrationManager.instance;
    }

    private async ensureMigrationTable(): Promise<void> {
        await db.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        timestamp TIMESTAMP NOT NULL,
        applied BOOLEAN DEFAULT false
      );
    `);
    }

    private async loadMigrations(): Promise<void> {
        try {
            const files = await fs.readdir(this.migrationsDir);
            const sqlFiles = files.filter(f => f.endsWith('.sql'));

            for (const file of sqlFiles) {
                const [timestamp, ...nameParts] = file.split('_');
                const name = nameParts.join('_').replace('.sql', '');

                this.migrations.push({
                    id: file,
                    name,
                    timestamp: new Date(parseInt(timestamp)),
                    applied: false
                });
            }

            // Sort migrations by timestamp
            this.migrations.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        } catch (error) {
            console.error('Error loading migrations:', error);
            throw error;
        }
    }

    private async getAppliedMigrations(): Promise<string[]> {
        const result = await db.query<{ id: string }>('SELECT id FROM migrations WHERE applied = true');
        return result.rows.map(row => row.id);
    }

    public async migrate(): Promise<void> {
        try {
            await this.ensureMigrationTable();
            await this.loadMigrations();

            const appliedMigrations = await this.getAppliedMigrations();
            const pendingMigrations = this.migrations.filter(m => !appliedMigrations.includes(m.id));

            for (const migration of pendingMigrations) {
                await db.transaction(async (client) => {
                    console.log(`Applying migration: ${migration.name}`);

                    const sqlPath = path.join(this.migrationsDir, migration.id);
                    const sql = await fs.readFile(sqlPath, 'utf-8');

                    await client.query(sql);
                    await client.query(
                        'INSERT INTO migrations (id, name, timestamp, applied) VALUES ($1, $2, $3, true)',
                        [migration.id, migration.name, migration.timestamp]
                    );

                    console.log(`✅ Migration applied: ${migration.name}`);
                });
            }
        } catch (error) {
            console.error('Migration failed:', error);
            throw error;
        }
    }

    public async rollback(steps: number = 1): Promise<void> {
        try {
            const appliedMigrations = await this.getAppliedMigrations();
            const migrationsToRollback = appliedMigrations.slice(-steps);

            for (const migrationId of migrationsToRollback) {
                await db.transaction(async (client) => {
                    console.log(`Rolling back migration: ${migrationId}`);

                    const sqlPath = path.join(this.migrationsDir, `${migrationId}.down.sql`);
                    const sql = await fs.readFile(sqlPath, 'utf-8');

                    await client.query(sql);
                    await client.query('DELETE FROM migrations WHERE id = $1', [migrationId]);

                    console.log(`✅ Migration rolled back: ${migrationId}`);
                });
            }
        } catch (error) {
            console.error('Rollback failed:', error);
            throw error;
        }
    }

    public async status(): Promise<Migration[]> {
        await this.loadMigrations();
        const appliedMigrations = await this.getAppliedMigrations();

        return this.migrations.map(migration => ({
            ...migration,
            applied: appliedMigrations.includes(migration.id)
        }));
    }
}

// Export singleton instance
export const migrationManager = MigrationManager.getInstance(); 