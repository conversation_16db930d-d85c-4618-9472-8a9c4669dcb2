// Real Production API Client - No Mock Data

import { getConfig } from "./config";
import { getLogger } from "./logger";

export interface RealAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

class RealAPIClient {
  private baseURL: string;
  private apiKey: string;
  private logger = getLogger().createChild("RealAPIClient");

  constructor() {
    const config = getConfig();
    this.baseURL = config.getApiBaseUrl();
    this.apiKey = import.meta.env.VITE_API_KEY || "";

    if (!this.baseURL || this.baseURL.includes("synapseai.com")) {
      throw new Error(
        "REAL API URL REQUIRED - Please set VITE_API_BASE_URL to your backend server"
      );
    }

    console.log(`🔗 Real API Client initialized with: ${this.baseURL}`);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<RealAPIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const headers = {
      "Content-Type": "application/json",
      Accept: "application/json",
      ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
      ...options.headers,
    };

    try {
      console.log(`🌐 Making REAL API call to: ${url}`);

      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      console.log(`✅ Real API response received from: ${url}`);

      return {
        success: true,
        data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`❌ Real API call failed to: ${url}`, errorMessage);

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Authentication
  async login(credentials: { email: string; password: string }): Promise<any> {
    const response = await this.makeRequest("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    });

    if (!response.success) {
      throw new Error(response.error || "Login failed");
    }

    return response.data;
  }

  // Providers
  async getProviders(): Promise<any[]> {
    const response = await this.makeRequest<any[]>("/providers");
    return response.success ? response.data || [] : [];
  }

  async createProvider(provider: any): Promise<any> {
    const response = await this.makeRequest("/providers", {
      method: "POST",
      body: JSON.stringify(provider),
    });

    if (!response.success) {
      throw new Error(response.error || "Failed to create provider");
    }

    return response.data;
  }

  async updateProvider(id: string, updates: any): Promise<any> {
    const response = await this.makeRequest(`/providers/${id}`, {
      method: "PUT",
      body: JSON.stringify(updates),
    });

    if (!response.success) {
      throw new Error(response.error || "Failed to update provider");
    }

    return response.data;
  }

  async deleteProvider(id: string): Promise<boolean> {
    const response = await this.makeRequest(`/providers/${id}`, {
      method: "DELETE",
    });

    return response.success;
  }

  async testProvider(id: string): Promise<any> {
    const response = await this.makeRequest(`/providers/${id}/test`, {
      method: "POST",
    });

    if (!response.success) {
      throw new Error(response.error || "Provider test failed");
    }

    return response.data;
  }

  // Agents
  async getAgents(): Promise<any[]> {
    const response = await this.makeRequest<any[]>("/agents");
    return response.success ? response.data || [] : [];
  }

  async createAgent(agent: any): Promise<any> {
    const response = await this.makeRequest("/agents", {
      method: "POST",
      body: JSON.stringify(agent),
    });

    if (!response.success) {
      throw new Error(response.error || "Failed to create agent");
    }

    return response.data;
  }

  async updateAgent(id: string, updates: any): Promise<any> {
    const response = await this.makeRequest(`/agents/${id}`, {
      method: "PUT",
      body: JSON.stringify(updates),
    });

    if (!response.success) {
      throw new Error(response.error || "Failed to update agent");
    }

    return response.data;
  }

  async deleteAgent(id: string): Promise<boolean> {
    const response = await this.makeRequest(`/agents/${id}`, {
      method: "DELETE",
    });

    return response.success;
  }

  async deployAgent(id: string): Promise<any> {
    const response = await this.makeRequest(`/agents/${id}/deploy`, {
      method: "POST",
    });

    if (!response.success) {
      throw new Error(response.error || "Failed to deploy agent");
    }

    return response.data;
  }

  // Analytics
  async getAnalytics(timeRange?: string): Promise<any> {
    const params = timeRange ? `?timeRange=${timeRange}` : "";
    const response = await this.makeRequest(`/analytics/overview${params}`);
    return response.success ? response.data : null;
  }

  async getUsageStats(timeRange?: string): Promise<any> {
    const params = timeRange ? `?timeRange=${timeRange}` : "";
    const response = await this.makeRequest(`/analytics/usage${params}`);
    return response.success ? response.data : null;
  }

  // Health Check
  async healthCheck(): Promise<
    RealAPIResponse<{ status: string; timestamp: string }>
  > {
    return this.makeRequest("/health");
  }
}

// Global instance
let realAPIClient: RealAPIClient | null = null;

export function getRealAPIClient(): RealAPIClient {
  if (!realAPIClient) {
    realAPIClient = new RealAPIClient();
  }
  return realAPIClient;
}

export { RealAPIClient };
