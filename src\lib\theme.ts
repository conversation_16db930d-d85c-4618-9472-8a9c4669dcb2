// Theme Management System for SynapseAI

import React from "react";

export type Theme = "light" | "dark" | "system";

export interface ThemeState {
  theme: Theme;
  resolvedTheme: "light" | "dark";
  systemTheme: "light" | "dark";
}

class ThemeManager {
  private state: ThemeState = {
    theme: "system",
    resolvedTheme: "light",
    systemTheme: "light",
  };

  private listeners: Set<(state: ThemeState) => void> = new Set();
  private storageKey = "synapseai-theme";
  private mediaQuery: MediaQueryList | null = null;

  constructor() {
    this.initializeTheme();
  }

  private initializeTheme(): void {
    if (typeof window === "undefined") return;

    // Set up system theme detection
    this.mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    this.state.systemTheme = this.mediaQuery.matches ? "dark" : "light";

    // Listen for system theme changes
    this.mediaQuery.addEventListener("change", this.handleSystemThemeChange);

    // Load saved theme preference
    const savedTheme = localStorage.getItem(this.storageKey) as Theme;
    if (savedTheme && ["light", "dark", "system"].includes(savedTheme)) {
      this.state.theme = savedTheme;
    }

    // Calculate resolved theme
    this.updateResolvedTheme();
    this.applyTheme();
  }

  private handleSystemThemeChange = (e: MediaQueryListEvent): void => {
    this.state.systemTheme = e.matches ? "dark" : "light";
    this.updateResolvedTheme();
    this.applyTheme();
    this.notifyListeners();
  };

  private updateResolvedTheme(): void {
    this.state.resolvedTheme =
      this.state.theme === "system" ? this.state.systemTheme : this.state.theme;
  }

  private applyTheme(): void {
    if (typeof window === "undefined") return;

    const root = window.document.documentElement;

    // Remove existing theme classes
    root.classList.remove("light", "dark");

    // Add current theme class
    root.classList.add(this.state.resolvedTheme);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        "content",
        this.state.resolvedTheme === "dark" ? "#0f172a" : "#ffffff"
      );
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener({ ...this.state });
      } catch (error) {
        console.error("Error in theme listener:", error);
      }
    });
  }

  subscribe(listener: (state: ThemeState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  getState(): ThemeState {
    return { ...this.state };
  }

  setTheme(theme: Theme): void {
    this.state.theme = theme;
    this.updateResolvedTheme();
    this.applyTheme();

    // Save to localStorage
    localStorage.setItem(this.storageKey, theme);

    this.notifyListeners();
  }

  toggleTheme(): void {
    const currentTheme = this.state.theme;
    let newTheme: Theme;

    switch (currentTheme) {
      case "light":
        newTheme = "dark";
        break;
      case "dark":
        newTheme = "system";
        break;
      case "system":
        newTheme = "light";
        break;
      default:
        newTheme = "light";
    }

    this.setTheme(newTheme);
  }

  cleanup(): void {
    if (this.mediaQuery) {
      this.mediaQuery.removeEventListener(
        "change",
        this.handleSystemThemeChange
      );
    }
    this.listeners.clear();
  }
}

// Global theme manager instance
let themeManager: ThemeManager | null = null;

export function getThemeManager(): ThemeManager {
  if (!themeManager) {
    themeManager = new ThemeManager();
  }
  return themeManager;
}

// React hook for using theme in components
export function useTheme() {
  const manager = getThemeManager();
  const [state, setState] = React.useState(manager.getState());

  React.useEffect(() => {
    const unsubscribe = manager.subscribe(setState);
    return unsubscribe;
  }, [manager]);

  return {
    ...state,
    setTheme: manager.setTheme.bind(manager),
    toggleTheme: manager.toggleTheme.bind(manager),
  };
}

export { ThemeManager };
