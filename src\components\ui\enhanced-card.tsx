import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, TrendingUp, TrendingDown } from "lucide-react";

interface EnhancedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  value?: string | number;
  change?: {
    value: number;
    type: "increase" | "decrease";
    period?: string;
  };
  status?: "success" | "warning" | "error" | "info";
  badge?: string;
  actions?: React.ReactNode;
  interactive?: boolean;
  gradient?: boolean;
}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  (
    {
      className,
      title,
      subtitle,
      value,
      change,
      status,
      badge,
      actions,
      interactive = false,
      gradient = false,
      children,
      ...props
    },
    ref
  ) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case "success":
          return "border-success-200 bg-success-50/50";
        case "warning":
          return "border-warning-200 bg-warning-50/50";
        case "error":
          return "border-destructive/20 bg-destructive/5";
        case "info":
          return "border-info-200 bg-info-50/50";
        default:
          return "";
      }
    };

    return (
      <Card
        ref={ref}
        className={cn(
          "relative overflow-hidden transition-all duration-300",
          gradient && "bg-gradient-surface",
          status && getStatusColor(status),
          interactive && "interactive cursor-pointer",
          className
        )}
        {...props}
      >
        {gradient && (
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-500/5 pointer-events-none" />
        )}

        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="space-y-1">
            {title && (
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {title}
              </CardTitle>
            )}
            {subtitle && (
              <p className="text-xs text-muted-foreground">{subtitle}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {badge && (
              <Badge variant="secondary" className="text-xs">
                {badge}
              </Badge>
            )}
            {actions || (
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {value && (
            <div className="space-y-2">
              <div className="text-2xl font-bold">{value}</div>
              {change && (
                <div className="flex items-center space-x-2 text-xs">
                  {change.type === "increase" ? (
                    <TrendingUp className="h-3 w-3 text-success-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-destructive" />
                  )}
                  <span
                    className={cn(
                      "font-medium",
                      change.type === "increase"
                        ? "text-success-600"
                        : "text-destructive"
                    )}
                  >
                    {change.value > 0 ? "+" : ""}
                    {change.value}%
                  </span>
                  {change.period && (
                    <span className="text-muted-foreground">
                      from {change.period}
                    </span>
                  )}
                </div>
              )}
            </div>
          )}
          {children}
        </CardContent>
      </Card>
    );
  }
);

EnhancedCard.displayName = "EnhancedCard";

export { EnhancedCard };
