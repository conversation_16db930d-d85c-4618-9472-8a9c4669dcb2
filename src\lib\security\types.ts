export enum SecurityEventType {
    LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',
    API_ACCESS = 'API_ACCESS',
    UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
    CONFIG_CHANGE = 'CONFIG_CHANGE',
    DATA_ACCESS = 'DATA_ACCESS',
    SYSTEM_ERROR = 'SYSTEM_ERROR',
}

export enum SecurityEventSeverity {
    INFO = 'INFO',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    CRITICAL = 'CRITICAL',
}

export interface SecurityEvent {
    type: SecurityEventType;
    severity: SecurityEventSeverity;
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    details: Record<string, any>;
    timestamp?: Date;
}

export interface SecurityEventQuery {
    type?: SecurityEventType;
    severity?: SecurityEventSeverity;
    userId?: string;
    ipAddress?: string;
    startTime?: Date;
    endTime?: Date;
}

export interface SecurityAlert {
    id: string;
    eventId: string;
    severity: SecurityEventSeverity;
    userId?: string;
    details: Record<string, any>;
    created: Date;
    resolved: boolean;
    resolutionDetails?: {
        resolvedBy: string;
        resolution: string;
        resolvedAt: Date;
    };
}

export interface SecurityAnalysis {
    userId: string;
    suspiciousLoginAttempts: boolean;
    suspiciousApiAccess: boolean;
    failedLoginCount: number;
    rapidApiCallCount: number;
    lastAnalyzed: Date;
}

export interface AlertResolution {
    resolvedBy: string;
    resolution: string;
}
