import { DatabaseError as PgDatabaseError } from 'pg';

export class DatabaseError extends Error {
    public readonly code?: string;
    public readonly detail?: string;
    public readonly hint?: string;
    public readonly position?: string;

    constructor(message: string, options: Partial<DatabaseError> = {}) {
        super(message);
        this.name = 'DatabaseError';
        Object.assign(this, options);
    }

    static fromPgError(error: PgDatabaseError): DatabaseError {
        return new DatabaseError(error.message, {
            code: error.code,
            detail: error.detail,
            hint: error.hint,
            position: error.position,
        });
    }
}

export class ConnectionError extends DatabaseError {
    constructor(message: string, options: Partial<DatabaseError> = {}) {
        super(message, options);
        this.name = 'ConnectionError';
    }
}

export class QueryError extends DatabaseError {
    public readonly query?: string;
    public readonly parameters?: any[];

    constructor(
        message: string,
        options: Partial<QueryError & DatabaseError> = {}
    ) {
        super(message, options);
        this.name = 'QueryError';
        this.query = options.query;
        this.parameters = options.parameters;
    }
}

export class TransactionError extends DatabaseError {
    public readonly operation?: string;

    constructor(
        message: string,
        options: Partial<TransactionError & DatabaseError> = {}
    ) {
        super(message, options);
        this.name = 'TransactionError';
        this.operation = options.operation;
    }
}

export class ValidationError extends DatabaseError {
    public readonly field?: string;
    public readonly value?: any;
    public readonly constraint?: string;

    constructor(
        message: string,
        options: Partial<ValidationError & DatabaseError> = {}
    ) {
        super(message, options);
        this.name = 'ValidationError';
        this.field = options.field;
        this.value = options.value;
        this.constraint = options.constraint;
    }
}

export class MigrationError extends DatabaseError {
    public readonly migrationId?: string;
    public readonly direction?: 'up' | 'down';

    constructor(
        message: string,
        options: Partial<MigrationError & DatabaseError> = {}
    ) {
        super(message, options);
        this.name = 'MigrationError';
        this.migrationId = options.migrationId;
        this.direction = options.direction;
    }
}

// Error code mappings
export const PostgresErrorCodes = {
    UNIQUE_VIOLATION: '23505',
    FOREIGN_KEY_VIOLATION: '23503',
    NOT_NULL_VIOLATION: '23502',
    CONNECTION_FAILURE: '08006',
    CONNECTION_REFUSED: '08001',
    INVALID_PASSWORD: '28P01',
    UNDEFINED_TABLE: '42P01',
    UNDEFINED_COLUMN: '42703',
    QUERY_CANCELED: '57014',
} as const;

// Error handler function
export function handleDatabaseError(error: PgDatabaseError): never {
        switch (error.code) {
            case PostgresErrorCodes.UNIQUE_VIOLATION:
                throw new ValidationError('Unique constraint violation', {
                    code: error.code,
                    detail: error.detail,
                    constraint: error.constraint,
                });

            case PostgresErrorCodes.FOREIGN_KEY_VIOLATION:
                throw new ValidationError('Foreign key constraint violation', {
                    code: error.code,
                    detail: error.detail,
                    constraint: error.constraint,
                });

            case PostgresErrorCodes.NOT_NULL_VIOLATION:
                throw new ValidationError('Not null constraint violation', {
                    code: error.code,
                    detail: error.detail,
                    constraint: error.constraint,
                });

            case PostgresErrorCodes.CONNECTION_FAILURE:
            case PostgresErrorCodes.CONNECTION_REFUSED:
                throw new ConnectionError('Database connection error', {
                    code: error.code,
                    detail: error.detail,
                });

            case PostgresErrorCodes.INVALID_PASSWORD:
                throw new ConnectionError('Invalid database credentials', {
                    code: error.code,
                });

            case PostgresErrorCodes.UNDEFINED_TABLE:
            case PostgresErrorCodes.UNDEFINED_COLUMN:
                throw new QueryError('Invalid query structure', {
                    code: error.code,
                    detail: error.detail,
                });

            case PostgresErrorCodes.QUERY_CANCELED:
                throw new QueryError('Query timeout or cancellation', {
                    code: error.code,
                    detail: error.detail,
                });

            default:
            throw DatabaseError.fromPgError(error);
    }

} 