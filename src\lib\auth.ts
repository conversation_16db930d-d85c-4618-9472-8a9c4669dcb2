// Authentication Module for SynapseAI Dashboard with JWT and RBAC

import React from "react";
import { getConfig } from "./config";
import { getLogger } from "./logger";
import { getErrorHandler } from "./errorHandler";
import { SecurityEventType, SecurityEventSeverity } from "./security/types";
import { securityManager } from "./security/index";
import { getAPIClient } from "./apiClient";

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
  tenantId?: string;
  projectIds: string[];
  appIds: string[];
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
  metadata: Record<string, any>;
}

export interface JWTPayload {
  sub: string; // user id
  email: string;
  name: string;
  role: string;
  permissions: string[];
  tenantId?: string;
  projectIds: string[];
  appIds: string[];
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

export interface RefreshToken {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  lastUsed?: Date;
  deviceInfo?: string;
  ipAddress?: string;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  tenantId?: string;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  settings: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

class AuthManager {
  private state: AuthState = {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  };

  private listeners: Set<(state: AuthState) => void> = new Set();
  private tokenKey = "synapseai_token";
  private refreshTokenKey = "synapseai_refresh_token";
  private userKey = "synapseai_user";
  private config = getConfig();
  private logger = getLogger().createChild("AuthManager");
  private errorHandler = getErrorHandler();
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeAuth();
  }

  private async initializeAuth(): Promise<void> {
    try {
      const token = localStorage.getItem(this.tokenKey);
      const userStr = localStorage.getItem(this.userKey);

      if (token && userStr) {
        // Validate JWT token
        const isValid = await this.validateJWTToken(token);
        if (isValid) {
          const user = JSON.parse(userStr);
          this.setState({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Start token refresh timer
          this.startTokenRefreshTimer();

          this.logger.info("Authentication initialized successfully", {
            userId: user.id,
            tenantId: user.tenantId,
          });
        } else {
          // Try to refresh token
          await this.attemptTokenRefresh();
        }
      }
    } catch (error) {
      this.errorHandler.handleError({
        message: "Failed to initialize authentication",
        stack: error instanceof Error ? error.stack : undefined,
        component: "AuthManager",
        severity: "medium",
        category: "auth",
      });
      this.clearAuth();
    }
  }

  private setState(newState: Partial<AuthState>): void {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener(this.state);
      } catch (error) {
        console.error("Error in auth listener:", error);
      }
    });
  }

  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  getState(): AuthState {
    return { ...this.state };
  }

  async login(credentials: LoginCredentials, tenantId?: string): Promise<User> {
    this.setState({ isLoading: true, error: null });

    try {
      // Log security event
      await securityManager.logEvent({
        type: SecurityEventType.LOGIN_ATTEMPT,
        severity: SecurityEventSeverity.INFO,
        details: {
          email: credentials.email,
          tenantId,
          timestamp: new Date(),
          userAgent: navigator.userAgent,
        },
      });

      const response = await this.authenticateUser(credentials, tenantId);

      if (response.success) {
        const { accessToken, refreshToken, user: userData } = response;

        // Create user object with RBAC data
        const user: User = {
          id: userData.id,
          email: credentials.email,
          name: userData.name,
          role: userData.role,
          permissions: userData.permissions || [],
          tenantId: userData.tenantId,
          projectIds: userData.projectIds || [],
          appIds: userData.appIds || [],
          avatar: userData.avatar,
          createdAt: userData.createdAt,
          lastLogin: new Date().toISOString(),
          metadata: userData.metadata || {},
        };

        // Store tokens and user data
        localStorage.setItem(this.tokenKey, accessToken);
        localStorage.setItem(this.refreshTokenKey, refreshToken);
        localStorage.setItem(this.userKey, JSON.stringify(user));

        this.setState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        // Start token refresh timer
        this.startTokenRefreshTimer();

        // Log successful login
        await securityManager.logEvent({
          type: SecurityEventType.LOGIN_ATTEMPT,
          severity: SecurityEventSeverity.INFO,
          userId: user.id,
          details: {
            success: true,
            tenantId: user.tenantId,
            role: user.role,
            timestamp: new Date(),
          },
        });

        this.logger.info("User logged in successfully", {
          userId: user.id,
          tenantId: user.tenantId,
          role: user.role,
        });

        return user;
      } else {
        // Log failed login
        await securityManager.logEvent({
          type: SecurityEventType.LOGIN_ATTEMPT,
          severity: SecurityEventSeverity.WARNING,
          details: {
            success: false,
            email: credentials.email,
            error: response.error,
            timestamp: new Date(),
          },
        });

        throw new Error(response.error || "Authentication failed");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";

      this.errorHandler.handleError({
        message: "Login failed",
        stack: error instanceof Error ? error.stack : undefined,
        component: "AuthManager",
        severity: "medium",
        category: "auth",
        metadata: { email: credentials.email, tenantId },
      });

      this.setState({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  }

  private async authenticateUser(
    credentials: LoginCredentials,
    tenantId?: string
  ): Promise<{
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
    user?: any;
    error?: string;
  }> {
    try {
      // Input validation and sanitization
      if (!this.validateCredentials(credentials)) {
        return {
          success: false,
          error: "Invalid credentials format",
        };
      }

      // Rate limiting check
      if (!this.checkRateLimit(credentials.email)) {
        return {
          success: false,
          error: "Too many login attempts. Please try again later.",
        };
      }

      // Call authentication API
      const apiClient = getAPIClient();
      const authResponse = await apiClient.post("/api/v1/auth/login", {
        email: credentials.email,
        password: credentials.password,
        tenantId,
      });

      if (!authResponse.success || !authResponse.data) {
        this.logFailedAttempt(credentials.email);
        return {
          success: false,
          error: authResponse.error || "Authentication failed",
        };
      }

      const { accessToken, refreshToken, user } = authResponse.data;

      return {
        success: true,
        accessToken,
        refreshToken,
        user,
      };
    } catch (error) {
      this.logFailedAttempt(credentials.email);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Authentication failed",
      };
    }
  }

  private async generateJWTToken(user: any): Promise<string> {
    // In production, this would use a proper JWT library like jose or jsonwebtoken
    // For demo purposes, we create a base64 encoded token with proper structure
    const header = {
      alg: this.config.get("auth").jwt.algorithm,
      typ: "JWT",
    };

    const now = Math.floor(Date.now() / 1000);
    const expiresIn = this.parseTimeString(
      this.config.get("auth").jwt.expiresIn
    );

    const payload: JWTPayload = {
      sub: user.id,
      email: user.email || "",
      name: user.name,
      role: user.role,
      permissions: user.permissions,
      tenantId: user.tenantId,
      projectIds: user.projectIds,
      appIds: user.appIds,
      iat: now,
      exp: now + Math.floor(expiresIn / 1000),
      iss: this.config.get("auth").jwt.issuer,
      aud: this.config.get("auth").jwt.audience,
    };

    // In production, use proper JWT signing
    const headerB64 = btoa(JSON.stringify(header));
    const payloadB64 = btoa(JSON.stringify(payload));
    const signature = await this.signJWT(`${headerB64}.${payloadB64}`);

    return `${headerB64}.${payloadB64}.${signature}`;
  }

  private generateRefreshToken(): string {
    const timestamp = Date.now();
    const randomBytes = crypto.getRandomValues(new Uint8Array(32));
    const randomString = Array.from(randomBytes, (byte) =>
      byte.toString(16).padStart(2, "0")
    ).join("");
    return `rt_${timestamp}_${randomString}`;
  }

  private validateCredentials(credentials: LoginCredentials): boolean {
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(credentials.email)) {
      return false;
    }

    // Password validation
    if (!credentials.password || credentials.password.length < 6) {
      return false;
    }

    // Sanitize inputs
    credentials.email = this.sanitizeInput(credentials.email);
    credentials.password = this.sanitizeInput(credentials.password);

    return true;
  }

  private sanitizeInput(input: string): string {
    // Remove potentially dangerous characters
    return input.replace(/[<>"'&]/g, "").trim();
  }

  private async hashPassword(password: string): Promise<string> {
    // In production, use bcrypt or similar
    const encoder = new TextEncoder();
    const data = encoder.encode(password + this.config.get("auth").jwt.secret);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  }

  private rateLimitMap = new Map<
    string,
    { attempts: number; lastAttempt: number }
  >();

  private checkRateLimit(email: string): boolean {
    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    const record = this.rateLimitMap.get(email);

    if (!record) {
      this.rateLimitMap.set(email, { attempts: 1, lastAttempt: now });
      return true;
    }

    // Reset if window has passed
    if (now - record.lastAttempt > windowMs) {
      this.rateLimitMap.set(email, { attempts: 1, lastAttempt: now });
      return true;
    }

    // Check if exceeded limit
    if (record.attempts >= maxAttempts) {
      return false;
    }

    record.attempts++;
    record.lastAttempt = now;
    return true;
  }

  private logFailedAttempt(email: string): void {
    this.logger.warn("Failed login attempt", {
      email: this.sanitizeInput(email),
      timestamp: new Date().toISOString(),
      ip: this.getClientIP(),
    });
  }

  private getClientIP(): string {
    // In production, get from request headers
    return "unknown";
  }

  private async signJWT(data: string): Promise<string> {
    // In production, use proper HMAC signing with the JWT secret
    const encoder = new TextEncoder();
    const keyData = encoder.encode(this.config.get("auth").jwt.secret);
    const dataBuffer = encoder.encode(data);

    const key = await crypto.subtle.importKey(
      "raw",
      keyData,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signature = await crypto.subtle.sign("HMAC", key, dataBuffer);
    const signatureArray = new Uint8Array(signature);

    return Array.from(signatureArray, (byte) =>
      byte.toString(16).padStart(2, "0")
    ).join("");
  }

  private async validateJWTToken(token: string): Promise<boolean> {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) {
        return false;
      }

      const [headerB64, payloadB64, signature] = parts;
      const payload: JWTPayload = JSON.parse(atob(payloadB64));

      // Check expiration
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) {
        return false;
      }

      // Verify signature
      const expectedSignature = await this.signJWT(
        `${headerB64}.${payloadB64}`
      );
      if (signature !== expectedSignature) {
        return false;
      }

      // Check issuer and audience
      if (
        payload.iss !== this.config.get("auth").jwt.issuer ||
        payload.aud !== this.config.get("auth").jwt.audience
      ) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.warn("JWT validation failed", { error });
      return false;
    }
  }

  private parseTimeString(timeStr: string): number {
    const unit = timeStr.slice(-1);
    const value = parseInt(timeStr.slice(0, -1));

    switch (unit) {
      case "s":
        return value * 1000;
      case "m":
        return value * 60 * 1000;
      case "h":
        return value * 60 * 60 * 1000;
      case "d":
        return value * 24 * 60 * 60 * 1000;
      default:
        return value;
    }
  }

  private startTokenRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Refresh token 5 minutes before expiration
    const refreshInterval =
      this.parseTimeString(this.config.get("auth").jwt.expiresIn) -
      5 * 60 * 1000;

    this.refreshTimer = setTimeout(async () => {
      try {
        await this.refreshAccessToken();
      } catch (error) {
        this.logger.error("Token refresh failed", { error });
        this.clearAuth();
      }
    }, refreshInterval);
  }

  private async refreshAccessToken(): Promise<void> {
    const refreshToken = localStorage.getItem(this.refreshTokenKey);
    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    // In production, call your auth API to refresh the token
    const response = await this.callRefreshTokenAPI(refreshToken);

    if (response.success && response.accessToken) {
      localStorage.setItem(this.tokenKey, response.accessToken);
      if (response.refreshToken) {
        localStorage.setItem(this.refreshTokenKey, response.refreshToken);
      }

      // Restart refresh timer
      this.startTokenRefreshTimer();

      this.logger.info("Access token refreshed successfully");
    } else {
      throw new Error("Token refresh failed");
    }
  }

  private async callRefreshTokenAPI(refreshToken: string): Promise<{
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
    error?: string;
  }> {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.post("/api/v1/auth/refresh", {
        refreshToken,
      });

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || "Token refresh failed",
        };
      }

      return {
        success: true,
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Token refresh failed",
      };
    }
  }

  private async attemptTokenRefresh(): Promise<void> {
    try {
      await this.refreshAccessToken();

      // Re-initialize auth state
      const userStr = localStorage.getItem(this.userKey);
      if (userStr) {
        const user = JSON.parse(userStr);
        this.setState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      }
    } catch (error) {
      this.logger.warn("Token refresh attempt failed", { error });
      this.clearAuth();
    }
  }

  async register(data: RegisterData): Promise<User> {
    this.setState({ isLoading: true, error: null });

    try {
      // Production registration would integrate with your auth service
      const response = await this.registerUser(data);

      if (response.success) {
        const user: User = {
          id: response.user.id,
          email: data.email,
          name: data.name,
          role: "user",
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(data.name)}`,
          createdAt: new Date().toISOString(),
          permissions: [],
          projectIds: [],
          appIds: [],
          metadata: undefined,
        };

        // Store authentication token securely
        localStorage.setItem(this.tokenKey, response.token);
        localStorage.setItem(this.userKey, JSON.stringify(user));

        this.setState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        return user;
      } else {
        throw new Error(response.error || "Registration failed");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      this.setState({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  }

  private async registerUser(data: RegisterData): Promise<{
    success: boolean;
    user?: any;
    token?: string;
    error?: string;
  }> {
    try {
      // Basic validation
      if (!data.email || !data.password || !data.name) {
        return {
          success: false,
          error: "All fields are required",
        };
      }

      if (data.password.length < 6) {
        return {
          success: false,
          error: "Password must be at least 6 characters",
        };
      }

      // Call registration API
      const apiClient = getAPIClient();
      const response = await apiClient.post("/api/v1/auth/register", {
        email: data.email,
        password: data.password,
        name: data.name,
      });

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || "Registration failed",
        };
      }

      return {
        success: true,
        user: response.data.user,
        token: response.data.accessToken,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Registration failed",
      };
    }
  }

  async logout(): Promise<void> {
    this.setState({ isLoading: true });

    try {
      const user = this.state.user;

      // Log security event
      if (user) {
        await securityManager.logEvent({
          type: SecurityEventType.LOGIN_ATTEMPT,
          severity: SecurityEventSeverity.INFO,
          userId: user.id,
          details: {
            action: "logout",
            timestamp: new Date(),
            tenantId: user.tenantId,
          },
        });
      }

      // In production, call logout API to invalidate tokens
      await this.callLogoutAPI();

      this.clearAuth();

      this.logger.info("User logged out successfully", {
        userId: user?.id,
        tenantId: user?.tenantId,
      });
    } catch (error) {
      this.errorHandler.handleError({
        message: "Logout error",
        stack: error instanceof Error ? error.stack : undefined,
        component: "AuthManager",
        severity: "low",
        category: "auth",
      });
      // Clear auth even if logout API fails
      this.clearAuth();
    }
  }

  private async callLogoutAPI(): Promise<void> {
    try {
      const apiClient = getAPIClient();
      const refreshToken = this.getRefreshToken();

      if (refreshToken) {
        await apiClient.post("/api/v1/auth/logout", {
          refreshToken,
        });
      }
    } catch (error) {
      // Log error but don't throw - logout should always succeed locally
      this.logger.warn("Logout API call failed", { error });
    }
  }

  private clearAuth(): void {
    // Clear refresh timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    // Clear stored data
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem(this.userKey);

    this.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  }

  async refreshToken(): Promise<string | null> {
    try {
      const currentToken = localStorage.getItem(this.tokenKey);
      if (!currentToken) {
        return null;
      }

      // Production token refresh would validate with your auth service
      const response = await this.validateAndRefreshToken(currentToken);

      if (response.success && response.token) {
        localStorage.setItem(this.tokenKey, response.token);
        return response.token;
      } else {
        this.clearAuth();
        return null;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      this.clearAuth();
      return null;
    }
  }

  private async validateAndRefreshToken(token: string): Promise<{
    success: boolean;
    token?: string;
    error?: string;
  }> {
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Basic token validation (in production, verify JWT signature and expiry)
    if (!token.startsWith("synapseai_")) {
      return {
        success: false,
        error: "Invalid token format",
      };
    }

    // Check if token is not too old (demo: 24 hours)
    const tokenParts = token.split("_");
    if (tokenParts.length >= 2) {
      const timestamp = parseInt(tokenParts[1]);
      const now = Date.now();
      const tokenAge = now - timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      if (tokenAge > maxAge) {
        return {
          success: false,
          error: "Token expired",
        };
      }
    }

    return {
      success: true,
      token: this.generateRefreshToken(),
    };
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  hasPermission(
    permission: string,
    resource?: string,
    tenantId?: string,
    projectId?: string,
    appId?: string
  ): boolean {
    if (!this.state.user) {
      return false;
    }

    const user = this.state.user;

    // Check tenant isolation
    if (this.config.get("auth").multiTenant.enabled && tenantId) {
      if (user.tenantId !== tenantId && user.role !== "superadmin") {
        return false;
      }
    }

    // Check project access
    if (
      projectId &&
      !user.projectIds.includes("*") &&
      !user.projectIds.includes(projectId)
    ) {
      return false;
    }

    // Check app access
    if (appId && !user.appIds.includes("*") && !user.appIds.includes(appId)) {
      return false;
    }

    // Check direct permission
    if (user.permissions.includes(permission)) {
      return true;
    }

    // Check role hierarchy
    const roleHierarchy = this.config.get("auth").rbac.roleHierarchy;
    const inheritedRoles = roleHierarchy[user.role] || [];

    // Check if any inherited role has the permission
    for (const inheritedRole of inheritedRoles) {
      const rolePermissions = this.getRolePermissions(inheritedRole);
      if (rolePermissions.includes(permission)) {
        return true;
      }
    }

    return false;
  }

  private getRolePermissions(role: string): string[] {
    // In production, this would fetch from database or cache
    const rolePermissions: Record<string, string[]> = {
      superadmin: [
        "read",
        "write",
        "delete",
        "manage_users",
        "manage_tenants",
        "manage_providers",
        "manage_agents",
        "system_admin",
      ],
      admin: [
        "read",
        "write",
        "delete",
        "manage_users",
        "manage_providers",
        "manage_agents",
      ],
      user: ["read", "write"],
      viewer: ["read"],
    };

    return rolePermissions[role] || [];
  }

  hasRole(role: string): boolean {
    if (!this.state.user) {
      return false;
    }

    if (this.state.user.role === role) {
      return true;
    }

    // Check role hierarchy
    const roleHierarchy = this.config.get("auth").rbac.roleHierarchy;
    const inheritedRoles = roleHierarchy[this.state.user.role] || [];

    return inheritedRoles.includes(role);
  }

  canAccessTenant(tenantId: string): boolean {
    if (!this.state.user) {
      return false;
    }

    // Superadmin can access all tenants
    if (this.state.user.role === "superadmin") {
      return true;
    }

    return this.state.user.tenantId === tenantId;
  }

  canAccessProject(projectId: string): boolean {
    if (!this.state.user) {
      return false;
    }

    return (
      this.state.user.projectIds.includes("*") ||
      this.state.user.projectIds.includes(projectId)
    );
  }

  canAccessApp(appId: string): boolean {
    if (!this.state.user) {
      return false;
    }

    return (
      this.state.user.appIds.includes("*") ||
      this.state.user.appIds.includes(appId)
    );
  }

  requireAuth(): void {
    if (!this.state.isAuthenticated) {
      throw new Error("Authentication required");
    }
  }

  requirePermission(
    permission: string,
    resource?: string,
    tenantId?: string,
    projectId?: string,
    appId?: string
  ): void {
    this.requireAuth();
    if (!this.hasPermission(permission, resource, tenantId, projectId, appId)) {
      // Log security event for unauthorized access attempt
      securityManager.logEvent({
        type: SecurityEventType.UNAUTHORIZED_ACCESS,
        severity: SecurityEventSeverity.WARNING,
        userId: this.state.user?.id,
        details: {
          permission,
          resource,
          tenantId,
          projectId,
          appId,
          timestamp: new Date(),
          userRole: this.state.user?.role,
        },
      });

      throw new Error(`Permission '${permission}' required`);
    }
  }

  requireRole(role: string): void {
    this.requireAuth();
    if (!this.hasRole(role)) {
      throw new Error(`Role '${role}' required`);
    }
  }

  requireTenantAccess(tenantId: string): void {
    this.requireAuth();
    if (!this.canAccessTenant(tenantId)) {
      throw new Error(`Access to tenant '${tenantId}' denied`);
    }
  }

  getCurrentTenant(): string | undefined {
    return this.state.user?.tenantId;
  }

  getAccessToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.refreshTokenKey);
  }

  async switchTenant(tenantId: string): Promise<void> {
    this.requireAuth();

    // In production, validate tenant access and re-authenticate
    if (!this.canAccessTenant(tenantId)) {
      throw new Error(`Access to tenant '${tenantId}' denied`);
    }

    // Update user tenant context
    if (this.state.user) {
      const updatedUser = {
        ...this.state.user,
        tenantId,
      };

      localStorage.setItem(this.userKey, JSON.stringify(updatedUser));

      this.setState({
        user: updatedUser,
      });

      this.logger.info("Tenant switched", {
        userId: updatedUser.id,
        newTenantId: tenantId,
      });
    }
  }
}

// Global auth manager instance
let authManager: AuthManager | null = null;

export function getAuthManager(): AuthManager {
  if (!authManager) {
    authManager = new AuthManager();
  }
  return authManager;
}

// React hook for using auth in components
export function useAuth() {
  const auth = getAuthManager();
  const [state, setState] = React.useState(auth.getState());

  React.useEffect(() => {
    const unsubscribe = auth.subscribe(setState);
    return unsubscribe;
  }, [auth]);

  return {
    ...state,
    login: auth.login.bind(auth),
    register: auth.register.bind(auth),
    logout: auth.logout.bind(auth),
    hasPermission: auth.hasPermission.bind(auth),
    requireAuth: auth.requireAuth.bind(auth),
    requirePermission: auth.requirePermission.bind(auth),
  };
}

export { AuthManager };
