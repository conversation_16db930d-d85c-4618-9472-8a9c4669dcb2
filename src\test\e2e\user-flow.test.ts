import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { db } from '../../lib/database';
import { securityManager } from '../../lib/security';
import { dbTestUtils } from '../utils';

describe('End-to-End User Flow Tests', () => {
    beforeAll(async () => {
        process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/synapseai_test';
        process.env.SIGNING_SECRET = 'test-signing-secret';
        await db.connect();
    });

    afterAll(async () => {
        await db.disconnect();
    });

    beforeEach(async () => {
        await dbTestUtils.cleanDatabase();
    });

    it('should handle complete provider and agent creation flow', async () => {
        // 1. Create a provider
        const providerResult = await db.query(
            'INSERT INTO providers (name, type, api_key, status, config) VALUES ($1, $2, $3, $4, $5) RETURNING *',
            [
                'Test OpenAI Provider',
                'openai',
                'initial-api-key',
                'active',
                {
                    maxTokens: 1000,
                    temperature: 0.7,
                    timeout: 30000,
                },
            ]
        );

        const provider = providerResult.rows[0];
        expect(provider.id).toBeDefined();
        expect(provider.name).toBe('Test OpenAI Provider');

        // 2. Rotate the provider's API key
        const newApiKey = await securityManager.rotateApiKey(provider.id);
        expect(newApiKey).toBeDefined();
        expect(newApiKey).not.toBe('initial-api-key');

        // Verify the key was rotated
        const isValidKey = await securityManager.validateApiKey(newApiKey);
        expect(isValidKey).toBe(true);

        // 3. Create an agent using the provider
        const agentResult = await db.query(
            'INSERT INTO agents (name, description, type, status, version, provider_id, model, system_prompt, config) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *',
            [
                'Test Chat Agent',
                'A test chat agent',
                'chatbot',
                'active',
                '1.0.0',
                provider.id,
                'gpt-4',
                'You are a helpful assistant',
                {
                    temperature: 0.7,
                    maxTokens: 1000,
                    topP: 1,
                },
            ]
        );

        const agent = agentResult.rows[0];
        expect(agent.id).toBeDefined();
        expect(agent.provider_id).toBe(provider.id);

        // 4. Record some metrics
        const timestamp = new Date();
        await db.query(
            'INSERT INTO metrics (entity_type, entity_id, metrics, timestamp) VALUES ($1, $2, $3, $4)',
            [
                'agent',
                agent.id,
                {
                    totalInteractions: 1,
                    successRate: 100,
                    avgResponseTime: 500,
                    tokenUsage: 150,
                },
                timestamp,
            ]
        );

        // 5. Verify metrics were recorded
        const metricsResult = await db.query(
            'SELECT * FROM metrics WHERE entity_id = $1',
            [agent.id]
        );

        expect(metricsResult.rows).toHaveLength(1);
        expect(metricsResult.rows[0].metrics.totalInteractions).toBe(1);

        // 6. Test request signing
        const testPayload = {
            agentId: agent.id,
            message: 'Hello, agent!',
        };

        const signature = securityManager.signRequest(testPayload);
        expect(signature).toBeDefined();

        const isValidSignature = securityManager.verifyRequestSignature(
            signature,
            testPayload,
            Date.now()
        );
        expect(isValidSignature).toBe(true);

        // 7. Log security event
        await securityManager.logSecurityEvent(
            'agent_interaction',
            'agent',
            agent.id,
            {
                action: 'message_sent',
                timestamp: new Date(),
                success: true,
            }
        );

        // 8. Verify security event was logged
        const securityEvents = await db.query(
            'SELECT * FROM security_events WHERE entity_id = $1',
            [agent.id]
        );

        expect(securityEvents.rows).toHaveLength(1);
        expect(securityEvents.rows[0].type).toBe('agent_interaction');
        expect(securityEvents.rows[0].details.action).toBe('message_sent');
    });

    it('should handle error cases and security violations', async () => {
        // 1. Try to validate non-existent API key
        const isValidKey = await securityManager.validateApiKey('non-existent-key');
        expect(isValidKey).toBe(false);

        // 2. Try to verify expired signature
        const testPayload = { agentId: 'test', message: 'test' };
        const signature = securityManager.signRequest (testPayload);

        const isValidSignature = securityManager.verifyRequestSignature(
            signature,
            testPayload,
            Date.now()
        );
        expect(isValidSignature).toBe(false);

        // 3. Try to create provider with invalid data
        await expect(
            db.query(
                'INSERT INTO providers (name, type, api_key) VALUES ($1, $2, $3)',
                ['Test Provider', 'invalid_type', 'key']
            )
        ).rejects.toThrow();

        // 4. Try to create agent without provider
        await expect(
            db.query(
                'INSERT INTO agents (name, type, status, version, provider_id, model) VALUES ($1, $2, $3, $4, $5, $6)',
                ['Test Agent', 'chatbot', 'active', '1.0.0', 'non-existent-id', 'gpt-4']
            )
        ).rejects.toThrow();

        // 5. Verify security events are logged for failures
        await securityManager.logSecurityEvent(
            'security_violation',
            'system',
            'test',
            {
                action: 'invalid_api_key',
                timestamp: new Date(),
                success: false,
            }
        );

        const securityEvents = await db.query(
            'SELECT * FROM security_events WHERE type = $1',
            ['security_violation']
        );

        expect(securityEvents.rows).toHaveLength(1);
        expect(securityEvents.rows[0].details.action).toBe('invalid_api_key');
        expect(securityEvents.rows[0].details.success).toBe(false);
    });
}); 