import { getAPIClient, ProviderRequest, ProviderResponse } from "./apiClient";
import {
  getProviderRouter,
  RoutingContext,
  RoutingDecision,
} from "./providerRouting";

// PostgreSQL Pool type - will be dynamically imported when needed
let Pool: any = null;

export interface SynapseAIConfig {
  apiKey?: string;
  baseUrl?: string;
  environment?: "development" | "production" | "staging";
  enableLogging?: boolean;
  retryAttempts?: number;
  timeout?: number;
  enableRouting?: boolean;
  enableEventSystem?: boolean;
  enableDatabase?: boolean;
  databaseUrl?: string;
}

export interface AIProvider {
  id: string;
  name: string;
  type: "openai" | "anthropic" | "google" | "mistral" | "groq";
  status: "active" | "inactive" | "error" | "maintenance";
  apiKey: string;
  models: string[];
  config?: {
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
  };
  metrics?: {
    requests: number;
    successRate: number;
    avgLatency: number;
    cost: number;
  };
}

export interface EventSubscription {
  id: string;
  eventType:
    | "ai_decision"
    | "usage_metric"
    | "error"
    | "custom"
    | "routing_decision"
    | "provider_status";
  callback: (event: any) => void;
  filters?: Record<string, any>;
}

export interface AIRequest {
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  context?: RoutingContext;
  providerId?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface AIResponse {
  id: string;
  provider: string;
  model: string;
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  responseTime: number;
  routingDecision?: RoutingDecision;
}

class SynapseAISDK {
  private config: SynapseAIConfig;
  private providers: Map<string, AIProvider> = new Map();
  private eventSubscriptions: Map<string, EventSubscription> = new Map();
  private isInitialized = false;
  private eventBus: EventTarget = new EventTarget();
  private requestHistory: Array<{
    timestamp: string;
    request: AIRequest;
    response?: AIResponse;
    error?: string;
  }> = [];

  // Add database pool (optional)
  private db?: any;

  constructor(config: SynapseAIConfig = {}) {
    this.config = {
      baseUrl: "https://api.synapseai.com",
      environment: "development",
      enableLogging: true,
      retryAttempts: 3,
      timeout: 30000,
      enableRouting: true,
      enableEventSystem: true,
      enableDatabase: false, // Default to false for backward compatibility
      ...config,
    };

    // Initialize database if enabled (async)
    if (this.config.enableDatabase && this.config.databaseUrl) {
      this.initializeDatabase().catch((error) => {
        console.error("Failed to initialize database:", error);
      });
    }
  }

  private async initializeDatabase(): Promise<void> {
    // Skip in browser environment
    if (typeof window !== "undefined") {
      console.warn("Database features not available in browser environment");
      return;
    }

    try {
      // Dynamically import PostgreSQL driver
      const { Pool: PgPool } = await import("pg");
      Pool = PgPool;

      this.db = new Pool({
        connectionString: this.config.databaseUrl,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      if (this.config.enableLogging) {
        console.log("🗄️ PostgreSQL connection initialized");
      }
    } catch (error) {
      console.warn(
        "PostgreSQL driver not available. Database features disabled.",
        error
      );
      this.db = null;
    }
  }

  async initialize(): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log("🧠 Initializing SynapseAI SDK...");
      }

      // Initialize event system
      if (this.config.enableEventSystem) {
        this.setupEventSystem();
      }

      // Load providers from configuration
      await this.loadProviders();

      // Initialize provider router
      if (this.config.enableRouting) {
        const router = getProviderRouter();
        this.providers.forEach((provider) => {
          router.updateProvider({
            providerId: provider.id,
            name: provider.name,
            type: provider.type,
            models: provider.models,
            capabilities: this.getProviderCapabilities(provider.type),
            performance: {
              averageLatency: provider.metrics?.avgLatency || 1000,
              successRate: provider.metrics?.successRate || 95,
              costPerToken: this.getCostPerToken(provider.type),
              maxTokens: provider.config?.maxTokens || 4096,
            },
            limits: {
              requestsPerMinute: 60,
              requestsPerDay: 10000,
              currentUsage: provider.metrics?.requests || 0,
            },
            status: provider.status,
          });
        });
      }

      this.isInitialized = true;

      if (this.config.enableLogging) {
        console.log("✅ SynapseAI SDK initialized successfully");
      }

      // Emit initialization event
      this.emitEvent("sdk_initialized", {
        timestamp: new Date().toISOString(),
        config: this.config,
        providersCount: this.providers.size,
      });
    } catch (error) {
      console.error("❌ Failed to initialize SynapseAI SDK:", error);
      throw error;
    }
  }

  private setupEventSystem(): void {
    // Setup event listeners for browser events
    if (typeof window !== "undefined") {
      window.addEventListener("beforeunload", () => {
        this.cleanup();
      });
    }

    // Setup periodic health checks
    setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Every 30 seconds
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const apiClient = getAPIClient();
      const health = await apiClient.healthCheck();

      this.emitEvent("health_check", {
        status: health.data?.status || "unknown",
        timestamp: health.data?.timestamp || new Date().toISOString(),
        providersCount: this.providers.size,
      });
    } catch (error) {
      this.emitEvent("health_check_failed", {
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async loadProviders(): Promise<void> {
    try {
      // Try to load from API first
      const apiClient = getAPIClient();
      const providersFromAPI = await apiClient.getProviders();

      if (providersFromAPI && providersFromAPI.length > 0) {
        providersFromAPI.forEach((provider: any) => {
          // Transform API response to AIProvider format
          const aiProvider: AIProvider = {
            id: provider.id || provider.providerId,
            name: provider.name,
            type: provider.type,
            status: provider.status || "active",
            apiKey: provider.apiKey || "***",
            models: provider.models || [],
            config: provider.config,
            metrics: provider.metrics,
          };
          this.providers.set(aiProvider.id, aiProvider);
        });
        return;
      }
    } catch (error) {
      if (this.config.enableLogging) {
        console.warn(
          "Failed to load providers from API, using defaults:",
          error
        );
      }
    }

    // No fallback providers - require proper configuration
    if (this.config.enableLogging) {
      console.warn(
        "No providers loaded from API. Please configure providers through the admin panel."
      );
    }
  }

  getProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }

  getProvider(id: string): AIProvider | undefined {
    return this.providers.get(id);
  }

  addProvider(provider: AIProvider): void {
    this.providers.set(provider.id, provider);

    // Update router if routing is enabled
    if (this.config.enableRouting) {
      const router = getProviderRouter();
      router.updateProvider({
        providerId: provider.id,
        name: provider.name,
        type: provider.type,
        models: provider.models,
        capabilities: this.getProviderCapabilities(provider.type),
        performance: {
          averageLatency: provider.metrics?.avgLatency || 1000,
          successRate: provider.metrics?.successRate || 95,
          costPerToken: this.getCostPerToken(provider.type),
          maxTokens: provider.config?.maxTokens || 4096,
        },
        limits: {
          requestsPerMinute: 60,
          requestsPerDay: 10000,
          currentUsage: provider.metrics?.requests || 0,
        },
        status: provider.status,
      });
    }

    this.emitEvent("provider_added", { provider });
  }

  removeProvider(id: string): boolean {
    const removed = this.providers.delete(id);
    if (removed) {
      // Remove from router if routing is enabled
      if (this.config.enableRouting) {
        const router = getProviderRouter();
        router.removeProvider(id);
      }

      this.emitEvent("provider_removed", { providerId: id });
    }
    return removed;
  }

  updateProvider(id: string, updates: Partial<AIProvider>): boolean {
    const provider = this.providers.get(id);
    if (!provider) {
      return false;
    }

    const updatedProvider = { ...provider, ...updates };
    this.providers.set(id, updatedProvider);

    // Update router if routing is enabled
    if (this.config.enableRouting) {
      const router = getProviderRouter();
      router.updateProvider({
        providerId: updatedProvider.id,
        name: updatedProvider.name,
        type: updatedProvider.type,
        models: updatedProvider.models,
        capabilities: this.getProviderCapabilities(updatedProvider.type),
        performance: {
          averageLatency: updatedProvider.metrics?.avgLatency || 1000,
          successRate: updatedProvider.metrics?.successRate || 95,
          costPerToken: this.getCostPerToken(updatedProvider.type),
          maxTokens: updatedProvider.config?.maxTokens || 4096,
        },
        limits: {
          requestsPerMinute: 60,
          requestsPerDay: 10000,
          currentUsage: updatedProvider.metrics?.requests || 0,
        },
        status: updatedProvider.status,
      });
    }

    this.emitEvent("provider_updated", { provider: updatedProvider });
    return true;
  }

  subscribe(
    eventType: string,
    callback: (event: any) => void,
    filters?: Record<string, any>
  ): string {
    const id = Math.random().toString(36).substring(2, 9);
    const subscription: EventSubscription = {
      id,
      eventType: eventType as any,
      callback,
      filters,
    };
    this.eventSubscriptions.set(id, subscription);

    if (this.config.enableLogging) {
      console.log(`📡 Subscribed to ${eventType} events with ID: ${id}`);
    }

    return id;
  }

  unsubscribe(subscriptionId: string): boolean {
    const removed = this.eventSubscriptions.delete(subscriptionId);

    if (removed && this.config.enableLogging) {
      console.log(`📡 Unsubscribed from events with ID: ${subscriptionId}`);
    }

    return removed;
  }

  getSubscriptions(): EventSubscription[] {
    return Array.from(this.eventSubscriptions.values());
  }

  emitEvent(eventType: string, data: any): void {
    if (!this.config.enableEventSystem) {
      return;
    }

    const event = {
      id: Math.random().toString(36).substring(2, 9),
      type: eventType,
      timestamp: new Date().toISOString(),
      data,
    };

    // Notify all subscribers
    this.eventSubscriptions.forEach((subscription) => {
      if (
        subscription.eventType === eventType ||
        subscription.eventType === "custom"
      ) {
        // Apply filters if they exist
        if (subscription.filters) {
          const matchesFilters = Object.entries(subscription.filters).every(
            ([key, value]) => data[key] === value
          );
          if (!matchesFilters) {
            return;
          }
        }

        try {
          subscription.callback(event);
        } catch (error) {
          console.error("Error in event callback:", error);
          this.emitEvent("callback_error", {
            subscriptionId: subscription.id,
            eventType,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }
    });

    // Emit to browser event system
    if (typeof window !== "undefined") {
      const customEvent = new CustomEvent(`synapseai:${eventType}`, {
        detail: event,
      });
      this.eventBus.dispatchEvent(customEvent);
    }

    if (this.config.enableLogging) {
      console.log(`📡 Event emitted: ${eventType}`, data);
    }
  }

  async sendRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    let selectedProvider: AIProvider;
    let routingDecision: RoutingDecision | undefined;

    try {
      // Use intelligent routing if enabled and no specific provider requested
      if (this.config.enableRouting && !request.providerId) {
        const router = getProviderRouter();
        const context: RoutingContext = {
          requestType: "text",
          complexity: "medium",
          priority: "normal",
          ...request.context,
        };

        routingDecision = await router.routeRequest(context);
        const provider = this.providers.get(routingDecision.selectedProvider);

        if (!provider) {
          throw new Error(
            `Routed provider ${routingDecision.selectedProvider} not found`
          );
        }

        selectedProvider = provider;
      } else {
        // Use specified provider or first available
        const providerId =
          request.providerId || Array.from(this.providers.keys())[0];
        const provider = this.providers.get(providerId);

        if (!provider) {
          throw new Error(`Provider ${providerId} not found`);
        }

        selectedProvider = provider;
      }

      if (selectedProvider.status !== "active") {
        throw new Error(
          `Provider ${selectedProvider.id} is not active (status: ${selectedProvider.status})`
        );
      }

      // Emit request event
      this.emitEvent("ai_request", {
        providerId: selectedProvider.id,
        provider: selectedProvider.name,
        request,
        routingDecision,
        timestamp: new Date().toISOString(),
      });

      // Make the actual API call
      const apiClient = getAPIClient();
      const providerRequest: ProviderRequest = {
        providerId: selectedProvider.id,
        model: request.model || selectedProvider.models[0],
        messages: request.messages,
        temperature:
          request.temperature || selectedProvider.config?.temperature || 0.7,
        maxTokens:
          request.maxTokens || selectedProvider.config?.maxTokens || 1000,
        stream: request.stream || false,
      };

      const providerResponse =
        await apiClient.sendProviderRequest(providerRequest);
      const responseTime = Date.now() - startTime;

      const aiResponse: AIResponse = {
        id: providerResponse.id,
        provider: selectedProvider.name,
        model: providerResponse.model,
        content: providerResponse.choices[0]?.message?.content || "",
        usage: providerResponse.usage,
        cost: providerResponse.cost,
        responseTime,
        routingDecision,
      };

      // Update provider metrics
      this.updateProviderMetrics(selectedProvider.id, {
        requests: (selectedProvider.metrics?.requests || 0) + 1,
        avgLatency:
          ((selectedProvider.metrics?.avgLatency || 0) + responseTime) / 2,
        cost: (selectedProvider.metrics?.cost || 0) + providerResponse.cost,
      });

      // Record request history
      this.requestHistory.push({
        timestamp: new Date().toISOString(),
        request,
        response: aiResponse,
      });

      // Keep only last 1000 requests
      if (this.requestHistory.length > 1000) {
        this.requestHistory = this.requestHistory.slice(-1000);
      }

      this.emitEvent("ai_response", {
        providerId: selectedProvider.id,
        provider: selectedProvider.name,
        request,
        response: aiResponse,
        success: true,
        responseTime,
      });

      return aiResponse;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      // Record failed request
      this.requestHistory.push({
        timestamp: new Date().toISOString(),
        request,
        error: errorMessage,
      });

      this.emitEvent("ai_error", {
        providerId: selectedProvider?.id,
        provider: selectedProvider?.name,
        request,
        error: errorMessage,
        responseTime,
      });

      throw error;
    }
  }

  private updateProviderMetrics(
    providerId: string,
    updates: Partial<AIProvider["metrics"]>
  ): void {
    const provider = this.providers.get(providerId);
    if (!provider) {
      return;
    }

    const updatedProvider = {
      ...provider,
      metrics: {
        ...provider.metrics,
        ...updates,
      },
    };

    this.providers.set(providerId, updatedProvider);

    this.emitEvent("usage_metric", {
      providerId,
      metrics: updatedProvider.metrics,
    });
  }

  private getProviderCapabilities(type: string) {
    const capabilityMap = {
      openai: {
        text: true,
        image: true,
        code: true,
        analysis: true,
        creative: true,
        streaming: true,
        multimodal: true,
      },
      anthropic: {
        text: true,
        image: true,
        code: true,
        analysis: true,
        creative: true,
        streaming: true,
        multimodal: true,
      },
      google: {
        text: true,
        image: true,
        code: true,
        analysis: true,
        creative: true,
        streaming: false,
        multimodal: true,
      },
      groq: {
        text: true,
        image: false,
        code: true,
        analysis: true,
        creative: true,
        streaming: true,
        multimodal: false,
      },
      mistral: {
        text: true,
        image: false,
        code: true,
        analysis: true,
        creative: true,
        streaming: true,
        multimodal: false,
      },
    };

    return (
      capabilityMap[type as keyof typeof capabilityMap] || {
        text: true,
        image: false,
        code: false,
        analysis: false,
        creative: false,
        streaming: false,
        multimodal: false,
      }
    );
  }

  private getCostPerToken(type: string): number {
    const costMap = {
      openai: 0.00003,
      anthropic: 0.000015,
      google: 0.0000125,
      groq: 0.000001,
      mistral: 0.000002,
    };

    return costMap[type as keyof typeof costMap] || 0.00001;
  }

  getStatus(): {
    initialized: boolean;
    providersCount: number;
    subscriptionsCount: number;
    requestHistory: number;
    activeProviders: number;
  } {
    return {
      initialized: this.isInitialized,
      providersCount: this.providers.size,
      subscriptionsCount: this.eventSubscriptions.size,
      requestHistory: this.requestHistory.length,
      activeProviders: Array.from(this.providers.values()).filter(
        (p) => p.status === "active"
      ).length,
    };
  }

  getMetrics(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalCost: number;
    averageResponseTime: number;
    providerUsage: Record<string, number>;
  } {
    const totalRequests = this.requestHistory.length;
    const successfulRequests = this.requestHistory.filter(
      (r) => r.response
    ).length;
    const failedRequests = this.requestHistory.filter((r) => r.error).length;
    const totalCost = this.requestHistory
      .filter((r) => r.response)
      .reduce((sum, r) => sum + (r.response?.cost || 0), 0);
    const averageResponseTime =
      this.requestHistory
        .filter((r) => r.response)
        .reduce((sum, r) => sum + (r.response?.responseTime || 0), 0) /
        successfulRequests || 0;

    const providerUsage: Record<string, number> = {};
    Array.from(this.providers.values()).forEach((provider) => {
      providerUsage[provider.name] = provider.metrics?.requests || 0;
    });

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      totalCost,
      averageResponseTime,
      providerUsage,
    };
  }

  getRequestHistory(limit?: number): typeof this.requestHistory {
    return limit ? this.requestHistory.slice(-limit) : [...this.requestHistory];
  }

  clearHistory(): void {
    this.requestHistory = [];
    this.emitEvent("history_cleared", {
      timestamp: new Date().toISOString(),
    });
  }

  cleanup(): void {
    this.eventSubscriptions.clear();
    this.requestHistory = [];

    if (this.config.enableLogging) {
      console.log("🧹 SynapseAI SDK cleaned up");
    }
  }
}

// Global SDK instance
let sdkInstance: SynapseAISDK | null = null;

export function initializeSynapseAI(
  config?: SynapseAIConfig
): Promise<SynapseAISDK> {
  if (!sdkInstance) {
    sdkInstance = new SynapseAISDK(config);
  }
  return sdkInstance.initialize().then(() => sdkInstance!);
}

export function getSynapseAI(): SynapseAISDK {
  if (!sdkInstance) {
    throw new Error(
      "SynapseAI SDK not initialized. Call initializeSynapseAI() first."
    );
  }
  return sdkInstance;
}

export { SynapseAISDK };
